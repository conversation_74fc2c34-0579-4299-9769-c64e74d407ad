[22:27:52] 🎉 应用启动成功，等待导入Excel文件...
[22:27:53] ✅ F8全局热键已注册
[22:27:53] 正在检测连接的MTP设备...
[22:27:53] 检查设备: 下载
[22:27:53] 检查设备: 3D 对象
[22:27:53] 检查设备: 图片
[22:27:53] 检查设备: 音乐
[22:27:53] 检查设备: 桌面
[22:27:53] 检查设备: 文档
[22:27:53] 检查设备: 视频
[22:27:53] 检查设备: iQOO Z1
[22:27:53] 发现可能的手机设备: iQOO Z1
[22:27:53] ✅ 在设备 'iQOO Z1' 中找到目标路径: 内部存储设备\DCIM\Pindd\goods
[22:27:53] ✅ 检测到MTP设备: iQOO Z1
[22:27:57] 正在读取文件: 若花淑.xlsx
[22:27:57] Excel文件读取成功
[22:27:57] 📊 已读取8个商品链接
[22:27:57] ✅ Excel文件已复制到: 商品图片\商品SKU信息.xlsx
[22:27:57] 📁 正在创建 8 个商品文件夹...
[22:27:57] ✅ 成功创建商品文件夹: 1-8
[22:27:57] 📦 已加载 8 个商品，可选择性执行
[22:27:57] 🔗 链接预览:
[22:27:57]   1. https://mobile.yangkeduo.com/goods.html?ps=SNC9mXfixy
[22:27:57]   2. https://mobile.yangkeduo.com/goods1.html?ps=A2IzYy2B3q
[22:27:57]   3. https://mobile.yangkeduo.com/goods1.html?ps=JTaVru9i0B
[22:27:57]   ... 还有 5 个链接
[22:27:59] === 准备开始自动化操作 ===
[22:27:59] 请确保投屏软件已打开并处于可操作状态
[22:27:59] 5秒后开始执行操作...
[22:27:59] 倒计时: 5 秒
[22:28:00] 倒计时: 4 秒
[22:28:01] 倒计时: 3 秒
[22:28:02] 倒计时: 2 秒
[22:28:03] 倒计时: 1 秒
[22:28:04] === 开始自动化操作 ===
[22:28:04] 🚀 准备处理 8 个商品链接
[22:28:04] 📱 检测连接设备品牌...
[22:28:04] 🔍 开始检测连接设备品牌...
[22:28:04] 正在检测连接的MTP设备...
[22:28:04] 检查设备: 下载
[22:28:04] 检查设备: 3D 对象
[22:28:04] 检查设备: 图片
[22:28:04] 检查设备: 音乐
[22:28:04] 检查设备: 桌面
[22:28:04] 检查设备: 文档
[22:28:04] 检查设备: 视频
[22:28:04] 检查设备: iQOO Z1
[22:28:04] 发现可能的手机设备: iQOO Z1
[22:28:04] ✅ 在设备 'iQOO Z1' 中找到目标路径: 内部存储设备\DCIM\Pindd\goods
[22:28:04] 检测到设备: iQOO Z1
[22:28:04] ✅ 检测到IQOO品牌设备，使用图片文件夹: image\IQOO
[22:28:04] ✅ 设备品牌检测完成，当前图片文件夹: image\IQOO
[22:28:04] 📱 当前设备: iQOO Z1
[22:28:04] 
============================================================
[22:28:04] 🎯 开始处理第 1 个商品
[22:28:04] 商品链接: https://mobile.yangkeduo.com/goods.html?ps=SNC9mXfixy
[22:28:04] ============================================================
[22:28:04] 步骤1: 设置剪贴板内容...
[22:28:04] 已复制商品链接到剪贴板: https://mobile.yangkeduo.com/goods.html?ps=SNC9mXfixy
[22:28:04] ✅ 剪贴板内容验证成功
[22:28:04] 步骤2: 点击搜索框并输入链接...
[22:28:04] 移动到位置(480,96)并点击...
[22:28:05] 按下Ctrl+A全选...
[22:28:05] 执行AutoHotkey脚本: ctrl_a.ahk
[22:28:06] ✅ AutoHotkey脚本执行成功: ctrl_a
[22:28:07] 粘贴商品链接...
[22:28:07] 使用外部脚本软件执行真正的Ctrl+V...
[22:28:07] Python的键盘模拟无法被escrcpy识别，尝试其他方案
[22:28:07] 方法1: 尝试AutoHotkey...
[22:28:07] 找到脚本文件: paste_v2.ahk
[22:28:07] 检查路径: AutoHotkey.exe
[22:28:07] 检查路径: C:\Program Files\AutoHotkey\AutoHotkey.exe
[22:28:07] 检查路径: C:\Program Files (x86)\AutoHotkey\AutoHotkey.exe
[22:28:07] 检查路径: C:\Program Files\AutoHotkey\v2\AutoHotkey.exe
[22:28:07] 找到AutoHotkey: C:\Program Files\AutoHotkey\v2\AutoHotkey.exe
[22:28:07] 执行AutoHotkey脚本: paste_v2.ahk
[22:28:08] AutoHotkey返回码: 0
[22:28:08] ✅ AutoHotkey执行成功
[22:28:08] ✅ Ctrl+V操作执行成功
[22:28:11] 步骤3: 点击搜索按钮...
[22:28:11] 正在查找图片: SOUSUO2.png (路径: image\IQOO\SOUSUO2.png)
[22:28:11] 找到图片 SOUSUO2.png，位置: (641, 81)
[22:28:11] 点击位置: (664, 96)
[22:28:12] 已点击图片: SOUSUO2.png
[22:28:12] 步骤4: 等待3秒进入商品页面...
[22:28:15] 步骤5: 执行从右往左的滑动操作...
[22:28:15] 开始执行从右往左的滑动操作...
[22:28:15] 📍 起始位置: (630, 185)
[22:28:15] 📍 结束位置: (284, 185)
[22:28:15] 📏 滑动距离: 346 像素
[22:28:15] ⏱️ 滑动时长: 0.3 秒
[22:28:15] 移动到起始位置(630, 185)
[22:28:15] 按住左键从(630, 185)拖拽到(284, 185)
[22:28:16] 开始滑动，时长0.3秒...
[22:28:16] 释放左键，滑动操作完成
[22:28:17] ✅ 从右往左的滑动操作完成
[22:28:17] 步骤6: 移动到位置(470,185)...
[22:28:17] 已移动到位置(470,185)
[22:28:17] 步骤7: 执行鼠标点击和长按操作...
[22:28:17] 点击鼠标左键一次
[22:28:18] 在位置(475, 323)长按鼠标左键0.5秒
[22:28:19] 鼠标操作完成
[22:28:20] 步骤8: 查找并点击保存按钮...
[22:28:20] 尝试查找图片 BAOCUN1.png (第1次/共3次)
[22:28:20] 正在查找图片: BAOCUN1.png (路径: image\IQOO\BAOCUN1.png)
[22:28:20] 找到图片 BAOCUN1.png，位置: (450, 808)
[22:28:20] 移动到图片上，等待0.5秒...
[22:28:21] 已点击图片: BAOCUN1.png
[22:28:21] 步骤9: 检测保存状态...
[22:28:21] 开始检测图片: BAOCUNZHONG.png (路径: image\IQOO\BAOCUNZHONG.png)
[22:28:21] 最多检测 80 次，每隔 0.2 秒检测一次
[22:28:21] 第 1/80 次检测...
[22:28:21] 第 2/80 次检测...
[22:28:22] 第 3/80 次检测...
[22:28:22] ✅ 找到图片 BAOCUNZHONG.png！位置: (443, 507)
[22:28:22] ✅ 检测到保存中状态，等待保存完成...
[22:28:22] 等待5秒让图片完全保存到手机...
[22:28:27] 步骤10: 复制手机图片到商品 1 文件夹...
[22:28:27] 第 1/3 次尝试复制图片...
[22:28:27] 开始从MTP设备复制图片到商品 1 文件夹...
[22:28:27] 正在使用Windows Shell API查找MTP设备...
[22:28:27] 找到设备: iQOO Z1
[22:28:27] 进入文件夹: 内部存储设备
[22:28:27] 进入文件夹: DCIM
[22:28:27] 进入文件夹: Pindd
[22:28:27] 进入文件夹: goods
[22:28:27] 成功找到目标文件夹: 内部存储设备\DCIM\Pindd\goods
[22:28:27] 目标路径: F:\自动捕获数据\商品图片\1
[22:28:27] 复制文件: 1753626501373-1315797334.jpg
[22:28:27] ✅ 成功复制: 1753626501373-1315797334.jpg
[22:28:27] 复制文件: 1753626501484-1645306700.jpg
[22:28:27] ✅ 成功复制: 1753626501484-1645306700.jpg
[22:28:27] 复制文件: 1753626501256--945688330.jpg
[22:28:27] ✅ 成功复制: 1753626501256--945688330.jpg
[22:28:27] 复制文件: 1753626501297-483977703.jpg
[22:28:27] ✅ 成功复制: 1753626501297-483977703.jpg
[22:28:27] 复制文件: 1753626501401--1182398115.jpg
[22:28:27] ✅ 成功复制: 1753626501401--1182398115.jpg
[22:28:27] 复制文件: 1753626501042--850234244.jpg
[22:28:27] ✅ 成功复制: 1753626501042--850234244.jpg
[22:28:27] 复制文件: 1753626500939-199319035.jpg
[22:28:27] ✅ 成功复制: 1753626500939-199319035.jpg
[22:28:27] 复制文件: 1753626501073--278957689.jpg
[22:28:27] ✅ 成功复制: 1753626501073--278957689.jpg
[22:28:27] 复制文件: 1753626501332-1706905320.jpg
[22:28:28] ✅ 成功复制: 1753626501332-1706905320.jpg
[22:28:28] 复制文件: 1753626501176--1680485554.jpg
[22:28:28] ✅ 成功复制: 1753626501176--1680485554.jpg
[22:28:28] 成功复制 10 个文件
[22:28:28] ✅ 成功复制 10 个图片到商品 1 文件夹
[22:28:28] 复制的文件:
[22:28:28]   - 1753626501373-1315797334.jpg
[22:28:28]   - 1753626501484-1645306700.jpg
[22:28:28]   - 1753626501256--945688330.jpg
[22:28:28]   - 1753626501297-483977703.jpg
[22:28:28]   - 1753626501401--1182398115.jpg
[22:28:28]   - 1753626501042--850234244.jpg
[22:28:28]   - 1753626500939-199319035.jpg
[22:28:28]   - 1753626501073--278957689.jpg
[22:28:28]   - 1753626501332-1706905320.jpg
[22:28:28]   - 1753626501176--1680485554.jpg
[22:28:28] ⚠️  注意：由于MTP协议限制，无法自动删除手机上的原文件
[22:28:28] 请手动删除手机 DCIM/Pindd/goods 文件夹中的图片以释放空间
[22:28:28] ✅ 图片复制完成
[22:28:28] 步骤11: 删除手机上的原文件...
[22:28:28] 开始删除手机文件操作...
[22:28:28] 查找并点击GOODS.png...
[22:28:28] 正在查找图片: GOODS.png (路径: image\IQOO\GOODS.png)
[22:28:28] 查找或点击图片 GOODS.png 时出错: 
[22:28:28] ❌ 未找到GOODS.png图片
[22:28:28] ⚠️ 手机文件删除失败，请手动删除
[22:28:28] 步骤12: 开始处理主图...
[22:28:28] 步骤13: 开始第二轮操作（详情页）...
[22:28:28] 处理主图文件夹: 商品图片\1
[22:28:28] === 开始详情页图片捕获流程 ===
[22:28:28] 找到 10 张图片
[22:28:28] 步骤13.1: 移动到(475,230)并点击右键...
[22:28:28] 删除多余图片: 1753626501401--1182398115.jpg
[22:28:28] 删除多余图片: 1753626501042--850234244.jpg
[22:28:28] 删除多余图片: 1753626500939-199319035.jpg
[22:28:28] 删除多余图片: 1753626501073--278957689.jpg
[22:28:28] 删除多余图片: 1753626501332-1706905320.jpg
[22:28:28] 删除多余图片: 1753626501176--1680485554.jpg
[22:28:28] 重命名: 1753626501373-1315797334.jpg → 主图1.jpg
[22:28:28] 重命名: 1753626501484-1645306700.jpg → 主图2.jpg
[22:28:28] 重命名: 1753626501256--945688330.jpg → 主图3.jpg
[22:28:28] 重命名: 1753626501297-483977703.jpg → 主图4.jpg
[22:28:28] ✅ 主图处理完成
[22:28:30] 步骤13.2: 使用AutoHotkey向下滚动40次...
[22:28:30] 执行AutoHotkey脚本: scroll_down.ahk
[22:28:34] ✅ AutoHotkey脚本执行成功: scroll_down
[22:28:34] ✅ AutoHotkey滚动执行成功
[22:28:34] 步骤13.3: 移动到(477,300)并点击左键...
[22:28:35] ✅ 点击操作完成
[22:28:35] 步骤13.4: 移动到(480,495)并长按0.5秒...
[22:28:36] ✅ 长按操作完成
[22:28:37] 步骤13.5: 查找并点击保存按钮...
[22:28:37] 尝试查找图片 BAOCUN1.png (第1次/共3次)
[22:28:37] 正在查找图片: BAOCUN1.png (路径: image\IQOO\BAOCUN1.png)
[22:28:37] 找到图片 BAOCUN1.png，位置: (451, 808)
[22:28:37] 移动到图片上，等待0.5秒...
[22:28:38] 已点击图片: BAOCUN1.png
[22:28:38] 步骤13.6: 检测详情页保存状态...
[22:28:38] 开始检测图片: BAOCUNZHONG.png (路径: image\IQOO\BAOCUNZHONG.png)
[22:28:38] 最多检测 80 次，每隔 0.2 秒检测一次
[22:28:38] 第 1/80 次检测...
[22:28:38] 第 2/80 次检测...
[22:28:39] 第 3/80 次检测...
[22:28:39] ✅ 找到图片 BAOCUNZHONG.png！位置: (443, 511)
[22:28:39] ✅ 检测到详情页保存中状态，等待保存完成...
[22:28:44] 步骤13.7: 复制详情页图片...
[22:28:44] 第 1/3 次尝试复制详情页图片...
[22:28:44] 开始从MTP设备复制图片到商品 1 文件夹...
[22:28:44] 正在使用Windows Shell API查找MTP设备...
[22:28:44] 找到设备: iQOO Z1
[22:28:44] 进入文件夹: 内部存储设备
[22:28:44] 进入文件夹: DCIM
[22:28:44] 进入文件夹: Pindd
[22:28:44] 进入文件夹: goods
[22:28:44] 成功找到目标文件夹: 内部存储设备\DCIM\Pindd\goods
[22:28:44] 目标路径: F:\自动捕获数据\商品图片\1
[22:28:44] 复制文件: 1753626501373-1315797334.jpg
[22:28:44] ✅ 成功复制: 1753626501373-1315797334.jpg
[22:28:44] 复制文件: 1753626501484-1645306700.jpg
[22:28:44] ✅ 成功复制: 1753626501484-1645306700.jpg
[22:28:44] 复制文件: 1753626501256--945688330.jpg
[22:28:44] ✅ 成功复制: 1753626501256--945688330.jpg
[22:28:44] 复制文件: 1753626501297-483977703.jpg
[22:28:44] ✅ 成功复制: 1753626501297-483977703.jpg
[22:28:44] 复制文件: 1753626501401--1182398115.jpg
[22:28:44] ✅ 成功复制: 1753626501401--1182398115.jpg
[22:28:44] 复制文件: 1753626501042--850234244.jpg
[22:28:44] ✅ 成功复制: 1753626501042--850234244.jpg
[22:28:44] 复制文件: 1753626500939-199319035.jpg
[22:28:45] ✅ 成功复制: 1753626500939-199319035.jpg
[22:28:45] 复制文件: 1753626501073--278957689.jpg
[22:28:45] ✅ 成功复制: 1753626501073--278957689.jpg
[22:28:45] 复制文件: 1753626501332-1706905320.jpg
[22:28:45] ✅ 成功复制: 1753626501332-1706905320.jpg
[22:28:45] 复制文件: 1753626501176--1680485554.jpg
[22:28:45] ✅ 成功复制: 1753626501176--1680485554.jpg
[22:28:45] 复制文件: 1753626501138--1193025293.jpg
[22:28:45] ✅ 成功复制: 1753626501138--1193025293.jpg
[22:28:45] 复制文件: 1753626501207--778938302.jpg
[22:28:45] ✅ 成功复制: 1753626501207--778938302.jpg
[22:28:45] 复制文件: 1753626501435-1066972225.jpg
[22:28:45] ✅ 成功复制: 1753626501435-1066972225.jpg
[22:28:45] 复制文件: 1753626518152-660672411.jpg
[22:28:45] ✅ 成功复制: 1753626518152-660672411.jpg
[22:28:45] 复制文件: 1753626518186-2125388435.jpg
[22:28:45] ✅ 成功复制: 1753626518186-2125388435.jpg
[22:28:45] 复制文件: 1753626518219-1804250823.jpg
[22:28:45] ✅ 成功复制: 1753626518219-1804250823.jpg
[22:28:45] 复制文件: 1753626518249--1065563707.jpg
[22:28:45] ✅ 成功复制: 1753626518249--1065563707.jpg
[22:28:45] 复制文件: 1753626518295-152127411.jpg
[22:28:45] ✅ 成功复制: 1753626518295-152127411.jpg
[22:28:45] 复制文件: 1753626518357-1552964844.jpg
[22:28:45] ✅ 成功复制: 1753626518357-1552964844.jpg
[22:28:45] 复制文件: 1753626518406-1752797653.jpg
[22:28:45] ✅ 成功复制: 1753626518406-1752797653.jpg
[22:28:45] 复制文件: 1753626518441--1753262296.jpg
[22:28:46] ✅ 成功复制: 1753626518441--1753262296.jpg
[22:28:46] 复制文件: 1753626518489-842254062.jpg
[22:28:46] ✅ 成功复制: 1753626518489-842254062.jpg
[22:28:46] 复制文件: 1753626518530--255483989.jpg
[22:28:46] ✅ 成功复制: 1753626518530--255483989.jpg
[22:28:46] 复制文件: 1753626518571--1285728568.jpg
[22:28:46] ✅ 成功复制: 1753626518571--1285728568.jpg
[22:28:46] 复制文件: 1753626518627-396311468.jpg
[22:28:46] ✅ 成功复制: 1753626518627-396311468.jpg
[22:28:46] 复制文件: 1753626518662-1252897153.jpg
[22:28:46] ✅ 成功复制: 1753626518662-1252897153.jpg
[22:28:46] 复制文件: 1753626518696--1537897804.jpg
[22:28:46] ✅ 成功复制: 1753626518696--1537897804.jpg
[22:28:46] 复制文件: 1753626518746--1146868097.jpg
[22:28:46] ✅ 成功复制: 1753626518746--1146868097.jpg
[22:28:46] 复制文件: 1753626518780--1712757656.jpg
[22:28:46] ✅ 成功复制: 1753626518780--1712757656.jpg
[22:28:46] 复制文件: 1753626518827-184866696.jpg
[22:28:46] ✅ 成功复制: 1753626518827-184866696.jpg
[22:28:46] 成功复制 30 个文件
[22:28:46] ✅ 成功复制 30 个图片到商品 1 文件夹
[22:28:46] 复制的文件:
[22:28:46]   - 1753626501373-1315797334.jpg
[22:28:46]   - 1753626501484-1645306700.jpg
[22:28:46]   - 1753626501256--945688330.jpg
[22:28:46]   - 1753626501297-483977703.jpg
[22:28:46]   - 1753626501401--1182398115.jpg
[22:28:46]   - 1753626501042--850234244.jpg
[22:28:46]   - 1753626500939-199319035.jpg
[22:28:46]   - 1753626501073--278957689.jpg
[22:28:46]   - 1753626501332-1706905320.jpg
[22:28:46]   - 1753626501176--1680485554.jpg
[22:28:46]   - 1753626501138--1193025293.jpg
[22:28:46]   - 1753626501207--778938302.jpg
[22:28:46]   - 1753626501435-1066972225.jpg
[22:28:46]   - 1753626518152-660672411.jpg
[22:28:46]   - 1753626518186-2125388435.jpg
[22:28:46]   - 1753626518219-1804250823.jpg
[22:28:46]   - 1753626518249--1065563707.jpg
[22:28:46]   - 1753626518295-152127411.jpg
[22:28:46]   - 1753626518357-1552964844.jpg
[22:28:46]   - 1753626518406-1752797653.jpg
[22:28:46]   - 1753626518441--1753262296.jpg
[22:28:46]   - 1753626518489-842254062.jpg
[22:28:46]   - 1753626518530--255483989.jpg
[22:28:46]   - 1753626518571--1285728568.jpg
[22:28:46]   - 1753626518627-396311468.jpg
[22:28:46]   - 1753626518662-1252897153.jpg
[22:28:46]   - 1753626518696--1537897804.jpg
[22:28:46]   - 1753626518746--1146868097.jpg
[22:28:46]   - 1753626518780--1712757656.jpg
[22:28:46]   - 1753626518827-184866696.jpg
[22:28:46] ⚠️  注意：由于MTP协议限制，无法自动删除手机上的原文件
[22:28:46] 请手动删除手机 DCIM/Pindd/goods 文件夹中的图片以释放空间
[22:28:46] ✅ 详情页图片复制完成
[22:28:46] 步骤13.8: 删除手机上的详情页图片...
[22:28:46] 开始删除手机文件操作...
[22:28:46] 查找并点击GOODS.png...
[22:28:46] 正在查找图片: GOODS.png (路径: image\IQOO\GOODS.png)
[22:28:46] 查找或点击图片 GOODS.png 时出错: 
[22:28:46] ❌ 未找到GOODS.png图片
[22:28:46] ⚠️ 手机详情页文件删除失败，请手动删除
[22:28:46] 步骤13.8: 处理详情页图片...
[22:28:46] 处理详情页图片文件夹: 商品图片\1
[22:28:46] 跳过已处理的主图: 主图1.jpg
[22:28:46] 跳过已处理的主图: 主图2.jpg
[22:28:46] 跳过已处理的主图: 主图3.jpg
[22:28:46] 跳过已处理的主图: 主图4.jpg
[22:28:46] 找到 30 张详情页图片
[22:28:46] 重命名详情页图片: 1753626501373-1315797334.jpg → 1.jpg
[22:28:46] 重命名详情页图片: 1753626501484-1645306700.jpg → 2.jpg
[22:28:46] 重命名详情页图片: 1753626501256--945688330.jpg → 3.jpg
[22:28:46] 重命名详情页图片: 1753626501297-483977703.jpg → 4.jpg
[22:28:46] 重命名详情页图片: 1753626501401--1182398115.jpg → 5.jpg
[22:28:46] 重命名详情页图片: 1753626501042--850234244.jpg → 6.jpg
[22:28:46] 重命名详情页图片: 1753626500939-199319035.jpg → 7.jpg
[22:28:46] 重命名详情页图片: 1753626501073--278957689.jpg → 8.jpg
[22:28:46] 重命名详情页图片: 1753626501332-1706905320.jpg → 9.jpg
[22:28:46] 重命名详情页图片: 1753626501176--1680485554.jpg → 10.jpg
[22:28:46] 重命名详情页图片: 1753626501138--1193025293.jpg → 11.jpg
[22:28:46] 重命名详情页图片: 1753626501207--778938302.jpg → 12.jpg
[22:28:46] 重命名详情页图片: 1753626501435-1066972225.jpg → 13.jpg
[22:28:46] 重命名详情页图片: 1753626518152-660672411.jpg → 14.jpg
[22:28:46] 重命名详情页图片: 1753626518186-2125388435.jpg → 15.jpg
[22:28:46] 重命名详情页图片: 1753626518219-1804250823.jpg → 16.jpg
[22:28:46] 重命名详情页图片: 1753626518249--1065563707.jpg → 17.jpg
[22:28:46] 重命名详情页图片: 1753626518295-152127411.jpg → 18.jpg
[22:28:46] 重命名详情页图片: 1753626518357-1552964844.jpg → 19.jpg
[22:28:46] 重命名详情页图片: 1753626518406-1752797653.jpg → 20.jpg
[22:28:46] 重命名详情页图片: 1753626518441--1753262296.jpg → 21.jpg
[22:28:46] 重命名详情页图片: 1753626518489-842254062.jpg → 22.jpg
[22:28:46] 重命名详情页图片: 1753626518530--255483989.jpg → 23.jpg
[22:28:46] 重命名详情页图片: 1753626518571--1285728568.jpg → 24.jpg
[22:28:46] 重命名详情页图片: 1753626518627-396311468.jpg → 25.jpg
[22:28:46] 重命名详情页图片: 1753626518662-1252897153.jpg → 26.jpg
[22:28:46] 重命名详情页图片: 1753626518696--1537897804.jpg → 27.jpg
[22:28:46] 重命名详情页图片: 1753626518746--1146868097.jpg → 28.jpg
[22:28:46] 重命名详情页图片: 1753626518780--1712757656.jpg → 29.jpg
[22:28:46] 重命名详情页图片: 1753626518827-184866696.jpg → 30.jpg
[22:28:46] ✅ 详情页图片处理完成
[22:28:46] ✅ 主图保持'主图X'格式，详情页图片重命名为1-30
[22:28:46] ✅ 详情页图片处理完成
[22:28:46] 步骤13.9: 执行最后的鼠标操作...
[22:28:46] 开始执行最后的鼠标操作序列...
[22:28:46] 移动到位置(475,125)...
[22:28:47] 点击右键...
[22:28:48] 等待1秒...
[22:28:49] 移动到位置(670,940)...
[22:28:49] 点击左键...
[22:28:49] 等待2.5秒让页面加载完成...
[22:28:52] ✅ 最后的鼠标操作序列完成
[22:28:52] 步骤13.10: 执行智能OCR分析...
[22:28:52] ==================================================
[22:28:52] 🧠 智能OCR分析开始
[22:28:52] ==================================================
[22:28:52] 🔍 开始智能OCR分析屏幕区域: (270, 230) 到 (691, 883)
[22:28:52] ✅ OCR截图已保存: 商品图片\1.jpg
[22:28:59] 正在执行OCR识别...
[22:29:01] OCR原始结果类型: <class 'list'>
[22:29:01] OCR原始结果长度: 1
[22:29:01] 第一页结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[22:29:01] OCRResult对象属性: ['_save_funcs', '_json_writer', '_rand_fn', '_img_writer']
[22:29:01]   _save_funcs: <class 'list'> - [<bound method JsonMixin.save_to_json of {'input_path': None, 'page_index': None, 'doc_preprocessor_...
[22:29:01]   _json_writer: <class 'paddlex.inference.utils.io.writers.JsonWriter'> - <paddlex.inference.utils.io.writers.JsonWriter object at 0x00000200E6DC04D0>
[22:29:01]   _rand_fn: <class 'NoneType'> - None
[22:29:01]   _img_writer: <class 'paddlex.inference.utils.io.writers.ImageWriter'> - <paddlex.inference.utils.io.writers.ImageWriter object at 0x00000200E6C453D0>
[22:29:01] 方式2成功：识别到 19 个文本
[22:29:01] 文本 0: '快要卖完￥39.9￥69.9', 置信度: 0.9715
[22:29:01] OCR识别: 快要卖完￥39.9￥69.9 (置信度: 0.9715)
[22:29:01] 文本 1: '1', 置信度: 0.9909
[22:29:01] OCR识别: 1 (置信度: 0.9909)
[22:29:01] 文本 2: '已选：裤子120码适合身高115字谱', 置信度: 0.9976
[22:29:02] OCR识别: 已选：裤子120码适合身高115字谱 (置信度: 0.9976)
[22:29:02] 文本 3: '颜色', 置信度: 0.9997
[22:29:02] OCR识别: 颜色 (置信度: 0.9997)
[22:29:02] 文本 4: '套装', 置信度: 0.9990
[22:29:02] OCR识别: 套装 (置信度: 0.9990)
[22:29:02] 文本 5: 'T恤', 置信度: 0.9722
[22:29:02] OCR识别: T恤 (置信度: 0.9722)
[22:29:02] 文本 6: '裤子', 置信度: 0.9999
[22:29:02] OCR识别: 裤子 (置信度: 0.9999)
[22:29:02] 文本 7: '尺码', 置信度: 0.9992
[22:29:02] OCR识别: 尺码 (置信度: 0.9992)
[22:29:02] 文本 8: '90码适合身高85左右', 置信度: 0.9981
[22:29:02] OCR识别: 90码适合身高85左右 (置信度: 0.9981)
[22:29:02] 文本 9: '100码适合身高95左右', 置信度: 0.9939
[22:29:02] OCR识别: 100码适合身高95左右 (置信度: 0.9939)
[22:29:02] 文本 10: '110码适合身高105左右', 置信度: 0.9994
[22:29:02] OCR识别: 110码适合身高105左右 (置信度: 0.9994)
[22:29:02] 文本 11: '120码适合身高115字谱', 置信度: 0.9991
[22:29:02] OCR识别: 120码适合身高115字谱 (置信度: 0.9991)
[22:29:02] 文本 12: '130码适合身高125左右', 置信度: 0.9989
[22:29:02] OCR识别: 130码适合身高125左右 (置信度: 0.9989)
[22:29:02] 文本 13: '140码适合身高135左右', 置信度: 0.9983
[22:29:02] OCR识别: 140码适合身高135左右 (置信度: 0.9983)
[22:29:02] 文本 14: '150码适合身高145左右', 置信度: 0.9929
[22:29:02] OCR识别: 150码适合身高145左右 (置信度: 0.9929)
[22:29:02] 文本 15: '10（店内款随意发）', 置信度: 0.9947
[22:29:02] OCR识别: 10（店内款随意发） (置信度: 0.9947)
[22:29:02] 文本 16: '免费服务', 置信度: 0.9999
[22:29:02] OCR识别: 免费服务 (置信度: 0.9999)
[22:29:02] 文本 17: '退货包运费（商家赠送）', 置信度: 0.9414
[22:29:02] OCR识别: 退货包运费（商家赠送） (置信度: 0.9414)
[22:29:02] 文本 18: '一次选多款，不满意可退货包运费>', 置信度: 0.9778
[22:29:02] OCR识别: 一次选多款，不满意可退货包运费> (置信度: 0.9778)
[22:29:02] ✅ OCR识别完成，共识别到 19 个文本
[22:29:02] 💾 保存OCR结果到文件...
[22:29:02] ✅ OCR结果已保存: OCR\商品1_OCR结果.txt
[22:29:02] 🧠 开始智能分析商品信息...
[22:29:02] 🔧 开始智能拼接被分割的文本...
[22:29:02] 🎯 找到上方紧贴文本:
[22:29:02]    上方文本: '尺码' Y范围: 289.0-313.0, X范围: 3.0-43.0
[22:29:02]    当前文本: '90码适合身高85左右' Y范围: 324.0-346.0, X起始: 9.0
[22:29:02]    垂直间距: 11.0px, X重叠: True
[22:29:02] ✅ 向上拼接: '尺码' + '90码适合身高85左右' = '尺码90码适合身高85左右'
[22:29:02] 🔗 完成向上拼接:
[22:29:02]    结果: '尺码90码适合身高85左右'
[22:29:02] 🎯 找到上方紧贴文本:
[22:29:02]    上方文本: '免费服务' Y范围: 496.0-518.0, X范围: 3.0-72.0
[22:29:02]    当前文本: '退货包运费（商家赠送）' Y范围: 532.0-553.0, X起始: 28.0
[22:29:02]    垂直间距: 14.0px, X重叠: True
[22:29:02] ✅ 向上拼接: '免费服务' + '退货包运费（商家赠送）' = '免费服务退货包运费（商家赠送）'
[22:29:02] 🔗 完成向上拼接:
[22:29:02]    结果: '免费服务退货包运费（商家赠送）'
[22:29:02] 📊 拼接结果: 原始19个文本 → 拼接后17个文本
[22:29:02] 📝 拼接后的文本列表:
[22:29:02]    1. '快要卖完￥39.9￥69.9'
[22:29:02]    2. '1'
[22:29:02]    3. '已选：裤子120码适合身高115字谱'
[22:29:02]    4. '颜色'
[22:29:02]    5. '套装'
[22:29:02]    6. 'T恤'
[22:29:02]    7. '裤子'
[22:29:02]    8. '尺码90码适合身高85左右'
[22:29:02]    9. '100码适合身高95左右'
[22:29:02]    10. '110码适合身高105左右'
[22:29:02]    11. '120码适合身高115字谱'
[22:29:02]    12. '130码适合身高125左右'
[22:29:02]    13. '140码适合身高135左右'
[22:29:02]    14. '150码适合身高145左右'
[22:29:02]    15. '10（店内款随意发）'
[22:29:02]    16. '免费服务退货包运费（商家赠送）'
[22:29:02]    17. '一次选多款，不满意可退货包运费>'
[22:29:02] 🎯 找到尺码区域开始位置: 第8行 '尺码90码适合身高85左右'
[22:29:02] ✅ 提取尺码: 100 (来源: '100码适合身高95左右')
[22:29:02] 🔍 处理bbox: [172, 324, 323, 346] (长度: 4)
[22:29:02] ✅ 计算坐标成功: (247, 335)
[22:29:02] 📍 记录尺码坐标: 100 -> (247, 335)
[22:29:02] ✅ 提取尺码: 110 (来源: '110码适合身高105左右')
[22:29:02] 🔍 处理bbox: [9, 363, 166, 386] (长度: 4)
[22:29:02] ✅ 计算坐标成功: (87, 374)
[22:29:02] 📍 记录尺码坐标: 110 -> (87, 374)
[22:29:02] ✅ 提取尺码: 120 (来源: '120码适合身高115字谱')
[22:29:02] 🔍 处理bbox: [185, 364, 341, 386] (长度: 4)
[22:29:02] ✅ 计算坐标成功: (263, 375)
[22:29:02] 📍 记录尺码坐标: 120 -> (263, 375)
[22:29:02] ✅ 提取尺码: 130 (来源: '130码适合身高125左右')
[22:29:02] 🔍 处理bbox: [11, 406, 165, 424] (长度: 4)
[22:29:02] ✅ 计算坐标成功: (88, 415)
[22:29:02] 📍 记录尺码坐标: 130 -> (88, 415)
[22:29:02] ✅ 提取尺码: 140 (来源: '140码适合身高135左右')
[22:29:02] 🔍 处理bbox: [186, 406, 342, 424] (长度: 4)
[22:29:02] ✅ 计算坐标成功: (264, 415)
[22:29:02] 📍 记录尺码坐标: 140 -> (264, 415)
[22:29:02] ✅ 提取尺码: 150 (来源: '150码适合身高145左右')
[22:29:02] 🔍 处理bbox: [12, 447, 168, 465] (长度: 4)
[22:29:02] ✅ 计算坐标成功: (90, 456)
[22:29:02] 📍 记录尺码坐标: 150 -> (90, 456)
[22:29:02] ⚠️ 跳过无效尺码: 10 (来源: '10（店内款随意发）')
[22:29:02] 🛑 遇到停止关键词，结束尺码提取: '免费服务退货包运费（商家赠送）'
[22:29:02] 📊 尺码提取结果: 数字=[100, 110, 120, 130, 140, 150], 范围=100-150, 原始文本数量=6
[22:29:02] 尺码信息: {'optimized_range': '100-150', 'original_texts': ['100码适合身高95左右', '110码适合身高105左右', '120码适合身高115字谱', '130码适合身高125左右', '140码适合身高135左右', '150码适合身高145左右'], 'size_numbers': [100, 110, 120, 130, 140, 150]}
[22:29:02] 📍 已记录尺码坐标: {100: (247, 335), 110: (87, 374), 120: (263, 375), 130: (88, 415), 140: (264, 415), 150: (90, 456)}
[22:29:02] 🎯 找到颜色分类开始位置: 第3行 '颜色'
[22:29:02] 🎯 找到颜色分类结束位置: 第7行 '尺码90码适合身高85左右'
[22:29:02] 🔍 开始提取颜色分类: 从第4行到第6行
[22:29:02] ✅ 保留有效文本: '套装'
[22:29:02] ✅ 保留有效文本: 'T恤'
[22:29:02] ✅ 保留有效文本: '裤子'
[22:29:02] 🔍 检查颜色文本: '套装' (长度: 2)
[22:29:02] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [ 53 ... 259]
[22:29:02] 🔍 价格检测结果: False
[22:29:02] 🔍 处理bbox: [53, 236, 96, 259] (长度: 4)
[22:29:02] ✅ 计算坐标成功: (74, 247)
[22:29:02] 📍 记录颜色坐标: 套装 -> (74, 247)
[22:29:02] ✅ 提取到无价格颜色: 套装
[22:29:02] 🔍 检查颜色文本: 'T恤' (长度: 2)
[22:29:02] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [193 ... 258]
[22:29:02] 🔍 价格检测结果: False
[22:29:02] 🔍 处理bbox: [193, 237, 224, 258] (长度: 4)
[22:29:02] ✅ 计算坐标成功: (208, 247)
[22:29:02] 📍 记录颜色坐标: T恤 -> (208, 247)
[22:29:02] ✅ 提取到无价格颜色: T恤
[22:29:02] 🔍 检查颜色文本: '裤子' (长度: 2)
[22:29:02] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [329 ... 259]
[22:29:02] 🔍 价格检测结果: False
[22:29:02] 🔍 处理bbox: [329, 235, 366, 259] (长度: 4)
[22:29:02] ✅ 计算坐标成功: (347, 247)
[22:29:02] 📍 记录颜色坐标: 裤子 -> (347, 247)
[22:29:02] ✅ 提取到无价格颜色: 裤子
[22:29:02] 🎨 颜色分类提取完成: 共提取到 3 个颜色
[22:29:02] 📍 坐标记录完成: 共记录 3 个坐标
[22:29:02] 提取到颜色分类: [{'pure_name': '套装', 'original_text': '套装', 'has_direct_price': False, 'direct_price': None, 'bbox': array([ 53, ..., 259], dtype=int16)}, {'pure_name': 'T恤', 'original_text': 'T恤', 'has_direct_price': False, 'direct_price': None, 'bbox': array([193, ..., 258], dtype=int16)}, {'pure_name': '裤子', 'original_text': '裤子', 'has_direct_price': False, 'direct_price': None, 'bbox': array([329, ..., 259], dtype=int16)}]
[22:29:02] 颜色分类数量: 3
[22:29:02] 📍 已记录颜色坐标: {'套装': (74, 247), 'T恤': (208, 247), '裤子': (347, 247)}
[22:29:02] 🎯 从页面提取价格信息
[22:29:02] ✅ 页面通用价格: 39.9 (来源: 快要卖完￥39.9￥69.9)
[22:29:02] 🔧 不分配页面价格给颜色，使用交互式方案获取准确价格
[22:29:02] 🔍 商品类型分析:
[22:29:02]    颜色数量: 3
[22:29:02]    颜色直接带价格: False
[22:29:02]    页面有券前/券后价格: False
[22:29:02]    尺码带价格: False
[22:29:02] 📊 分析类型: type3_multiple_colors_no_prices
[22:29:02] 🔧 处理方案: interactive
[22:29:02] 📊 智能分析结果:
[22:29:02]   优化尺码范围: 100-150
[22:29:02]   原始尺码文本: ['100码适合身高95左右', '110码适合身高105左右', '120码适合身高115字谱', '130码适合身高125左右', '140码适合身高135左右', '150码适合身高145左右']
[22:29:02]   颜色分类: ['套装', 'T恤', '裤子']
[22:29:02]   颜色价格: {}
[22:29:02]   分析类型: type3_multiple_colors_no_prices
[22:29:02]   处理方案: interactive
[22:29:02] 🔄 切换到交互式价格获取方案
[22:29:02] 🚀 开始交互式价格获取...
[22:29:02] 需要交互获取价格的颜色: ['套装', 'T恤', '裤子']
[22:29:02] 📏 尺码选择策略: 从6个尺码中选择中间值 130
[22:29:02]    完整尺码列表: [100, 110, 120, 130, 140, 150]
[22:29:02]    避免最小码: 100，避免最大码: 150
[22:29:02] 选择中间尺码: 130
[22:29:02] 🔍 检查尺码坐标记录: {100: (247, 335), 110: (87, 374), 120: (263, 375), 130: (88, 415), 140: (264, 415), 150: (90, 456)}
[22:29:02] 🔍 查找尺码: 130
[22:29:02] 📍 使用记录的尺码坐标: 130
[22:29:02]    相对坐标: (88, 415)
[22:29:02]    绝对坐标: (358, 645)
[22:29:02] 🎯 移动到尺码 130 坐标: (358, 645)
[22:29:03] 🎯 点击尺码 130
[22:29:05] 🎯 检测到多颜色商品，需要依次点击 3 个颜色
[22:29:05] 🎨 处理颜色 1/3: 套装
[22:29:05] 📍 使用记录的坐标: 套装
[22:29:05]    相对坐标: (74, 247)
[22:29:05]    绝对坐标: (344, 477)
[22:29:05] 🎯 移动到颜色 套装 坐标: (344, 477)
[22:29:06] 🎯 点击颜色 套装
[22:29:08] 📸 截取颜色 套装 更新后的页面...
[22:29:09] ✅ 价格OCR截图已保存: 商品图片\1-1.jpg
[22:29:11] 价格OCR结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[22:29:11] 字典格式价格OCR识别到 19 个文本
[22:29:11] 价格OCR识别: 快要卖完¥59.41 ￥69.9 (置信度: 0.930)
[22:29:11] 价格OCR识别: 1 (置信度: 0.998)
[22:29:11] 价格OCR识别: 已选：套装130码适合身高125左右 (置信度: 0.997)
[22:29:11] 价格OCR识别: 颜色 (置信度: 1.000)
[22:29:11] 价格OCR识别: 套装 (置信度: 1.000)
[22:29:11] 价格OCR识别: T恤 (置信度: 0.962)
[22:29:11] 价格OCR识别: 裤子 (置信度: 1.000)
[22:29:11] 价格OCR识别: 尺码 (置信度: 0.999)
[22:29:11] 价格OCR识别: 90码适合身高85左右 (置信度: 0.999)
[22:29:11] 价格OCR识别: 100码适合身高95左右 (置信度: 0.999)
[22:29:11] 价格OCR识别: 110码适合身高105左右 (置信度: 0.999)
[22:29:11] 价格OCR识别: 120码适合身高115字谱 (置信度: 0.999)
[22:29:11] 价格OCR识别: 130码适合身高125左右 (置信度: 0.999)
[22:29:11] 价格OCR识别: 140码适合身高135左右 (置信度: 0.999)
[22:29:11] 价格OCR识别: 150码适合身高145左右 (置信度: 0.999)
[22:29:11] 价格OCR识别: 10（店内款随意发） (置信度: 0.995)
[22:29:11] 价格OCR识别: 免费服务 (置信度: 1.000)
[22:29:11] 价格OCR识别: 退货包运费（商家赠送) (置信度: 0.925)
[22:29:11] 价格OCR识别: 一次选多款，不满意可退货包运费> (置信度: 0.980)
[22:29:11] ✅ 价格OCR识别完成，共识别到 19 个文本
[22:29:11] ✅ 提取到通用价格: 69.9 (来源: 快要卖完¥59.41 ￥69.9)
[22:29:11] ✅ 获取到颜色 套装 的价格: 69.9
[22:29:11] 🔍 开始坐标校验，重新进行完整OCR识别...
[22:29:11] 🔍 执行坐标校验OCR识别...
[22:29:11] 🔍 开始智能OCR分析屏幕区域: (270, 230) 到 (691, 883)
[22:29:11] ✅ OCR截图已保存: 商品图片\coordinate_verify.jpg
[22:29:11] 正在执行OCR识别...
[22:29:13] OCR原始结果类型: <class 'list'>
[22:29:13] OCR原始结果长度: 1
[22:29:13] 第一页结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[22:29:13] OCRResult对象属性: ['_save_funcs', '_json_writer', '_rand_fn', '_img_writer']
[22:29:13]   _save_funcs: <class 'list'> - [<bound method JsonMixin.save_to_json of {'input_path': None, 'page_index': None, 'doc_preprocessor_...
[22:29:13]   _json_writer: <class 'paddlex.inference.utils.io.writers.JsonWriter'> - <paddlex.inference.utils.io.writers.JsonWriter object at 0x00000200DFF0AA80>
[22:29:13]   _rand_fn: <class 'NoneType'> - None
[22:29:13]   _img_writer: <class 'paddlex.inference.utils.io.writers.ImageWriter'> - <paddlex.inference.utils.io.writers.ImageWriter object at 0x00000200E6E82E10>
[22:29:13] 方式2成功：识别到 19 个文本
[22:29:13] 文本 0: '快要卖完¥59.41 ￥69.9', 置信度: 0.9302
[22:29:13] OCR识别: 快要卖完¥59.41 ￥69.9 (置信度: 0.9302)
[22:29:13] 文本 1: '1', 置信度: 0.9977
[22:29:13] OCR识别: 1 (置信度: 0.9977)
[22:29:13] 文本 2: '已选：套装130码适合身高125左右', 置信度: 0.9972
[22:29:13] OCR识别: 已选：套装130码适合身高125左右 (置信度: 0.9972)
[22:29:13] 文本 3: '颜色', 置信度: 0.9997
[22:29:13] OCR识别: 颜色 (置信度: 0.9997)
[22:29:13] 文本 4: '套装', 置信度: 0.9996
[22:29:13] OCR识别: 套装 (置信度: 0.9996)
[22:29:13] 文本 5: 'T恤', 置信度: 0.9624
[22:29:13] OCR识别: T恤 (置信度: 0.9624)
[22:29:13] 文本 6: '裤子', 置信度: 0.9999
[22:29:13] OCR识别: 裤子 (置信度: 0.9999)
[22:29:13] 文本 7: '尺码', 置信度: 0.9990
[22:29:13] OCR识别: 尺码 (置信度: 0.9990)
[22:29:13] 文本 8: '90码适合身高85左右', 置信度: 0.9993
[22:29:13] OCR识别: 90码适合身高85左右 (置信度: 0.9993)
[22:29:13] 文本 9: '100码适合身高95左右', 置信度: 0.9995
[22:29:13] OCR识别: 100码适合身高95左右 (置信度: 0.9995)
[22:29:13] 文本 10: '110码适合身高105左右', 置信度: 0.9991
[22:29:13] OCR识别: 110码适合身高105左右 (置信度: 0.9991)
[22:29:13] 文本 11: '120码适合身高115字谱', 置信度: 0.9990
[22:29:13] OCR识别: 120码适合身高115字谱 (置信度: 0.9990)
[22:29:13] 文本 12: '130码适合身高125左右', 置信度: 0.9994
[22:29:13] OCR识别: 130码适合身高125左右 (置信度: 0.9994)
[22:29:13] 文本 13: '140码适合身高135左右', 置信度: 0.9987
[22:29:13] OCR识别: 140码适合身高135左右 (置信度: 0.9987)
[22:29:13] 文本 14: '150码适合身高145左右', 置信度: 0.9991
[22:29:13] OCR识别: 150码适合身高145左右 (置信度: 0.9991)
[22:29:13] 文本 15: '10（店内款随意发）', 置信度: 0.9948
[22:29:13] OCR识别: 10（店内款随意发） (置信度: 0.9948)
[22:29:13] 文本 16: '免费服务', 置信度: 0.9998
[22:29:13] OCR识别: 免费服务 (置信度: 0.9998)
[22:29:13] 文本 17: '退货包运费（商家赠送)', 置信度: 0.9247
[22:29:13] OCR识别: 退货包运费（商家赠送) (置信度: 0.9247)
[22:29:13] 文本 18: '一次选多款，不满意可退货包运费>', 置信度: 0.9798
[22:29:13] OCR识别: 一次选多款，不满意可退货包运费> (置信度: 0.9798)
[22:29:13] ✅ OCR识别完成，共识别到 19 个文本
[22:29:13] 🧠 开始智能分析商品信息...
[22:29:13] 🔧 开始智能拼接被分割的文本...
[22:29:13] 🎯 找到上方紧贴文本:
[22:29:13]    上方文本: '尺码' Y范围: 289.0-313.0, X范围: 2.0-43.0
[22:29:13]    当前文本: '90码适合身高85左右' Y范围: 324.0-346.0, X起始: 10.0
[22:29:13]    垂直间距: 11.0px, X重叠: True
[22:29:13] ✅ 向上拼接: '尺码' + '90码适合身高85左右' = '尺码90码适合身高85左右'
[22:29:13] 🔗 完成向上拼接:
[22:29:13]    结果: '尺码90码适合身高85左右'
[22:29:13] 🎯 找到上方紧贴文本:
[22:29:13]    上方文本: '免费服务' Y范围: 496.0-518.0, X范围: 3.0-72.0
[22:29:13]    当前文本: '退货包运费（商家赠送)' Y范围: 532.0-553.0, X起始: 28.0
[22:29:13]    垂直间距: 14.0px, X重叠: True
[22:29:13] ✅ 向上拼接: '免费服务' + '退货包运费（商家赠送)' = '免费服务退货包运费（商家赠送)'
[22:29:13] 🔗 完成向上拼接:
[22:29:13]    结果: '免费服务退货包运费（商家赠送)'
[22:29:13] 📊 拼接结果: 原始19个文本 → 拼接后17个文本
[22:29:13] 📝 拼接后的文本列表:
[22:29:13]    1. '快要卖完¥59.41 ￥69.9'
[22:29:13]    2. '1'
[22:29:13]    3. '已选：套装130码适合身高125左右'
[22:29:13]    4. '颜色'
[22:29:13]    5. '套装'
[22:29:13]    6. 'T恤'
[22:29:13]    7. '裤子'
[22:29:13]    8. '尺码90码适合身高85左右'
[22:29:13]    9. '100码适合身高95左右'
[22:29:13]    10. '110码适合身高105左右'
[22:29:13]    11. '120码适合身高115字谱'
[22:29:13]    12. '130码适合身高125左右'
[22:29:13]    13. '140码适合身高135左右'
[22:29:13]    14. '150码适合身高145左右'
[22:29:13]    15. '10（店内款随意发）'
[22:29:13]    16. '免费服务退货包运费（商家赠送)'
[22:29:13]    17. '一次选多款，不满意可退货包运费>'
[22:29:13] 🎯 找到尺码区域开始位置: 第8行 '尺码90码适合身高85左右'
[22:29:13] ✅ 提取尺码: 100 (来源: '100码适合身高95左右')
[22:29:13] 🔍 处理bbox: [171, 323, 323, 345] (长度: 4)
[22:29:13] ✅ 计算坐标成功: (247, 334)
[22:29:13] 📍 记录尺码坐标: 100 -> (247, 334)
[22:29:13] ✅ 提取尺码: 110 (来源: '110码适合身高105左右')
[22:29:13] 🔍 处理bbox: [10, 363, 165, 386] (长度: 4)
[22:29:13] ✅ 计算坐标成功: (87, 374)
[22:29:13] 📍 记录尺码坐标: 110 -> (87, 374)
[22:29:13] ✅ 提取尺码: 120 (来源: '120码适合身高115字谱')
[22:29:13] 🔍 处理bbox: [184, 363, 341, 386] (长度: 4)
[22:29:13] ✅ 计算坐标成功: (262, 374)
[22:29:13] 📍 记录尺码坐标: 120 -> (262, 374)
[22:29:13] ✅ 提取尺码: 130 (来源: '130码适合身高125左右')
[22:29:13] 🔍 处理bbox: [11, 406, 166, 424] (长度: 4)
[22:29:13] ✅ 计算坐标成功: (88, 415)
[22:29:13] 📍 记录尺码坐标: 130 -> (88, 415)
[22:29:13] ✅ 提取尺码: 140 (来源: '140码适合身高135左右')
[22:29:13] 🔍 处理bbox: [185, 406, 341, 424] (长度: 4)
[22:29:13] ✅ 计算坐标成功: (263, 415)
[22:29:13] 📍 记录尺码坐标: 140 -> (263, 415)
[22:29:13] ✅ 提取尺码: 150 (来源: '150码适合身高145左右')
[22:29:13] 🔍 处理bbox: [11, 445, 169, 466] (长度: 4)
[22:29:13] ✅ 计算坐标成功: (90, 455)
[22:29:13] 📍 记录尺码坐标: 150 -> (90, 455)
[22:29:13] ⚠️ 跳过无效尺码: 10 (来源: '10（店内款随意发）')
[22:29:13] 🛑 遇到停止关键词，结束尺码提取: '免费服务退货包运费（商家赠送)'
[22:29:13] 📊 尺码提取结果: 数字=[100, 110, 120, 130, 140, 150], 范围=100-150, 原始文本数量=6
[22:29:13] 尺码信息: {'optimized_range': '100-150', 'original_texts': ['100码适合身高95左右', '110码适合身高105左右', '120码适合身高115字谱', '130码适合身高125左右', '140码适合身高135左右', '150码适合身高145左右'], 'size_numbers': [100, 110, 120, 130, 140, 150]}
[22:29:13] 📍 已记录尺码坐标: {100: (247, 334), 110: (87, 374), 120: (262, 374), 130: (88, 415), 140: (263, 415), 150: (90, 455)}
[22:29:13] 🎯 找到颜色分类开始位置: 第3行 '颜色'
[22:29:13] 🎯 找到颜色分类结束位置: 第7行 '尺码90码适合身高85左右'
[22:29:13] 🔍 开始提取颜色分类: 从第4行到第6行
[22:29:13] ✅ 保留有效文本: '套装'
[22:29:13] ✅ 保留有效文本: 'T恤'
[22:29:13] ✅ 保留有效文本: '裤子'
[22:29:13] 🔍 检查颜色文本: '套装' (长度: 2)
[22:29:13] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [ 47 ... 258]
[22:29:13] 🔍 价格检测结果: False
[22:29:13] 🔍 处理bbox: [47, 236, 96, 258] (长度: 4)
[22:29:13] ✅ 计算坐标成功: (71, 247)
[22:29:13] 📍 记录颜色坐标: 套装 -> (71, 247)
[22:29:13] ✅ 提取到无价格颜色: 套装
[22:29:13] 🔍 检查颜色文本: 'T恤' (长度: 2)
[22:29:13] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [193 ... 258]
[22:29:13] 🔍 价格检测结果: False
[22:29:13] 🔍 处理bbox: [193, 237, 224, 258] (长度: 4)
[22:29:13] ✅ 计算坐标成功: (208, 247)
[22:29:13] 📍 记录颜色坐标: T恤 -> (208, 247)
[22:29:13] ✅ 提取到无价格颜色: T恤
[22:29:13] 🔍 检查颜色文本: '裤子' (长度: 2)
[22:29:13] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [328 ... 259]
[22:29:13] 🔍 价格检测结果: False
[22:29:13] 🔍 处理bbox: [328, 236, 366, 259] (长度: 4)
[22:29:13] ✅ 计算坐标成功: (347, 247)
[22:29:13] 📍 记录颜色坐标: 裤子 -> (347, 247)
[22:29:13] ✅ 提取到无价格颜色: 裤子
[22:29:13] 🎨 颜色分类提取完成: 共提取到 3 个颜色
[22:29:13] 📍 坐标记录完成: 共记录 3 个坐标
[22:29:13] 提取到颜色分类: [{'pure_name': '套装', 'original_text': '套装', 'has_direct_price': False, 'direct_price': None, 'bbox': array([ 47, ..., 258], dtype=int16)}, {'pure_name': 'T恤', 'original_text': 'T恤', 'has_direct_price': False, 'direct_price': None, 'bbox': array([193, ..., 258], dtype=int16)}, {'pure_name': '裤子', 'original_text': '裤子', 'has_direct_price': False, 'direct_price': None, 'bbox': array([328, ..., 259], dtype=int16)}]
[22:29:13] 颜色分类数量: 3
[22:29:13] 📍 已记录颜色坐标: {'套装': (71, 247), 'T恤': (208, 247), '裤子': (347, 247)}
[22:29:13] 🎯 从页面提取价格信息
[22:29:13] ✅ 页面通用价格: 69.9 (来源: 快要卖完¥59.41 ￥69.9)
[22:29:13] 🔧 不分配页面价格给颜色，使用交互式方案获取准确价格
[22:29:13] 🔍 商品类型分析:
[22:29:13]    颜色数量: 3
[22:29:13]    颜色直接带价格: False
[22:29:13]    页面有券前/券后价格: False
[22:29:13]    尺码带价格: False
[22:29:13] 📊 分析类型: type3_multiple_colors_no_prices
[22:29:13] 🔧 处理方案: interactive
[22:29:13] 📊 坐标对比结果（仅对比原有颜色）:
[22:29:13]    套装: (74, 247) → (71, 247) (差异: X±3, Y±0)
[22:29:13]    ✅ 套装 坐标无显著变化
[22:29:13]    T恤: (208, 247) → (208, 247) (差异: X±0, Y±0)
[22:29:13]    ✅ T恤 坐标无显著变化
[22:29:13]    裤子: (347, 247) → (347, 247) (差异: X±0, Y±0)
[22:29:13]    ✅ 裤子 坐标无显著变化
[22:29:13] ✅ 所有颜色坐标无显著变化，继续使用原始坐标
[22:29:13] ✅ 坐标无变化，继续使用原始坐标
[22:29:13] 🎨 处理颜色 2/3: T恤
[22:29:13] 📍 使用记录的坐标: T恤
[22:29:13]    相对坐标: (208, 247)
[22:29:13]    绝对坐标: (478, 477)
[22:29:13] 🎯 移动到颜色 T恤 坐标: (478, 477)
[22:29:14] 🎯 点击颜色 T恤
[22:29:17] 📸 截取颜色 T恤 更新后的页面...
[22:29:17] ✅ 价格OCR截图已保存: 商品图片\1-2.jpg
[22:29:19] 价格OCR结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[22:29:19] 字典格式价格OCR识别到 19 个文本
[22:29:19] 价格OCR识别: 快要卖完￥29.9 ￥69.9 (置信度: 0.915)
[22:29:19] 价格OCR识别: 1 (置信度: 0.991)
[22:29:19] 价格OCR识别: 已选：T恤130码适合身高125左右 (置信度: 0.996)
[22:29:19] 价格OCR识别: 颜色 (置信度: 1.000)
[22:29:19] 价格OCR识别: 套装 (置信度: 0.999)
[22:29:19] 价格OCR识别: T恤 (置信度: 0.980)
[22:29:19] 价格OCR识别: 裤子 (置信度: 1.000)
[22:29:19] 价格OCR识别: 尺码 (置信度: 0.999)
[22:29:19] 价格OCR识别: 90码适合身高85左右 (置信度: 0.997)
[22:29:19] 价格OCR识别: 100码适合身高95左右 (置信度: 0.998)
[22:29:19] 价格OCR识别: 110码适合身高105左右 (置信度: 0.997)
[22:29:19] 价格OCR识别: 120码适合身高115字谱 (置信度: 0.990)
[22:29:19] 价格OCR识别: 130码适合身高125左右 (置信度: 0.999)
[22:29:19] 价格OCR识别: 140码适合身高135左右 (置信度: 0.998)
[22:29:19] 价格OCR识别: 150码适合身高145左右 (置信度: 0.995)
[22:29:19] 价格OCR识别: 10（店内款随意发） (置信度: 0.995)
[22:29:19] 价格OCR识别: 免费服务 (置信度: 1.000)
[22:29:19] 价格OCR识别: 退货包运费(商家赠送) (置信度: 0.919)
[22:29:19] 价格OCR识别: 一次选多款，不满意可退货包运费> (置信度: 0.980)
[22:29:19] ✅ 价格OCR识别完成，共识别到 19 个文本
[22:29:19] ✅ 提取到通用价格: 29.9 (来源: 快要卖完￥29.9 ￥69.9)
[22:29:19] ✅ 获取到颜色 T恤 的价格: 29.9
[22:29:19] 🎨 处理颜色 3/3: 裤子
[22:29:19] 📍 使用记录的坐标: 裤子
[22:29:19]    相对坐标: (347, 247)
[22:29:19]    绝对坐标: (617, 477)
[22:29:19] 🎯 移动到颜色 裤子 坐标: (617, 477)
[22:29:20] 🎯 点击颜色 裤子
[22:29:22] 📸 截取颜色 裤子 更新后的页面...
[22:29:22] ✅ 价格OCR截图已保存: 商品图片\1-3.jpg
[22:29:24] 价格OCR结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[22:29:24] 字典格式价格OCR识别到 19 个文本
[22:29:24] 价格OCR识别: 快要卖完￥39.9￥69.9 (置信度: 0.967)
[22:29:24] 价格OCR识别: 1 (置信度: 0.991)
[22:29:24] 价格OCR识别: 已选：裤子130码适合身高125左右 (置信度: 0.997)
[22:29:24] 价格OCR识别: 颜色 (置信度: 1.000)
[22:29:24] 价格OCR识别: 套装 (置信度: 0.999)
[22:29:24] 价格OCR识别: T恤 (置信度: 0.961)
[22:29:24] 价格OCR识别: 裤子 (置信度: 1.000)
[22:29:24] 价格OCR识别: 尺码 (置信度: 0.999)
[22:29:24] 价格OCR识别: 90码适合身高85左右 (置信度: 0.999)
[22:29:24] 价格OCR识别: 100码适合身高95左右 (置信度: 0.999)
[22:29:24] 价格OCR识别: 110码适合身高105左右 (置信度: 0.999)
[22:29:24] 价格OCR识别: 120码适合身高115字谱 (置信度: 0.999)
[22:29:24] 价格OCR识别: 130码适合身高125左右 (置信度: 0.999)
[22:29:24] 价格OCR识别: 140码适合身高135左右 (置信度: 0.999)
[22:29:24] 价格OCR识别: 150码适合身高145左右 (置信度: 0.999)
[22:29:24] 价格OCR识别: 10（店内款随意发） (置信度: 0.995)
[22:29:24] 价格OCR识别: 免费服务 (置信度: 1.000)
[22:29:24] 价格OCR识别: 退货包运费（商家赠送) (置信度: 0.925)
[22:29:24] 价格OCR识别: 一次选多款，不满意可退货包运费> (置信度: 0.980)
[22:29:24] ✅ 价格OCR识别完成，共识别到 19 个文本
[22:29:24] ✅ 提取到通用价格: 39.9 (来源: 快要卖完￥39.9￥69.9)
[22:29:24] ✅ 获取到颜色 裤子 的价格: 39.9
[22:29:24] 💾 保存分析结果到Excel...
[22:29:24] 找到目标商品链接在第 2 行
[22:29:24] ✅ 保存有价格颜色: 套装 -> 69.9
[22:29:24] ✅ 保存有价格颜色: T恤 -> 29.9
[22:29:24] ✅ 保存有价格颜色: 裤子 -> 39.9
[22:29:24] 找到下一个商品链接在第 13 行
[22:29:24] 清空第 3 行到第 12 行中的 0 行有内容数据，保留空行
[22:29:24] ✅ 分析结果已保存到商品1下方: 商品图片\商品SKU信息.xlsx
[22:29:24]    插入了 5 行新数据
[22:29:24] 🎉 交互式价格获取完成
[22:29:24] ==================================================
[22:29:24] ✅ 智能OCR分析完成
[22:29:24] ==================================================
[22:29:24] ✅ 详情页图片捕获完成
[22:29:24] ✅ OCR分析和Excel保存已在详情页处理中完成
[22:29:24] ✅ 第 1 个商品处理完成
[22:29:24] 🔄 准备处理下一个商品...
[22:29:24] 🔄 开始返回搜索页面...
[22:29:24] 移动到位置(470,590)...
[22:29:25] 点击鼠标右键...
[22:29:26] 再次点击鼠标右键...
[22:29:27] ✅ 返回搜索页面操作完成
[22:29:27] 
============================================================
[22:29:27] 🎯 开始处理第 2 个商品
[22:29:27] 商品链接: https://mobile.yangkeduo.com/goods1.html?ps=A2IzYy2B3q
[22:29:27] ============================================================
[22:29:27] 步骤1: 设置剪贴板内容...
[22:29:27] 已复制商品链接到剪贴板: https://mobile.yangkeduo.com/goods1.html?ps=A2IzYy2B3q
[22:29:28] ✅ 剪贴板内容验证成功
[22:29:28] 步骤2: 点击搜索框并输入链接...
[22:29:28] 移动到位置(480,96)并点击...
[22:29:29] 按下Ctrl+A全选...
[22:29:29] 执行AutoHotkey脚本: ctrl_a.ahk
[22:29:30] ✅ AutoHotkey脚本执行成功: ctrl_a
[22:29:30] 粘贴商品链接...
[22:29:30] 使用外部脚本软件执行真正的Ctrl+V...
[22:29:30] Python的键盘模拟无法被escrcpy识别，尝试其他方案
[22:29:30] 方法1: 尝试AutoHotkey...
[22:29:30] 找到脚本文件: paste_v2.ahk
[22:29:30] 检查路径: AutoHotkey.exe
[22:29:30] 检查路径: C:\Program Files\AutoHotkey\AutoHotkey.exe
[22:29:30] 检查路径: C:\Program Files (x86)\AutoHotkey\AutoHotkey.exe
[22:29:30] 检查路径: C:\Program Files\AutoHotkey\v2\AutoHotkey.exe
[22:29:30] 找到AutoHotkey: C:\Program Files\AutoHotkey\v2\AutoHotkey.exe
[22:29:30] 执行AutoHotkey脚本: paste_v2.ahk
[22:29:32] AutoHotkey返回码: 0
[22:29:32] ✅ AutoHotkey执行成功
[22:29:32] ✅ Ctrl+V操作执行成功
[22:29:35] 步骤3: 点击搜索按钮...
[22:29:35] 正在查找图片: SOUSUO2.png (路径: image\IQOO\SOUSUO2.png)
[22:29:35] 找到图片 SOUSUO2.png，位置: (641, 81)
[22:29:35] 点击位置: (664, 96)
[22:29:35] 已点击图片: SOUSUO2.png
[22:29:35] 步骤4: 等待3秒进入商品页面...
[22:29:38] 步骤5: 执行从右往左的滑动操作...
[22:29:38] 开始执行从右往左的滑动操作...
[22:29:38] 📍 起始位置: (630, 185)
[22:29:38] 📍 结束位置: (284, 185)
[22:29:38] 📏 滑动距离: 346 像素
[22:29:38] ⏱️ 滑动时长: 0.3 秒
[22:29:38] 移动到起始位置(630, 185)
[22:29:39] 按住左键从(630, 185)拖拽到(284, 185)
[22:29:39] 开始滑动，时长0.3秒...
[22:29:40] 释放左键，滑动操作完成
[22:29:40] ✅ 从右往左的滑动操作完成
[22:29:40] 步骤6: 移动到位置(470,185)...
[22:29:41] 已移动到位置(470,185)
[22:29:41] 步骤7: 执行鼠标点击和长按操作...
[22:29:41] 点击鼠标左键一次
[22:29:41] 在位置(475, 323)长按鼠标左键0.5秒
[22:29:42] 鼠标操作完成
[22:29:43] 步骤8: 查找并点击保存按钮...
[22:29:43] 尝试查找图片 BAOCUN1.png (第1次/共3次)
[22:29:43] 正在查找图片: BAOCUN1.png (路径: image\IQOO\BAOCUN1.png)
[22:29:44] 找到图片 BAOCUN1.png，位置: (454, 808)
[22:29:44] 移动到图片上，等待0.5秒...
[22:29:44] 已点击图片: BAOCUN1.png
[22:29:44] 步骤9: 检测保存状态...
[22:29:44] 开始检测图片: BAOCUNZHONG.png (路径: image\IQOO\BAOCUNZHONG.png)
[22:29:44] 最多检测 80 次，每隔 0.2 秒检测一次
[22:29:44] 第 1/80 次检测...
[22:29:45] 第 2/80 次检测...
[22:29:45] 第 3/80 次检测...
[22:29:45] ✅ 找到图片 BAOCUNZHONG.png！位置: (443, 507)
[22:29:45] ✅ 检测到保存中状态，等待保存完成...
[22:29:45] 等待5秒让图片完全保存到手机...
[22:29:50] 步骤10: 复制手机图片到商品 2 文件夹...
[22:29:50] 第 1/3 次尝试复制图片...
[22:29:50] 开始从MTP设备复制图片到商品 2 文件夹...
[22:29:50] 正在使用Windows Shell API查找MTP设备...
[22:29:50] 找到设备: iQOO Z1
[22:29:50] 进入文件夹: 内部存储设备
[22:29:50] 进入文件夹: DCIM
[22:29:50] 进入文件夹: Pindd
[22:29:50] 进入文件夹: goods
[22:29:50] 成功找到目标文件夹: 内部存储设备\DCIM\Pindd\goods
[22:29:50] 目标路径: F:\自动捕获数据\商品图片\2
[22:29:50] 复制文件: 1753626501373-1315797334.jpg
[22:29:50] ✅ 成功复制: 1753626501373-1315797334.jpg
[22:29:50] 复制文件: 1753626501484-1645306700.jpg
[22:29:50] ✅ 成功复制: 1753626501484-1645306700.jpg
[22:29:50] 复制文件: 1753626501256--945688330.jpg
[22:29:50] ✅ 成功复制: 1753626501256--945688330.jpg
[22:29:50] 复制文件: 1753626501297-483977703.jpg
[22:29:51] ✅ 成功复制: 1753626501297-483977703.jpg
[22:29:51] 复制文件: 1753626501401--1182398115.jpg
[22:29:51] ✅ 成功复制: 1753626501401--1182398115.jpg
[22:29:51] 复制文件: 1753626501042--850234244.jpg
[22:29:51] ✅ 成功复制: 1753626501042--850234244.jpg
[22:29:51] 复制文件: 1753626500939-199319035.jpg
[22:29:51] ✅ 成功复制: 1753626500939-199319035.jpg
[22:29:51] 复制文件: 1753626501073--278957689.jpg
[22:29:51] ✅ 成功复制: 1753626501073--278957689.jpg
[22:29:51] 复制文件: 1753626501332-1706905320.jpg
[22:29:51] ✅ 成功复制: 1753626501332-1706905320.jpg
[22:29:51] 复制文件: 1753626501176--1680485554.jpg
[22:29:51] ✅ 成功复制: 1753626501176--1680485554.jpg
[22:29:51] 复制文件: 1753626501138--1193025293.jpg
[22:29:51] ✅ 成功复制: 1753626501138--1193025293.jpg
[22:29:51] 复制文件: 1753626501207--778938302.jpg
[22:29:51] ✅ 成功复制: 1753626501207--778938302.jpg
[22:29:51] 复制文件: 1753626501435-1066972225.jpg
[22:29:51] ✅ 成功复制: 1753626501435-1066972225.jpg
[22:29:51] 复制文件: 1753626518152-660672411.jpg
[22:29:51] ✅ 成功复制: 1753626518152-660672411.jpg
[22:29:51] 复制文件: 1753626518186-2125388435.jpg
[22:29:51] ✅ 成功复制: 1753626518186-2125388435.jpg
[22:29:51] 复制文件: 1753626518219-1804250823.jpg
[22:29:51] ✅ 成功复制: 1753626518219-1804250823.jpg
[22:29:51] 复制文件: 1753626518249--1065563707.jpg
[22:29:51] ✅ 成功复制: 1753626518249--1065563707.jpg
[22:29:51] 复制文件: 1753626518295-152127411.jpg
[22:29:52] ✅ 成功复制: 1753626518295-152127411.jpg
[22:29:52] 复制文件: 1753626518357-1552964844.jpg
[22:29:52] ✅ 成功复制: 1753626518357-1552964844.jpg
[22:29:52] 复制文件: 1753626518406-1752797653.jpg
[22:29:52] ✅ 成功复制: 1753626518406-1752797653.jpg
[22:29:52] 复制文件: 1753626518441--1753262296.jpg
[22:29:52] ✅ 成功复制: 1753626518441--1753262296.jpg
[22:29:52] 复制文件: 1753626518489-842254062.jpg
[22:29:52] ✅ 成功复制: 1753626518489-842254062.jpg
[22:29:52] 复制文件: 1753626518530--255483989.jpg
[22:29:52] ✅ 成功复制: 1753626518530--255483989.jpg
[22:29:52] 复制文件: 1753626518571--1285728568.jpg
[22:29:52] ✅ 成功复制: 1753626518571--1285728568.jpg
[22:29:52] 复制文件: 1753626518627-396311468.jpg
[22:29:52] ✅ 成功复制: 1753626518627-396311468.jpg
[22:29:52] 复制文件: 1753626518662-1252897153.jpg
[22:29:52] ✅ 成功复制: 1753626518662-1252897153.jpg
[22:29:52] 复制文件: 1753626518696--1537897804.jpg
[22:29:52] ✅ 成功复制: 1753626518696--1537897804.jpg
[22:29:52] 复制文件: 1753626518746--1146868097.jpg
[22:29:52] ✅ 成功复制: 1753626518746--1146868097.jpg
[22:29:52] 复制文件: 1753626518780--1712757656.jpg
[22:29:52] ✅ 成功复制: 1753626518780--1712757656.jpg
[22:29:52] 复制文件: 1753626518827-184866696.jpg
[22:29:52] ✅ 成功复制: 1753626518827-184866696.jpg
[22:29:52] 复制文件: 1753626584506--174181552.jpg
[22:29:52] ✅ 成功复制: 1753626584506--174181552.jpg
[22:29:52] 复制文件: 1753626584568-649400088.jpg
[22:29:53] ✅ 成功复制: 1753626584568-649400088.jpg
[22:29:53] 复制文件: 1753626584616--75726748.jpg
[22:29:53] ✅ 成功复制: 1753626584616--75726748.jpg
[22:29:53] 复制文件: 1753626584693--1636531876.jpg
[22:29:53] ✅ 成功复制: 1753626584693--1636531876.jpg
[22:29:53] 复制文件: 1753626584727-1492996970.jpg
[22:29:53] ✅ 成功复制: 1753626584727-1492996970.jpg
[22:29:53] 复制文件: 1753626584762-2021427306.jpg
[22:29:53] ✅ 成功复制: 1753626584762-2021427306.jpg
[22:29:53] 成功复制 36 个文件
[22:29:53] ✅ 成功复制 36 个图片到商品 2 文件夹
[22:29:53] 复制的文件:
[22:29:53]   - 1753626501373-1315797334.jpg
[22:29:53]   - 1753626501484-1645306700.jpg
[22:29:53]   - 1753626501256--945688330.jpg
[22:29:53]   - 1753626501297-483977703.jpg
[22:29:53]   - 1753626501401--1182398115.jpg
[22:29:53]   - 1753626501042--850234244.jpg
[22:29:53]   - 1753626500939-199319035.jpg
[22:29:53]   - 1753626501073--278957689.jpg
[22:29:53]   - 1753626501332-1706905320.jpg
[22:29:53]   - 1753626501176--1680485554.jpg
[22:29:53]   - 1753626501138--1193025293.jpg
[22:29:53]   - 1753626501207--778938302.jpg
[22:29:53]   - 1753626501435-1066972225.jpg
[22:29:53]   - 1753626518152-660672411.jpg
[22:29:53]   - 1753626518186-2125388435.jpg
[22:29:53]   - 1753626518219-1804250823.jpg
[22:29:53]   - 1753626518249--1065563707.jpg
[22:29:53]   - 1753626518295-152127411.jpg
[22:29:53]   - 1753626518357-1552964844.jpg
[22:29:53]   - 1753626518406-1752797653.jpg
[22:29:53]   - 1753626518441--1753262296.jpg
[22:29:53]   - 1753626518489-842254062.jpg
[22:29:53]   - 1753626518530--255483989.jpg
[22:29:53]   - 1753626518571--1285728568.jpg
[22:29:53]   - 1753626518627-396311468.jpg
[22:29:53]   - 1753626518662-1252897153.jpg
[22:29:53]   - 1753626518696--1537897804.jpg
[22:29:53]   - 1753626518746--1146868097.jpg
[22:29:53]   - 1753626518780--1712757656.jpg
[22:29:53]   - 1753626518827-184866696.jpg
[22:29:53]   - 1753626584506--174181552.jpg
[22:29:53]   - 1753626584568-649400088.jpg
[22:29:53]   - 1753626584616--75726748.jpg
[22:29:53]   - 1753626584693--1636531876.jpg
[22:29:53]   - 1753626584727-1492996970.jpg
[22:29:53]   - 1753626584762-2021427306.jpg
[22:29:53] ⚠️  注意：由于MTP协议限制，无法自动删除手机上的原文件
[22:29:53] 请手动删除手机 DCIM/Pindd/goods 文件夹中的图片以释放空间
[22:29:53] ✅ 图片复制完成
[22:29:53] 步骤11: 删除手机上的原文件...
[22:29:53] 开始删除手机文件操作...
[22:29:53] 查找并点击GOODS.png...
[22:29:53] 正在查找图片: GOODS.png (路径: image\IQOO\GOODS.png)
[22:29:53] 查找或点击图片 GOODS.png 时出错: 
[22:29:53] ❌ 未找到GOODS.png图片
[22:29:53] ⚠️ 手机文件删除失败，请手动删除
[22:29:53] 步骤12: 开始处理主图...
[22:29:53] 步骤13: 开始第二轮操作（详情页）...
[22:29:53] 处理主图文件夹: 商品图片\2
[22:29:53] === 开始详情页图片捕获流程 ===
[22:29:53] 步骤13.1: 移动到(475,230)并点击右键...
[22:29:53] 找到 36 张图片
[22:29:53] 删除多余图片: 1753626501401--1182398115.jpg
[22:29:53] 删除多余图片: 1753626501042--850234244.jpg
[22:29:53] 删除多余图片: 1753626500939-199319035.jpg
[22:29:53] 删除多余图片: 1753626501073--278957689.jpg
[22:29:53] 删除多余图片: 1753626501332-1706905320.jpg
[22:29:53] 删除多余图片: 1753626501176--1680485554.jpg
[22:29:53] 删除多余图片: 1753626501138--1193025293.jpg
[22:29:53] 删除多余图片: 1753626501207--778938302.jpg
[22:29:53] 删除多余图片: 1753626501435-1066972225.jpg
[22:29:53] 删除多余图片: 1753626518152-660672411.jpg
[22:29:53] 删除多余图片: 1753626518186-2125388435.jpg
[22:29:53] 删除多余图片: 1753626518219-1804250823.jpg
[22:29:53] 删除多余图片: 1753626518249--1065563707.jpg
[22:29:53] 删除多余图片: 1753626518295-152127411.jpg
[22:29:53] 删除多余图片: 1753626518357-1552964844.jpg
[22:29:53] 删除多余图片: 1753626518406-1752797653.jpg
[22:29:53] 删除多余图片: 1753626518441--1753262296.jpg
[22:29:53] 删除多余图片: 1753626518489-842254062.jpg
[22:29:53] 删除多余图片: 1753626518530--255483989.jpg
[22:29:53] 删除多余图片: 1753626518571--1285728568.jpg
[22:29:53] 删除多余图片: 1753626518627-396311468.jpg
[22:29:53] 删除多余图片: 1753626518662-1252897153.jpg
[22:29:53] 删除多余图片: 1753626518696--1537897804.jpg
[22:29:53] 删除多余图片: 1753626518746--1146868097.jpg
[22:29:53] 删除多余图片: 1753626518780--1712757656.jpg
[22:29:53] 删除多余图片: 1753626518827-184866696.jpg
[22:29:53] 删除多余图片: 1753626584506--174181552.jpg
[22:29:53] 删除多余图片: 1753626584568-649400088.jpg
[22:29:53] 删除多余图片: 1753626584616--75726748.jpg
[22:29:53] 删除多余图片: 1753626584693--1636531876.jpg
[22:29:53] 删除多余图片: 1753626584727-1492996970.jpg
[22:29:53] 删除多余图片: 1753626584762-2021427306.jpg
[22:29:53] 重命名: 1753626501373-1315797334.jpg → 主图1.jpg
[22:29:53] 重命名: 1753626501484-1645306700.jpg → 主图2.jpg
[22:29:53] 重命名: 1753626501256--945688330.jpg → 主图3.jpg
[22:29:53] 重命名: 1753626501297-483977703.jpg → 主图4.jpg
[22:29:53] ✅ 主图处理完成
[22:29:55] 步骤13.2: 使用AutoHotkey向下滚动40次...
[22:29:55] 执行AutoHotkey脚本: scroll_down.ahk
[22:29:59] ✅ AutoHotkey脚本执行成功: scroll_down
[22:29:59] ✅ AutoHotkey滚动执行成功
[22:29:59] 步骤13.3: 移动到(477,300)并点击左键...
[22:30:00] ✅ 点击操作完成
[22:30:00] 步骤13.4: 移动到(480,495)并长按0.5秒...
[22:30:01] ✅ 长按操作完成
[22:30:02] 步骤13.5: 查找并点击保存按钮...
[22:30:02] 尝试查找图片 BAOCUN1.png (第1次/共3次)
[22:30:02] 正在查找图片: BAOCUN1.png (路径: image\IQOO\BAOCUN1.png)
[22:30:02] 找到图片 BAOCUN1.png，位置: (454, 808)
[22:30:03] 移动到图片上，等待0.5秒...
[22:30:03] 已点击图片: BAOCUN1.png
[22:30:03] 步骤13.6: 检测详情页保存状态...
[22:30:03] 开始检测图片: BAOCUNZHONG.png (路径: image\IQOO\BAOCUNZHONG.png)
[22:30:03] 最多检测 80 次，每隔 0.2 秒检测一次
[22:30:03] 第 1/80 次检测...
[22:30:04] 第 2/80 次检测...
[22:30:04] ✅ 找到图片 BAOCUNZHONG.png！位置: (443, 507)
[22:30:04] ✅ 检测到详情页保存中状态，等待保存完成...
[22:30:09] 步骤13.7: 复制详情页图片...
[22:30:09] 第 1/3 次尝试复制详情页图片...
[22:30:09] 开始从MTP设备复制图片到商品 2 文件夹...
[22:30:09] 正在使用Windows Shell API查找MTP设备...
[22:30:09] 找到设备: iQOO Z1
[22:30:09] 进入文件夹: 内部存储设备
[22:30:09] 进入文件夹: DCIM
[22:30:09] 进入文件夹: Pindd
[22:30:09] 进入文件夹: goods
[22:30:09] 成功找到目标文件夹: 内部存储设备\DCIM\Pindd\goods
[22:30:09] 目标路径: F:\自动捕获数据\商品图片\2
[22:30:09] 复制文件: 1753626501373-1315797334.jpg
[22:30:09] ✅ 成功复制: 1753626501373-1315797334.jpg
[22:30:09] 复制文件: 1753626501484-1645306700.jpg
[22:30:09] ✅ 成功复制: 1753626501484-1645306700.jpg
[22:30:09] 复制文件: 1753626501256--945688330.jpg
[22:30:09] ✅ 成功复制: 1753626501256--945688330.jpg
[22:30:09] 复制文件: 1753626501297-483977703.jpg
[22:30:09] ✅ 成功复制: 1753626501297-483977703.jpg
[22:30:09] 复制文件: 1753626501401--1182398115.jpg
[22:30:09] ✅ 成功复制: 1753626501401--1182398115.jpg
[22:30:09] 复制文件: 1753626501042--850234244.jpg
[22:30:09] ✅ 成功复制: 1753626501042--850234244.jpg
[22:30:09] 复制文件: 1753626500939-199319035.jpg
[22:30:09] ✅ 成功复制: 1753626500939-199319035.jpg
[22:30:09] 复制文件: 1753626501073--278957689.jpg
[22:30:09] ✅ 成功复制: 1753626501073--278957689.jpg
[22:30:09] 复制文件: 1753626501332-1706905320.jpg
[22:30:10] ✅ 成功复制: 1753626501332-1706905320.jpg
[22:30:10] 复制文件: 1753626501176--1680485554.jpg
[22:30:10] ✅ 成功复制: 1753626501176--1680485554.jpg
[22:30:10] 复制文件: 1753626501138--1193025293.jpg
[22:30:10] ✅ 成功复制: 1753626501138--1193025293.jpg
[22:30:10] 复制文件: 1753626501207--778938302.jpg
[22:30:10] ✅ 成功复制: 1753626501207--778938302.jpg
[22:30:10] 复制文件: 1753626501435-1066972225.jpg
[22:30:10] ✅ 成功复制: 1753626501435-1066972225.jpg
[22:30:10] 复制文件: 1753626518152-660672411.jpg
[22:30:10] ✅ 成功复制: 1753626518152-660672411.jpg
[22:30:10] 复制文件: 1753626518186-2125388435.jpg
[22:30:10] ✅ 成功复制: 1753626518186-2125388435.jpg
[22:30:10] 复制文件: 1753626518219-1804250823.jpg
[22:30:10] ✅ 成功复制: 1753626518219-1804250823.jpg
[22:30:10] 复制文件: 1753626518249--1065563707.jpg
[22:30:10] ✅ 成功复制: 1753626518249--1065563707.jpg
[22:30:10] 复制文件: 1753626518295-152127411.jpg
[22:30:10] ✅ 成功复制: 1753626518295-152127411.jpg
[22:30:10] 复制文件: 1753626518357-1552964844.jpg
[22:30:10] ✅ 成功复制: 1753626518357-1552964844.jpg
[22:30:10] 复制文件: 1753626518406-1752797653.jpg
[22:30:10] ✅ 成功复制: 1753626518406-1752797653.jpg
[22:30:10] 复制文件: 1753626518441--1753262296.jpg
[22:30:10] ✅ 成功复制: 1753626518441--1753262296.jpg
[22:30:10] 复制文件: 1753626518489-842254062.jpg
[22:30:10] ✅ 成功复制: 1753626518489-842254062.jpg
[22:30:10] 复制文件: 1753626518530--255483989.jpg
[22:30:11] ✅ 成功复制: 1753626518530--255483989.jpg
[22:30:11] 复制文件: 1753626518571--1285728568.jpg
[22:30:11] ✅ 成功复制: 1753626518571--1285728568.jpg
[22:30:11] 复制文件: 1753626518627-396311468.jpg
[22:30:11] ✅ 成功复制: 1753626518627-396311468.jpg
[22:30:11] 复制文件: 1753626518662-1252897153.jpg
[22:30:11] ✅ 成功复制: 1753626518662-1252897153.jpg
[22:30:11] 复制文件: 1753626518696--1537897804.jpg
[22:30:11] ✅ 成功复制: 1753626518696--1537897804.jpg
[22:30:11] 复制文件: 1753626518746--1146868097.jpg
[22:30:11] ✅ 成功复制: 1753626518746--1146868097.jpg
[22:30:11] 复制文件: 1753626518780--1712757656.jpg
[22:30:11] ✅ 成功复制: 1753626518780--1712757656.jpg
[22:30:11] 复制文件: 1753626518827-184866696.jpg
[22:30:11] ✅ 成功复制: 1753626518827-184866696.jpg
[22:30:11] 复制文件: 1753626584506--174181552.jpg
[22:30:11] ✅ 成功复制: 1753626584506--174181552.jpg
[22:30:11] 复制文件: 1753626584568-649400088.jpg
[22:30:11] ✅ 成功复制: 1753626584568-649400088.jpg
[22:30:11] 复制文件: 1753626584616--75726748.jpg
[22:30:11] ✅ 成功复制: 1753626584616--75726748.jpg
[22:30:11] 复制文件: 1753626584693--1636531876.jpg
[22:30:11] ✅ 成功复制: 1753626584693--1636531876.jpg
[22:30:11] 复制文件: 1753626584727-1492996970.jpg
[22:30:11] ✅ 成功复制: 1753626584727-1492996970.jpg
[22:30:11] 复制文件: 1753626584762-2021427306.jpg
[22:30:11] ✅ 成功复制: 1753626584762-2021427306.jpg
[22:30:11] 复制文件: 1753626603375-1223281507.jpg
[22:30:12] ✅ 成功复制: 1753626603375-1223281507.jpg
[22:30:12] 复制文件: 1753626603420--59820335.jpg
[22:30:12] ✅ 成功复制: 1753626603420--59820335.jpg
[22:30:12] 复制文件: 1753626603446--1882576375.jpg
[22:30:12] ✅ 成功复制: 1753626603446--1882576375.jpg
[22:30:12] 复制文件: 1753626603487--2039576347.jpg
[22:30:12] ✅ 成功复制: 1753626603487--2039576347.jpg
[22:30:12] 复制文件: 1753626603536--320016844.jpg
[22:30:12] ✅ 成功复制: 1753626603536--320016844.jpg
[22:30:12] 复制文件: 1753626603577--1854090630.jpg
[22:30:12] ✅ 成功复制: 1753626603577--1854090630.jpg
[22:30:12] 成功复制 42 个文件
[22:30:12] ✅ 成功复制 42 个图片到商品 2 文件夹
[22:30:12] 复制的文件:
[22:30:12]   - 1753626501373-1315797334.jpg
[22:30:12]   - 1753626501484-1645306700.jpg
[22:30:12]   - 1753626501256--945688330.jpg
[22:30:12]   - 1753626501297-483977703.jpg
[22:30:12]   - 1753626501401--1182398115.jpg
[22:30:12]   - 1753626501042--850234244.jpg
[22:30:12]   - 1753626500939-199319035.jpg
[22:30:12]   - 1753626501073--278957689.jpg
[22:30:12]   - 1753626501332-1706905320.jpg
[22:30:12]   - 1753626501176--1680485554.jpg
[22:30:12]   - 1753626501138--1193025293.jpg
[22:30:12]   - 1753626501207--778938302.jpg
[22:30:12]   - 1753626501435-1066972225.jpg
[22:30:12]   - 1753626518152-660672411.jpg
[22:30:12]   - 1753626518186-2125388435.jpg
[22:30:12]   - 1753626518219-1804250823.jpg
[22:30:12]   - 1753626518249--1065563707.jpg
[22:30:12]   - 1753626518295-152127411.jpg
[22:30:12]   - 1753626518357-1552964844.jpg
[22:30:12]   - 1753626518406-1752797653.jpg
[22:30:12]   - 1753626518441--1753262296.jpg
[22:30:12]   - 1753626518489-842254062.jpg
[22:30:12]   - 1753626518530--255483989.jpg
[22:30:12]   - 1753626518571--1285728568.jpg
[22:30:12]   - 1753626518627-396311468.jpg
[22:30:12]   - 1753626518662-1252897153.jpg
[22:30:12]   - 1753626518696--1537897804.jpg
[22:30:12]   - 1753626518746--1146868097.jpg
[22:30:12]   - 1753626518780--1712757656.jpg
[22:30:12]   - 1753626518827-184866696.jpg
[22:30:12]   - 1753626584506--174181552.jpg
[22:30:12]   - 1753626584568-649400088.jpg
[22:30:12]   - 1753626584616--75726748.jpg
[22:30:12]   - 1753626584693--1636531876.jpg
[22:30:12]   - 1753626584727-1492996970.jpg
[22:30:12]   - 1753626584762-2021427306.jpg
[22:30:12]   - 1753626603375-1223281507.jpg
[22:30:12]   - 1753626603420--59820335.jpg
[22:30:12]   - 1753626603446--1882576375.jpg
[22:30:12]   - 1753626603487--2039576347.jpg
[22:30:12]   - 1753626603536--320016844.jpg
[22:30:12]   - 1753626603577--1854090630.jpg
[22:30:12] ⚠️  注意：由于MTP协议限制，无法自动删除手机上的原文件
[22:30:12] 请手动删除手机 DCIM/Pindd/goods 文件夹中的图片以释放空间
[22:30:12] ✅ 详情页图片复制完成
[22:30:12] 步骤13.8: 删除手机上的详情页图片...
[22:30:12] 开始删除手机文件操作...
[22:30:12] 查找并点击GOODS.png...
[22:30:12] 正在查找图片: GOODS.png (路径: image\IQOO\GOODS.png)
[22:30:12] 查找或点击图片 GOODS.png 时出错: 
[22:30:12] ❌ 未找到GOODS.png图片
[22:30:12] ⚠️ 手机详情页文件删除失败，请手动删除
[22:30:12] 步骤13.8: 处理详情页图片...
[22:30:12] 处理详情页图片文件夹: 商品图片\2
[22:30:12] 跳过已处理的主图: 主图1.jpg
[22:30:12] 跳过已处理的主图: 主图2.jpg
[22:30:12] 跳过已处理的主图: 主图3.jpg
[22:30:12] 跳过已处理的主图: 主图4.jpg
[22:30:12] 找到 42 张详情页图片
[22:30:12] 删除多余详情页图片: 1753626584506--174181552.jpg
[22:30:12] 删除多余详情页图片: 1753626584568-649400088.jpg
[22:30:12] 删除多余详情页图片: 1753626584616--75726748.jpg
[22:30:12] 删除多余详情页图片: 1753626584693--1636531876.jpg
[22:30:12] 删除多余详情页图片: 1753626584727-1492996970.jpg
[22:30:12] 删除多余详情页图片: 1753626584762-2021427306.jpg
[22:30:12] 删除多余详情页图片: 1753626603375-1223281507.jpg
[22:30:12] 删除多余详情页图片: 1753626603420--59820335.jpg
[22:30:12] 删除多余详情页图片: 1753626603446--1882576375.jpg
[22:30:12] 删除多余详情页图片: 1753626603487--2039576347.jpg
[22:30:12] 删除多余详情页图片: 1753626603536--320016844.jpg
[22:30:12] 删除多余详情页图片: 1753626603577--1854090630.jpg
[22:30:12] 重命名详情页图片: 1753626501373-1315797334.jpg → 1.jpg
[22:30:12] 重命名详情页图片: 1753626501484-1645306700.jpg → 2.jpg
[22:30:12] 重命名详情页图片: 1753626501256--945688330.jpg → 3.jpg
[22:30:12] 重命名详情页图片: 1753626501297-483977703.jpg → 4.jpg
[22:30:12] 重命名详情页图片: 1753626501401--1182398115.jpg → 5.jpg
[22:30:12] 重命名详情页图片: 1753626501042--850234244.jpg → 6.jpg
[22:30:12] 重命名详情页图片: 1753626500939-199319035.jpg → 7.jpg
[22:30:12] 重命名详情页图片: 1753626501073--278957689.jpg → 8.jpg
[22:30:12] 重命名详情页图片: 1753626501332-1706905320.jpg → 9.jpg
[22:30:12] 重命名详情页图片: 1753626501176--1680485554.jpg → 10.jpg
[22:30:12] 重命名详情页图片: 1753626501138--1193025293.jpg → 11.jpg
[22:30:12] 重命名详情页图片: 1753626501207--778938302.jpg → 12.jpg
[22:30:12] 重命名详情页图片: 1753626501435-1066972225.jpg → 13.jpg
[22:30:12] 重命名详情页图片: 1753626518152-660672411.jpg → 14.jpg
[22:30:12] 重命名详情页图片: 1753626518186-2125388435.jpg → 15.jpg
[22:30:12] 重命名详情页图片: 1753626518219-1804250823.jpg → 16.jpg
[22:30:12] 重命名详情页图片: 1753626518249--1065563707.jpg → 17.jpg
[22:30:12] 重命名详情页图片: 1753626518295-152127411.jpg → 18.jpg
[22:30:12] 重命名详情页图片: 1753626518357-1552964844.jpg → 19.jpg
[22:30:12] 重命名详情页图片: 1753626518406-1752797653.jpg → 20.jpg
[22:30:12] 重命名详情页图片: 1753626518441--1753262296.jpg → 21.jpg
[22:30:12] 重命名详情页图片: 1753626518489-842254062.jpg → 22.jpg
[22:30:12] 重命名详情页图片: 1753626518530--255483989.jpg → 23.jpg
[22:30:12] 重命名详情页图片: 1753626518571--1285728568.jpg → 24.jpg
[22:30:12] 重命名详情页图片: 1753626518627-396311468.jpg → 25.jpg
[22:30:12] 重命名详情页图片: 1753626518662-1252897153.jpg → 26.jpg
[22:30:12] 重命名详情页图片: 1753626518696--1537897804.jpg → 27.jpg
[22:30:12] 重命名详情页图片: 1753626518746--1146868097.jpg → 28.jpg
[22:30:12] 重命名详情页图片: 1753626518780--1712757656.jpg → 29.jpg
[22:30:12] 重命名详情页图片: 1753626518827-184866696.jpg → 30.jpg
[22:30:12] ✅ 详情页图片处理完成
[22:30:12] ✅ 主图保持'主图X'格式，详情页图片重命名为1-30
[22:30:12] ✅ 详情页图片处理完成
[22:30:12] 步骤13.9: 执行最后的鼠标操作...
[22:30:12] 开始执行最后的鼠标操作序列...
[22:30:12] 移动到位置(475,125)...
[22:30:13] 点击右键...
[22:30:13] 等待1秒...
[22:30:14] 移动到位置(670,940)...
[22:30:15] 点击左键...
[22:30:15] 等待2.5秒让页面加载完成...
[22:30:17] ✅ 最后的鼠标操作序列完成
[22:30:17] 步骤13.10: 执行智能OCR分析...
[22:30:17] ==================================================
[22:30:17] 🧠 智能OCR分析开始
[22:30:17] ==================================================
[22:30:17] 🔍 开始智能OCR分析屏幕区域: (270, 230) 到 (691, 883)
[22:30:17] ✅ OCR截图已保存: 商品图片\2.jpg
[22:30:17] 正在执行OCR识别...
[22:30:20] OCR原始结果类型: <class 'list'>
[22:30:20] OCR原始结果长度: 1
[22:30:20] 第一页结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[22:30:20] OCRResult对象属性: ['_save_funcs', '_json_writer', '_rand_fn', '_img_writer']
[22:30:20]   _save_funcs: <class 'list'> - [<bound method JsonMixin.save_to_json of {'input_path': None, 'page_index': None, 'doc_preprocessor_...
[22:30:20]   _json_writer: <class 'paddlex.inference.utils.io.writers.JsonWriter'> - <paddlex.inference.utils.io.writers.JsonWriter object at 0x00000200D5060260>
[22:30:20]   _rand_fn: <class 'NoneType'> - None
[22:30:20]   _img_writer: <class 'paddlex.inference.utils.io.writers.ImageWriter'> - <paddlex.inference.utils.io.writers.ImageWriter object at 0x00000200E8FDDAC0>
[22:30:20] 方式2成功：识别到 24 个文本
[22:30:20] 文本 0: '大促底价￥69.2￥69.9', 置信度: 0.9792
[22:30:20] OCR识别: 大促底价￥69.2￥69.9 (置信度: 0.9792)
[22:30:20] 文本 1: '满80减4', 置信度: 0.9997
[22:30:20] OCR识别: 满80减4 (置信度: 0.9997)
[22:30:20] 文本 2: '已选：背带裤套装两件120', 置信度: 0.9984
[22:30:20] OCR识别: 已选：背带裤套装两件120 (置信度: 0.9984)
[22:30:20] 文本 3: '1', 置信度: 0.9991
[22:30:20] OCR识别: 1 (置信度: 0.9991)
[22:30:20] 文本 4: '十', 置信度: 0.5095
[22:30:20] OCR识别: 十 (置信度: 0.5095)
[22:30:20] 文本 5: '优惠', 置信度: 0.9996
[22:30:20] OCR识别: 优惠 (置信度: 0.9996)
[22:30:20] 文本 6: '满80可减4元>', 置信度: 0.9824
[22:30:20] OCR识别: 满80可减4元> (置信度: 0.9824)
[22:30:20] 文本 7: '颜色分类', 置信度: 0.9998
[22:30:20] OCR识别: 颜色分类 (置信度: 0.9998)
[22:30:20] 文本 8: '快要抢光', 置信度: 0.9993
[22:30:20] OCR识别: 快要抢光 (置信度: 0.9993)
[22:30:20] 文本 9: '快要抢光', 置信度: 0.9982
[22:30:20] OCR识别: 快要抢光 (置信度: 0.9982)
[22:30:20] 文本 10: '牛仔灰单件背带裤', 置信度: 0.9994
[22:30:20] OCR识别: 牛仔灰单件背带裤 (置信度: 0.9994)
[22:30:20] 文本 11: '●背带裤套装两件', 置信度: 0.9579
[22:30:20] OCR识别: ●背带裤套装两件 (置信度: 0.9579)
[22:30:20] 文本 12: '参考分类', 置信度: 0.9999
[22:30:20] OCR识别: 参考分类 (置信度: 0.9999)
[22:30:20] 文本 13: '90', 置信度: 0.9999
[22:30:20] OCR识别: 90 (置信度: 0.9999)
[22:30:20] 文本 14: '100', 置信度: 1.0000
[22:30:20] OCR识别: 100 (置信度: 1.0000)
[22:30:20] 文本 15: '110', 置信度: 0.9999
[22:30:20] OCR识别: 110 (置信度: 0.9999)
[22:30:20] 文本 16: '120', 置信度: 0.9999
[22:30:20] OCR识别: 120 (置信度: 0.9999)
[22:30:20] 文本 17: '130', 置信度: 0.9999
[22:30:20] OCR识别: 130 (置信度: 0.9999)
[22:30:20] 文本 18: '140', 置信度: 0.9997
[22:30:20] OCR识别: 140 (置信度: 0.9997)
[22:30:20] 文本 19: '免费服务', 置信度: 0.9998
[22:30:20] OCR识别: 免费服务 (置信度: 0.9998)
[22:30:20] 文本 20: '退货包运费(商家赠送)', 置信度: 0.9315
[22:30:20] OCR识别: 退货包运费(商家赠送) (置信度: 0.9315)
[22:30:20] 文本 21: '一次选多款，不满意可极速退款>', 置信度: 0.9811
[22:30:20] OCR识别: 一次选多款，不满意可极速退款> (置信度: 0.9811)
[22:30:20] 文本 22: '大促直降', 置信度: 0.9984
[22:30:20] OCR识别: 大促直降 (置信度: 0.9984)
[22:30:20] 文本 23: '全网低价', 置信度: 0.9985
[22:30:20] OCR识别: 全网低价 (置信度: 0.9985)
[22:30:20] ✅ OCR识别完成，共识别到 24 个文本
[22:30:20] 💾 保存OCR结果到文件...
[22:30:20] ✅ OCR结果已保存: OCR\商品2_OCR结果.txt
[22:30:20] 🧠 开始智能分析商品信息...
[22:30:20] 🔧 开始智能拼接被分割的文本...
[22:30:20] 🎯 找到上方紧贴文本:
[22:30:20]    上方文本: '大促底价￥69.2￥69.9' Y范围: 17.0-42.0, X范围: 166.0-346.0
[22:30:20]    当前文本: '满80减4' Y范围: 44.0-69.0, X起始: 146.0
[22:30:20]    垂直间距: 2.0px, X重叠: True
[22:30:20] ✅ 向上拼接: '大促底价￥69.2￥69.9' + '满80减4' = '大促底价￥69.2￥69.9满80减4'
[22:30:20] 🔗 完成向上拼接:
[22:30:20]    结果: '大促底价￥69.2￥69.9满80减4'
[22:30:20] 🎯 找到上方紧贴文本:
[22:30:20]    上方文本: '1' Y范围: 127.0-141.0, X范围: 186.0-197.0
[22:30:20]    当前文本: '十' Y范围: 128.0-139.0, X起始: 216.0
[22:30:20]    垂直间距: 13.0px, X重叠: True
[22:30:20] ✅ 向上拼接: '1' + '十' = '1十'
[22:30:20] 🔗 完成向上拼接:
[22:30:20]    结果: '1十'
[22:30:20] 🎯 找到上方紧贴文本:
[22:30:20]    上方文本: '优惠' Y范围: 168.0-196.0, X范围: 1.0-44.0
[22:30:20]    当前文本: '颜色分类' Y范围: 208.0-234.0, X起始: 2.0
[22:30:20]    垂直间距: 12.0px, X重叠: True
[22:30:20] ✅ 向上拼接: '优惠' + '颜色分类' = '优惠颜色分类'
[22:30:20] 🔗 完成向上拼接:
[22:30:20]    结果: '优惠颜色分类'
[22:30:20] 🎯 找到上方紧贴文本:
[22:30:20]    上方文本: '颜色分类' Y范围: 208.0-234.0, X范围: 2.0-74.0
[22:30:20]    当前文本: '快要抢光' Y范围: 230.0-249.0, X起始: 4.0
[22:30:20]    垂直间距: 4.0px, X重叠: True
[22:30:20] ✅ 向上拼接: '颜色分类' + '快要抢光' = '颜色分类快要抢光'
[22:30:20] 🔗 完成向上拼接:
[22:30:20]    结果: '颜色分类快要抢光'
[22:30:20] 🎯 找到上方紧贴文本:
[22:30:20]    上方文本: '颜色分类' Y范围: 208.0-234.0, X范围: 2.0-74.0
[22:30:20]    当前文本: '牛仔灰单件背带裤' Y范围: 244.0-269.0, X起始: 9.0
[22:30:20]    垂直间距: 10.0px, X重叠: True
[22:30:20] 🎯 找到上方紧贴文本:
[22:30:20]    上方文本: '快要抢光' Y范围: 230.0-249.0, X范围: 4.0-64.0
[22:30:20]    当前文本: '牛仔灰单件背带裤' Y范围: 244.0-269.0, X起始: 9.0
[22:30:20]    垂直间距: 5.0px, X重叠: True
[22:30:20] ✅ 向上拼接: '快要抢光' + '牛仔灰单件背带裤' = '快要抢光牛仔灰单件背带裤'
[22:30:20] 🔗 完成向上拼接:
[22:30:20]    结果: '快要抢光牛仔灰单件背带裤'
[22:30:20] 🎯 找到上方紧贴文本:
[22:30:20]    上方文本: '快要抢光' Y范围: 230.0-249.0, X范围: 156.0-214.0
[22:30:20]    当前文本: '●背带裤套装两件' Y范围: 245.0-267.0, X起始: 158.0
[22:30:20]    垂直间距: 4.0px, X重叠: True
[22:30:20] ✅ 向上拼接: '快要抢光' + '●背带裤套装两件' = '快要抢光●背带裤套装两件'
[22:30:20] 🔗 完成向上拼接:
[22:30:20]    结果: '快要抢光●背带裤套装两件'
[22:30:20] 🎯 找到上方紧贴文本:
[22:30:20]    上方文本: '免费服务' Y范围: 375.0-401.0, X范围: 2.0-73.0
[22:30:20]    当前文本: '退货包运费(商家赠送)' Y范围: 413.0-435.0, X起始: 12.0
[22:30:20]    垂直间距: 12.0px, X重叠: True
[22:30:20] ✅ 向上拼接: '免费服务' + '退货包运费(商家赠送)' = '免费服务退货包运费(商家赠送)'
[22:30:20] 🔗 完成向上拼接:
[22:30:20]    结果: '免费服务退货包运费(商家赠送)'
[22:30:20] 📊 拼接结果: 原始24个文本 → 拼接后19个文本
[22:30:20] 📝 拼接后的文本列表:
[22:30:20]    1. '大促底价￥69.2￥69.9满80减4'
[22:30:20]    2. '已选：背带裤套装两件120'
[22:30:20]    3. '1十'
[22:30:20]    4. '满80可减4元>'
[22:30:20]    5. '优惠颜色分类'
[22:30:20]    6. '颜色分类快要抢光'
[22:30:20]    7. '快要抢光牛仔灰单件背带裤'
[22:30:20]    8. '快要抢光●背带裤套装两件'
[22:30:20]    9. '参考分类'
[22:30:20]    10. '90'
[22:30:20]    11. '100'
[22:30:20]    12. '110'
[22:30:20]    13. '120'
[22:30:20]    14. '130'
[22:30:20]    15. '140'
[22:30:20]    16. '免费服务退货包运费(商家赠送)'
[22:30:20]    17. '一次选多款，不满意可极速退款>'
[22:30:20]    18. '大促直降'
[22:30:20]    19. '全网低价'
[22:30:20] 🎯 找到尺码区域开始位置: 第9行 '参考分类'
[22:30:20] ✅ 提取尺码: 90 (来源: '90')
[22:30:20] 🔍 处理bbox: [16, 323, 49, 350] (长度: 4)
[22:30:20] ✅ 计算坐标成功: (32, 336)
[22:30:20] 📍 记录尺码坐标: 90 -> (32, 336)
[22:30:20] ✅ 提取尺码: 100 (来源: '100')
[22:30:20] 🔍 处理bbox: [75, 324, 114, 349] (长度: 4)
[22:30:20] ✅ 计算坐标成功: (94, 336)
[22:30:20] 📍 记录尺码坐标: 100 -> (94, 336)
[22:30:20] ✅ 提取尺码: 110 (来源: '110')
[22:30:20] 🔍 处理bbox: [139, 323, 176, 349] (长度: 4)
[22:30:20] ✅ 计算坐标成功: (157, 336)
[22:30:20] 📍 记录尺码坐标: 110 -> (157, 336)
[22:30:20] ✅ 提取尺码: 120 (来源: '120')
[22:30:20] 🔍 处理bbox: [200, 322, 241, 350] (长度: 4)
[22:30:20] ✅ 计算坐标成功: (220, 336)
[22:30:20] 📍 记录尺码坐标: 120 -> (220, 336)
[22:30:20] ✅ 提取尺码: 130 (来源: '130')
[22:30:20] 🔍 处理bbox: [263, 324, 302, 349] (长度: 4)
[22:30:20] ✅ 计算坐标成功: (282, 336)
[22:30:20] 📍 记录尺码坐标: 130 -> (282, 336)
[22:30:20] ✅ 提取尺码: 140 (来源: '140')
[22:30:20] 🔍 处理bbox: [322, 320, 367, 352] (长度: 4)
[22:30:20] ✅ 计算坐标成功: (344, 336)
[22:30:20] 📍 记录尺码坐标: 140 -> (344, 336)
[22:30:20] 🛑 遇到停止关键词，结束尺码提取: '免费服务退货包运费(商家赠送)'
[22:30:20] 📊 尺码提取结果: 数字=[90, 100, 110, 120, 130, 140], 范围=90-140, 原始文本数量=6
[22:30:20] 尺码信息: {'optimized_range': '90-140', 'original_texts': ['90', '100', '110', '120', '130', '140'], 'size_numbers': [90, 100, 110, 120, 130, 140]}
[22:30:20] 📍 已记录尺码坐标: {100: (94, 336), 110: (157, 336), 120: (220, 336), 130: (282, 336), 140: (344, 336), 150: (90, 455), 90: (32, 336)}
[22:30:20] 🔍 发现颜色嵌入格式: '优惠颜色分类' → 提取: '分类'
[22:30:20] 🎯 发现嵌入颜色分类: '分类' (来源: '优惠颜色分类')
[22:30:20] 🎯 找到颜色分类开始位置: 第4行 '优惠颜色分类'
[22:30:20] 🔍 发现颜色分类嵌入格式: '颜色分类快要抢光' → 提取: '快要抢光'
[22:30:20] ⚠️ 嵌入颜色清理后为空: '快要抢光' (来源: '颜色分类快要抢光')
[22:30:20] 🎯 找到颜色分类开始位置: 第5行 '颜色分类快要抢光'
[22:30:20] 🎯 找到颜色分类结束位置: 第8行 '参考分类'
[22:30:20] 🔍 开始提取颜色分类: 从第6行到第7行
[22:30:20] 🧹 清理库存提示: '快要抢光牛仔灰单件背带裤' → '牛仔灰单件背带裤'
[22:30:20] 🧹 清理库存提示: '快要抢光●背带裤套装两件' → '●背带裤套装两件'
[22:30:20] ✅ 添加嵌入颜色分类: '分类'
[22:30:20] 🔍 检查颜色文本: '牛仔灰单件背带裤' (长度: 8)
[22:30:20] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [  4 ... 249]
[22:30:20] 🔍 价格检测结果: False
[22:30:20] 🔍 处理bbox: [4, 230, 64, 249] (长度: 4)
[22:30:20] ✅ 计算坐标成功: (34, 239)
[22:30:20] 📍 记录颜色坐标(库存提示调整): 牛仔灰单件背带裤 -> (34, 239) → (34, 244)
[22:30:20] ✅ 提取到无价格颜色: 牛仔灰单件背带裤
[22:30:20] 🔍 检查颜色文本: '●背带裤套装两件' (长度: 8)
[22:30:20] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [156 ... 249]
[22:30:20] 🔍 价格检测结果: False
[22:30:20] 🔍 处理bbox: [156, 230, 214, 249] (长度: 4)
[22:30:20] ✅ 计算坐标成功: (185, 239)
[22:30:20] 📍 记录颜色坐标(库存提示调整): ●背带裤套装两件 -> (185, 239) → (185, 244)
[22:30:20] ✅ 提取到无价格颜色: ●背带裤套装两件
[22:30:20] 🔍 检查颜色文本: '分类' (长度: 2)
[22:30:20] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [  1 ... 196]
[22:30:20] 🔍 价格检测结果: False
[22:30:20] 🔍 处理bbox: [1, 168, 44, 196] (长度: 4)
[22:30:20] ✅ 计算坐标成功: (22, 182)
[22:30:20] 📍 记录颜色坐标: 分类 -> (22, 182)
[22:30:20] ✅ 提取到无价格颜色: 分类
[22:30:20] 🎨 颜色分类提取完成: 共提取到 3 个颜色
[22:30:20] 📍 坐标记录完成: 共记录 3 个坐标
[22:30:20] 提取到颜色分类: [{'pure_name': '牛仔灰单件背带裤', 'original_text': '牛仔灰单件背带裤', 'has_direct_price': False, 'direct_price': None, 'bbox': array([  4, ..., 249], dtype=int16)}, {'pure_name': '●背带裤套装两件', 'original_text': '●背带裤套装两件', 'has_direct_price': False, 'direct_price': None, 'bbox': array([156, ..., 249], dtype=int16)}, {'pure_name': '分类', 'original_text': '分类', 'has_direct_price': False, 'direct_price': None, 'bbox': array([  1, ..., 196], dtype=int16)}]
[22:30:20] 颜色分类数量: 3
[22:30:20] 📍 已记录颜色坐标: {'牛仔灰单件背带裤': (34, 244), '●背带裤套装两件': (185, 244), '分类': (22, 182)}
[22:30:20] 🎯 从页面提取价格信息
[22:30:20] ✅ 页面底价: 69.2 (来源: 大促底价￥69.2￥69.9满80减4)
[22:30:20] 🔧 不分配页面价格给颜色，使用交互式方案获取准确价格
[22:30:20] 🔍 商品类型分析:
[22:30:20]    颜色数量: 3
[22:30:20]    颜色直接带价格: False
[22:30:20]    页面有券前/券后价格: True
[22:30:20]    尺码带价格: False
[22:30:20] 📊 分析类型: type3_multiple_colors_no_prices
[22:30:20] 🔧 处理方案: interactive
[22:30:20] 📊 智能分析结果:
[22:30:20]   优化尺码范围: 90-140
[22:30:20]   原始尺码文本: ['90', '100', '110', '120', '130', '140']
[22:30:20]   颜色分类: ['牛仔灰单件背带裤', '●背带裤套装两件', '分类']
[22:30:20]   颜色价格: {}
[22:30:20]   分析类型: type3_multiple_colors_no_prices
[22:30:20]   处理方案: interactive
[22:30:20] 🔄 切换到交互式价格获取方案
[22:30:20] 🚀 开始交互式价格获取...
[22:30:20] 需要交互获取价格的颜色: ['牛仔灰单件背带裤', '●背带裤套装两件', '分类']
[22:30:20] 📏 尺码选择策略: 从6个尺码中选择中间值 120
[22:30:20]    完整尺码列表: [90, 100, 110, 120, 130, 140]
[22:30:20]    避免最小码: 90，避免最大码: 140
[22:30:20] 选择中间尺码: 120
[22:30:20] 🔍 检查尺码坐标记录: {100: (94, 336), 110: (157, 336), 120: (220, 336), 130: (282, 336), 140: (344, 336), 150: (90, 455), 90: (32, 336)}
[22:30:20] 🔍 查找尺码: 120
[22:30:20] 📍 使用记录的尺码坐标: 120
[22:30:20]    相对坐标: (220, 336)
[22:30:20]    绝对坐标: (490, 566)
[22:30:20] 🎯 移动到尺码 120 坐标: (490, 566)
[22:30:21] 🎯 点击尺码 120
[22:30:24] 🎯 检测到多颜色商品，需要依次点击 3 个颜色
[22:30:24] 🎨 处理颜色 1/3: 牛仔灰单件背带裤
[22:30:24] 📍 使用记录的坐标: 牛仔灰单件背带裤
[22:30:24]    相对坐标: (34, 244)
[22:30:24]    绝对坐标: (304, 474)
[22:30:24] 🎯 移动到颜色 牛仔灰单件背带裤 坐标: (304, 474)
[22:30:25] 🎯 点击颜色 牛仔灰单件背带裤
[22:30:27] 📸 截取颜色 牛仔灰单件背带裤 更新后的页面...
[22:30:27] ✅ 价格OCR截图已保存: 商品图片\2-1.jpg
[22:30:29] 价格OCR结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[22:30:29] 字典格式价格OCR识别到 24 个文本
[22:30:29] 价格OCR识别: ¥39.11 - 69.2 (置信度: 0.926)
[22:30:29] 价格OCR识别: 满80减4 (置信度: 1.000)
[22:30:29] 价格OCR识别: 请选择：参考分类 (置信度: 0.982)
[22:30:29] 价格OCR识别: 1 (置信度: 0.999)
[22:30:29] 价格OCR识别: + (置信度: 0.903)
[22:30:29] 价格OCR识别: 优惠 (置信度: 0.999)
[22:30:29] 价格OCR识别: 选择款式后查看优惠 (置信度: 0.999)
[22:30:29] 价格OCR识别: 颜色分类 (置信度: 1.000)
[22:30:29] 价格OCR识别: 快要抢光 (置信度: 0.998)
[22:30:29] 价格OCR识别: 快要抢光 (置信度: 0.999)
[22:30:29] 价格OCR识别: 牛仔灰单件背带裤 (置信度: 0.999)
[22:30:29] 价格OCR识别: 背带裤套装两件 (置信度: 1.000)
[22:30:29] 价格OCR识别: 参考分类 (置信度: 1.000)
[22:30:29] 价格OCR识别: 90 (置信度: 1.000)
[22:30:29] 价格OCR识别: 100 (置信度: 1.000)
[22:30:29] 价格OCR识别: 110 (置信度: 1.000)
[22:30:29] 价格OCR识别: 120 (置信度: 1.000)
[22:30:29] 价格OCR识别: 130 (置信度: 1.000)
[22:30:29] 价格OCR识别: 140 (置信度: 1.000)
[22:30:29] 价格OCR识别: 免费服务 (置信度: 1.000)
[22:30:29] 价格OCR识别: 退货包运费(商家赠送) (置信度: 0.945)
[22:30:29] 价格OCR识别: 一次选多款，不满意可极速退款> (置信度: 0.985)
[22:30:29] 价格OCR识别: 大促直降 (置信度: 0.999)
[22:30:29] 价格OCR识别: 全网低价 (置信度: 0.999)
[22:30:29] ✅ 价格OCR识别完成，共识别到 24 个文本
[22:30:29] ⚠️ 未能从OCR结果中提取颜色 牛仔灰单件背带裤 的价格
[22:30:29] 🎨 处理颜色 2/3: ●背带裤套装两件
[22:30:29] 📍 使用记录的坐标: ●背带裤套装两件
[22:30:29]    相对坐标: (185, 244)
[22:30:29]    绝对坐标: (455, 474)
[22:30:29] 🎯 移动到颜色 ●背带裤套装两件 坐标: (455, 474)
[22:30:30] 🎯 点击颜色 ●背带裤套装两件
[22:30:32] 📸 截取颜色 ●背带裤套装两件 更新后的页面...
[22:30:32] ✅ 价格OCR截图已保存: 商品图片\2-2.jpg
[22:30:35] 价格OCR结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[22:30:35] 字典格式价格OCR识别到 24 个文本
[22:30:35] 价格OCR识别: ¥39.11 - 69.2 (置信度: 0.927)
[22:30:35] 价格OCR识别: 满80减4 (置信度: 1.000)
[22:30:35] 价格OCR识别: 请选择：参考分类 (置信度: 0.982)
[22:30:35] 价格OCR识别: 1 (置信度: 0.999)
[22:30:35] 价格OCR识别: + (置信度: 0.637)
[22:30:35] 价格OCR识别: 优惠 (置信度: 0.999)
[22:30:35] 价格OCR识别: 选择款式后查看优惠 (置信度: 0.999)
[22:30:35] 价格OCR识别: 颜色分类 (置信度: 1.000)
[22:30:35] 价格OCR识别: 快要抢光 (置信度: 0.999)
[22:30:35] 价格OCR识别: 快要抢光 (置信度: 0.998)
[22:30:35] 价格OCR识别: 牛仔灰单件背带裤 (置信度: 0.999)
[22:30:35] 价格OCR识别: 背带裤套装两件 (置信度: 1.000)
[22:30:35] 价格OCR识别: 参考分类 (置信度: 1.000)
[22:30:35] 价格OCR识别: 90 (置信度: 1.000)
[22:30:35] 价格OCR识别: 100 (置信度: 1.000)
[22:30:35] 价格OCR识别: 110 (置信度: 1.000)
[22:30:35] 价格OCR识别: 120 (置信度: 1.000)
[22:30:35] 价格OCR识别: 130 (置信度: 1.000)
[22:30:35] 价格OCR识别: 140 (置信度: 1.000)
[22:30:35] 价格OCR识别: 免费服务 (置信度: 1.000)
[22:30:35] 价格OCR识别: 退货包运费(商家赠送) (置信度: 0.945)
[22:30:35] 价格OCR识别: 一次选多款，不满意可极速退款> (置信度: 0.985)
[22:30:35] 价格OCR识别: 大促直降 (置信度: 0.999)
[22:30:35] 价格OCR识别: 全网低价 (置信度: 0.999)
[22:30:35] ✅ 价格OCR识别完成，共识别到 24 个文本
[22:30:35] ⚠️ 未能从OCR结果中提取颜色 ●背带裤套装两件 的价格
[22:30:35] 🎨 处理颜色 3/3: 分类
[22:30:35] 📍 使用记录的坐标: 分类
[22:30:35]    相对坐标: (22, 182)
[22:30:35]    绝对坐标: (292, 412)
[22:30:35] 🎯 移动到颜色 分类 坐标: (292, 412)
[22:30:36] 🎯 点击颜色 分类
[22:30:38] 📸 截取颜色 分类 更新后的页面...
[22:30:38] ✅ 价格OCR截图已保存: 商品图片\2-3.jpg
[22:30:40] 价格OCR结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[22:30:40] 字典格式价格OCR识别到 25 个文本
[22:30:40] 价格OCR识别: ¥39.11 - 69.2 (置信度: 0.903)
[22:30:40] 价格OCR识别: 满80减4 (置信度: 1.000)
[22:30:40] 价格OCR识别: 请选择：参考分类 (置信度: 0.978)
[22:30:40] 价格OCR识别: 1 (置信度: 0.999)
[22:30:40] 价格OCR识别: 十 (置信度: 0.501)
[22:30:40] 价格OCR识别: 优惠 (置信度: 0.999)
[22:30:40] 价格OCR识别: 选择款式后查看优惠 (置信度: 0.996)
[22:30:40] 价格OCR识别: 颜色分类 (置信度: 1.000)
[22:30:40] 价格OCR识别: 快要抢光 (置信度: 0.999)
[22:30:40] 价格OCR识别: 快要抢光 (置信度: 0.998)
[22:30:40] 价格OCR识别: 牛仔灰单件背带裤 (置信度: 0.999)
[22:30:40] 价格OCR识别: 背带裤套装两件 (置信度: 1.000)
[22:30:40] 价格OCR识别: 参考分类 (置信度: 1.000)
[22:30:40] 价格OCR识别: 选择款式后查看优惠 (置信度: 0.999)
[22:30:40] 价格OCR识别: 90 (置信度: 1.000)
[22:30:40] 价格OCR识别: 100 (置信度: 1.000)
[22:30:40] 价格OCR识别: 110 (置信度: 1.000)
[22:30:40] 价格OCR识别: 120 (置信度: 1.000)
[22:30:40] 价格OCR识别: 130 (置信度: 1.000)
[22:30:40] 价格OCR识别: 140 (置信度: 1.000)
[22:30:40] 价格OCR识别: 免费服务 (置信度: 1.000)
[22:30:40] 价格OCR识别: 退货包运费(商家赠送) (置信度: 0.937)
[22:30:40] 价格OCR识别: 4一次选多款，不满意可极速退款> (置信度: 0.936)
[22:30:40] 价格OCR识别: 大促直降 (置信度: 0.999)
[22:30:40] 价格OCR识别: 全网低价 (置信度: 0.998)
[22:30:40] ✅ 价格OCR识别完成，共识别到 25 个文本
[22:30:40] ⚠️ 未能从OCR结果中提取颜色 分类 的价格
[22:30:40] 💾 保存分析结果到Excel...
[22:30:40] 找到目标商品链接在第 18 行
[22:30:40] ✅ 保存有价格颜色: 牛仔灰单件背带裤 -> 获取失败
[22:30:40] ✅ 保存有价格颜色: ●背带裤套装两件 -> 获取失败
[22:30:40] ✅ 保存有价格颜色: 分类 -> 获取失败
[22:30:40] 找到下一个商品链接在第 29 行
[22:30:40] 清空第 19 行到第 28 行中的 0 行有内容数据，保留空行
[22:30:40] ✅ 分析结果已保存到商品2下方: 商品图片\商品SKU信息.xlsx
[22:30:40]    插入了 5 行新数据
[22:30:40] 🎉 交互式价格获取完成
[22:30:40] ==================================================
[22:30:40] ✅ 智能OCR分析完成
[22:30:40] ==================================================
[22:30:40] ✅ 详情页图片捕获完成
[22:30:40] ✅ OCR分析和Excel保存已在详情页处理中完成
[22:30:40] ✅ 第 2 个商品处理完成
[22:30:40] 🔄 准备处理下一个商品...
[22:30:40] 🔄 开始返回搜索页面...
[22:30:40] 移动到位置(470,590)...
[22:30:41] 点击鼠标右键...
[22:30:43] 再次点击鼠标右键...
[22:30:44] ✅ 返回搜索页面操作完成
[22:30:44] 
============================================================
[22:30:44] 🎯 开始处理第 3 个商品
[22:30:44] 商品链接: https://mobile.yangkeduo.com/goods1.html?ps=JTaVru9i0B
[22:30:44] ============================================================
[22:30:44] 步骤1: 设置剪贴板内容...
[22:30:44] 已复制商品链接到剪贴板: https://mobile.yangkeduo.com/goods1.html?ps=JTaVru9i0B
[22:30:44] ✅ 剪贴板内容验证成功
[22:30:44] 步骤2: 点击搜索框并输入链接...
[22:30:44] 移动到位置(480,96)并点击...
[22:30:45] 按下Ctrl+A全选...
[22:30:45] 执行AutoHotkey脚本: ctrl_a.ahk
[22:30:46] ✅ AutoHotkey脚本执行成功: ctrl_a
[22:30:46] 粘贴商品链接...
[22:30:46] 使用外部脚本软件执行真正的Ctrl+V...
[22:30:46] Python的键盘模拟无法被escrcpy识别，尝试其他方案
[22:30:46] 方法1: 尝试AutoHotkey...
[22:30:46] 找到脚本文件: paste_v2.ahk
[22:30:46] 检查路径: AutoHotkey.exe
[22:30:46] 检查路径: C:\Program Files\AutoHotkey\AutoHotkey.exe
[22:30:46] 检查路径: C:\Program Files (x86)\AutoHotkey\AutoHotkey.exe
[22:30:46] 检查路径: C:\Program Files\AutoHotkey\v2\AutoHotkey.exe
[22:30:46] 找到AutoHotkey: C:\Program Files\AutoHotkey\v2\AutoHotkey.exe
[22:30:46] 执行AutoHotkey脚本: paste_v2.ahk
[22:30:48] AutoHotkey返回码: 0
[22:30:48] ✅ AutoHotkey执行成功
[22:30:48] ✅ Ctrl+V操作执行成功
[22:30:51] 步骤3: 点击搜索按钮...
[22:30:51] 正在查找图片: SOUSUO2.png (路径: image\IQOO\SOUSUO2.png)
[22:30:51] 找到图片 SOUSUO2.png，位置: (641, 81)
[22:30:51] 点击位置: (664, 96)
[22:30:51] 已点击图片: SOUSUO2.png
[22:30:51] 步骤4: 等待3秒进入商品页面...
[22:30:54] 步骤5: 执行从右往左的滑动操作...
[22:30:54] 开始执行从右往左的滑动操作...
[22:30:54] 📍 起始位置: (630, 185)
[22:30:54] 📍 结束位置: (284, 185)
[22:30:54] 📏 滑动距离: 346 像素
[22:30:54] ⏱️ 滑动时长: 0.3 秒
[22:30:54] 移动到起始位置(630, 185)
[22:30:55] 按住左键从(630, 185)拖拽到(284, 185)
[22:30:55] 开始滑动，时长0.3秒...
[22:30:56] 释放左键，滑动操作完成
[22:30:57] ✅ 从右往左的滑动操作完成
[22:30:57] 步骤6: 移动到位置(470,185)...
[22:30:57] 已移动到位置(470,185)
[22:30:57] 步骤7: 执行鼠标点击和长按操作...
[22:30:57] 点击鼠标左键一次
[22:30:57] 在位置(475, 323)长按鼠标左键0.5秒
[22:30:59] 鼠标操作完成
[22:31:00] 步骤8: 查找并点击保存按钮...
[22:31:00] 尝试查找图片 BAOCUN1.png (第1次/共3次)
[22:31:00] 正在查找图片: BAOCUN1.png (路径: image\IQOO\BAOCUN1.png)
[22:31:00] 找到图片 BAOCUN1.png，位置: (455, 808)
[22:31:00] 移动到图片上，等待0.5秒...
[22:31:01] 已点击图片: BAOCUN1.png
[22:31:01] 步骤9: 检测保存状态...
[22:31:01] 开始检测图片: BAOCUNZHONG.png (路径: image\IQOO\BAOCUNZHONG.png)
[22:31:01] 最多检测 80 次，每隔 0.2 秒检测一次
[22:31:01] 第 1/80 次检测...
[22:31:01] 第 2/80 次检测...
[22:31:01] ✅ 找到图片 BAOCUNZHONG.png！位置: (443, 507)
[22:31:01] ✅ 检测到保存中状态，等待保存完成...
[22:31:01] 等待5秒让图片完全保存到手机...
[22:31:06] 步骤10: 复制手机图片到商品 3 文件夹...
[22:31:06] 第 1/3 次尝试复制图片...
[22:31:06] 开始从MTP设备复制图片到商品 3 文件夹...
[22:31:06] 正在使用Windows Shell API查找MTP设备...
[22:31:06] 找到设备: iQOO Z1
[22:31:06] 进入文件夹: 内部存储设备
[22:31:06] 进入文件夹: DCIM
[22:31:06] 进入文件夹: Pindd
[22:31:06] 进入文件夹: goods
[22:31:06] 成功找到目标文件夹: 内部存储设备\DCIM\Pindd\goods
[22:31:06] 目标路径: F:\自动捕获数据\商品图片\3
[22:31:06] 复制文件: 1753626501373-1315797334.jpg
[22:31:06] ✅ 成功复制: 1753626501373-1315797334.jpg
[22:31:06] 复制文件: 1753626501484-1645306700.jpg
[22:31:06] ✅ 成功复制: 1753626501484-1645306700.jpg
[22:31:06] 复制文件: 1753626501256--945688330.jpg
[22:31:06] ✅ 成功复制: 1753626501256--945688330.jpg
[22:31:06] 复制文件: 1753626501297-483977703.jpg
[22:31:06] ✅ 成功复制: 1753626501297-483977703.jpg
[22:31:06] 复制文件: 1753626501401--1182398115.jpg
[22:31:06] ✅ 成功复制: 1753626501401--1182398115.jpg
[22:31:06] 复制文件: 1753626501042--850234244.jpg
[22:31:07] ✅ 成功复制: 1753626501042--850234244.jpg
[22:31:07] 复制文件: 1753626500939-199319035.jpg
[22:31:07] ✅ 成功复制: 1753626500939-199319035.jpg
[22:31:07] 复制文件: 1753626501073--278957689.jpg
[22:31:07] ✅ 成功复制: 1753626501073--278957689.jpg
[22:31:07] 复制文件: 1753626501332-1706905320.jpg
[22:31:07] ✅ 成功复制: 1753626501332-1706905320.jpg
[22:31:07] 复制文件: 1753626501176--1680485554.jpg
[22:31:07] ✅ 成功复制: 1753626501176--1680485554.jpg
[22:31:07] 复制文件: 1753626501138--1193025293.jpg
[22:31:07] ✅ 成功复制: 1753626501138--1193025293.jpg
[22:31:07] 复制文件: 1753626501207--778938302.jpg
[22:31:07] ✅ 成功复制: 1753626501207--778938302.jpg
[22:31:07] 复制文件: 1753626501435-1066972225.jpg
[22:31:07] ✅ 成功复制: 1753626501435-1066972225.jpg
[22:31:07] 复制文件: 1753626518152-660672411.jpg
[22:31:07] ✅ 成功复制: 1753626518152-660672411.jpg
[22:31:07] 复制文件: 1753626518186-2125388435.jpg
[22:31:07] ✅ 成功复制: 1753626518186-2125388435.jpg
[22:31:07] 复制文件: 1753626518219-1804250823.jpg
[22:31:07] ✅ 成功复制: 1753626518219-1804250823.jpg
[22:31:07] 复制文件: 1753626518249--1065563707.jpg
[22:31:07] ✅ 成功复制: 1753626518249--1065563707.jpg
[22:31:07] 复制文件: 1753626518295-152127411.jpg
[22:31:07] ✅ 成功复制: 1753626518295-152127411.jpg
[22:31:07] 复制文件: 1753626518357-1552964844.jpg
[22:31:07] ✅ 成功复制: 1753626518357-1552964844.jpg
[22:31:07] 复制文件: 1753626518406-1752797653.jpg
[22:31:08] ✅ 成功复制: 1753626518406-1752797653.jpg
[22:31:08] 复制文件: 1753626518441--1753262296.jpg
[22:31:08] ✅ 成功复制: 1753626518441--1753262296.jpg
[22:31:08] 复制文件: 1753626518489-842254062.jpg
[22:31:08] ✅ 成功复制: 1753626518489-842254062.jpg
[22:31:08] 复制文件: 1753626518530--255483989.jpg
[22:31:08] ✅ 成功复制: 1753626518530--255483989.jpg
[22:31:08] 复制文件: 1753626518571--1285728568.jpg
[22:31:08] ✅ 成功复制: 1753626518571--1285728568.jpg
[22:31:08] 复制文件: 1753626518627-396311468.jpg
[22:31:08] ✅ 成功复制: 1753626518627-396311468.jpg
[22:31:08] 复制文件: 1753626518662-1252897153.jpg
[22:31:08] ✅ 成功复制: 1753626518662-1252897153.jpg
[22:31:08] 复制文件: 1753626518696--1537897804.jpg
[22:31:08] ✅ 成功复制: 1753626518696--1537897804.jpg
[22:31:08] 复制文件: 1753626518746--1146868097.jpg
[22:31:08] ✅ 成功复制: 1753626518746--1146868097.jpg
[22:31:08] 复制文件: 1753626518780--1712757656.jpg
[22:31:08] ✅ 成功复制: 1753626518780--1712757656.jpg
[22:31:08] 复制文件: 1753626518827-184866696.jpg
[22:31:08] ✅ 成功复制: 1753626518827-184866696.jpg
[22:31:08] 复制文件: 1753626584506--174181552.jpg
[22:31:08] ✅ 成功复制: 1753626584506--174181552.jpg
[22:31:08] 复制文件: 1753626584568-649400088.jpg
[22:31:08] ✅ 成功复制: 1753626584568-649400088.jpg
[22:31:08] 复制文件: 1753626584616--75726748.jpg
[22:31:08] ✅ 成功复制: 1753626584616--75726748.jpg
[22:31:08] 复制文件: 1753626584693--1636531876.jpg
[22:31:09] ✅ 成功复制: 1753626584693--1636531876.jpg
[22:31:09] 复制文件: 1753626584727-1492996970.jpg
[22:31:09] ✅ 成功复制: 1753626584727-1492996970.jpg
[22:31:09] 复制文件: 1753626584762-2021427306.jpg
[22:31:09] ✅ 成功复制: 1753626584762-2021427306.jpg
[22:31:09] 复制文件: 1753626603375-1223281507.jpg
[22:31:09] ✅ 成功复制: 1753626603375-1223281507.jpg
[22:31:09] 复制文件: 1753626603420--59820335.jpg
[22:31:09] ✅ 成功复制: 1753626603420--59820335.jpg
[22:31:09] 复制文件: 1753626603446--1882576375.jpg
[22:31:09] ✅ 成功复制: 1753626603446--1882576375.jpg
[22:31:09] 复制文件: 1753626603487--2039576347.jpg
[22:31:09] ✅ 成功复制: 1753626603487--2039576347.jpg
[22:31:09] 复制文件: 1753626603536--320016844.jpg
[22:31:09] ✅ 成功复制: 1753626603536--320016844.jpg
[22:31:09] 复制文件: 1753626603577--1854090630.jpg
[22:31:09] ✅ 成功复制: 1753626603577--1854090630.jpg
[22:31:09] 复制文件: 1753626660614--1428446268.jpg
[22:31:09] ✅ 成功复制: 1753626660614--1428446268.jpg
[22:31:09] 复制文件: 1753626660666-1209359625.jpg
[22:31:09] ✅ 成功复制: 1753626660666-1209359625.jpg
[22:31:09] 复制文件: 1753626660709-1902243945.jpg
[22:31:09] ✅ 成功复制: 1753626660709-1902243945.jpg
[22:31:09] 复制文件: 1753626660740-333858737.jpg
[22:31:09] ✅ 成功复制: 1753626660740-333858737.jpg
[22:31:09] 复制文件: 1753626660786-1052787518.jpg
[22:31:10] ✅ 成功复制: 1753626660786-1052787518.jpg
[22:31:10] 复制文件: 1753626660849-877359325.jpg
[22:31:10] ✅ 成功复制: 1753626660849-877359325.jpg
[22:31:10] 复制文件: 1753626660881-1230956995.jpg
[22:31:10] ✅ 成功复制: 1753626660881-1230956995.jpg
[22:31:10] 成功复制 49 个文件
[22:31:10] ✅ 成功复制 49 个图片到商品 3 文件夹
[22:31:10] 复制的文件:
[22:31:10]   - 1753626501373-1315797334.jpg
[22:31:10]   - 1753626501484-1645306700.jpg
[22:31:10]   - 1753626501256--945688330.jpg
[22:31:10]   - 1753626501297-483977703.jpg
[22:31:10]   - 1753626501401--1182398115.jpg
[22:31:10]   - 1753626501042--850234244.jpg
[22:31:10]   - 1753626500939-199319035.jpg
[22:31:10]   - 1753626501073--278957689.jpg
[22:31:10]   - 1753626501332-1706905320.jpg
[22:31:10]   - 1753626501176--1680485554.jpg
[22:31:10]   - 1753626501138--1193025293.jpg
[22:31:10]   - 1753626501207--778938302.jpg
[22:31:10]   - 1753626501435-1066972225.jpg
[22:31:10]   - 1753626518152-660672411.jpg
[22:31:10]   - 1753626518186-2125388435.jpg
[22:31:10]   - 1753626518219-1804250823.jpg
[22:31:10]   - 1753626518249--1065563707.jpg
[22:31:10]   - 1753626518295-152127411.jpg
[22:31:10]   - 1753626518357-1552964844.jpg
[22:31:10]   - 1753626518406-1752797653.jpg
[22:31:10]   - 1753626518441--1753262296.jpg
[22:31:10]   - 1753626518489-842254062.jpg
[22:31:10]   - 1753626518530--255483989.jpg
[22:31:10]   - 1753626518571--1285728568.jpg
[22:31:10]   - 1753626518627-396311468.jpg
[22:31:10]   - 1753626518662-1252897153.jpg
[22:31:10]   - 1753626518696--1537897804.jpg
[22:31:10]   - 1753626518746--1146868097.jpg
[22:31:10]   - 1753626518780--1712757656.jpg
[22:31:10]   - 1753626518827-184866696.jpg
[22:31:10]   - 1753626584506--174181552.jpg
[22:31:10]   - 1753626584568-649400088.jpg
[22:31:10]   - 1753626584616--75726748.jpg
[22:31:10]   - 1753626584693--1636531876.jpg
[22:31:10]   - 1753626584727-1492996970.jpg
[22:31:10]   - 1753626584762-2021427306.jpg
[22:31:10]   - 1753626603375-1223281507.jpg
[22:31:10]   - 1753626603420--59820335.jpg
[22:31:10]   - 1753626603446--1882576375.jpg
[22:31:10]   - 1753626603487--2039576347.jpg
[22:31:10]   - 1753626603536--320016844.jpg
[22:31:10]   - 1753626603577--1854090630.jpg
[22:31:10]   - 1753626660614--1428446268.jpg
[22:31:10]   - 1753626660666-1209359625.jpg
[22:31:10]   - 1753626660709-1902243945.jpg
[22:31:10]   - 1753626660740-333858737.jpg
[22:31:10]   - 1753626660786-1052787518.jpg
[22:31:10]   - 1753626660849-877359325.jpg
[22:31:10]   - 1753626660881-1230956995.jpg
[22:31:10] ⚠️  注意：由于MTP协议限制，无法自动删除手机上的原文件
[22:31:10] 请手动删除手机 DCIM/Pindd/goods 文件夹中的图片以释放空间
[22:31:10] ✅ 图片复制完成
[22:31:10] 步骤11: 删除手机上的原文件...
[22:31:10] 开始删除手机文件操作...
[22:31:10] 查找并点击GOODS.png...
[22:31:10] 正在查找图片: GOODS.png (路径: image\IQOO\GOODS.png)
[22:31:10] 查找或点击图片 GOODS.png 时出错: 
[22:31:10] ❌ 未找到GOODS.png图片
[22:31:10] ⚠️ 手机文件删除失败，请手动删除
[22:31:10] 步骤12: 开始处理主图...
[22:31:10] 步骤13: 开始第二轮操作（详情页）...
[22:31:10] 处理主图文件夹: 商品图片\3
[22:31:10] === 开始详情页图片捕获流程 ===
[22:31:10] 步骤13.1: 移动到(475,230)并点击右键...
[22:31:10] 找到 49 张图片
[22:31:10] 删除多余图片: 1753626501401--1182398115.jpg
[22:31:10] 删除多余图片: 1753626501042--850234244.jpg
[22:31:10] 删除多余图片: 1753626500939-199319035.jpg
[22:31:10] 删除多余图片: 1753626501073--278957689.jpg
[22:31:10] 删除多余图片: 1753626501332-1706905320.jpg
[22:31:10] 删除多余图片: 1753626501176--1680485554.jpg
[22:31:10] 删除多余图片: 1753626501138--1193025293.jpg
[22:31:10] 删除多余图片: 1753626501207--778938302.jpg
[22:31:10] 删除多余图片: 1753626501435-1066972225.jpg
[22:31:10] 删除多余图片: 1753626518152-660672411.jpg
[22:31:10] 删除多余图片: 1753626518186-2125388435.jpg
[22:31:10] 删除多余图片: 1753626518219-1804250823.jpg
[22:31:10] 删除多余图片: 1753626518249--1065563707.jpg
[22:31:10] 删除多余图片: 1753626518295-152127411.jpg
[22:31:10] 删除多余图片: 1753626518357-1552964844.jpg
[22:31:10] 删除多余图片: 1753626518406-1752797653.jpg
[22:31:10] 删除多余图片: 1753626518441--1753262296.jpg
[22:31:10] 删除多余图片: 1753626518489-842254062.jpg
[22:31:10] 删除多余图片: 1753626518530--255483989.jpg
[22:31:10] 删除多余图片: 1753626518571--1285728568.jpg
[22:31:10] 删除多余图片: 1753626518627-396311468.jpg
[22:31:10] 删除多余图片: 1753626518662-1252897153.jpg
[22:31:10] 删除多余图片: 1753626518696--1537897804.jpg
[22:31:10] 删除多余图片: 1753626518746--1146868097.jpg
[22:31:10] 删除多余图片: 1753626518780--1712757656.jpg
[22:31:10] 删除多余图片: 1753626518827-184866696.jpg
[22:31:10] 删除多余图片: 1753626584506--174181552.jpg
[22:31:10] 删除多余图片: 1753626584568-649400088.jpg
[22:31:10] 删除多余图片: 1753626584616--75726748.jpg
[22:31:10] 删除多余图片: 1753626584693--1636531876.jpg
[22:31:10] 删除多余图片: 1753626584727-1492996970.jpg
[22:31:10] 删除多余图片: 1753626584762-2021427306.jpg
[22:31:10] 删除多余图片: 1753626603375-1223281507.jpg
[22:31:10] 删除多余图片: 1753626603420--59820335.jpg
[22:31:10] 删除多余图片: 1753626603446--1882576375.jpg
[22:31:10] 删除多余图片: 1753626603487--2039576347.jpg
[22:31:10] 删除多余图片: 1753626603536--320016844.jpg
[22:31:10] 删除多余图片: 1753626603577--1854090630.jpg
[22:31:10] 删除多余图片: 1753626660614--1428446268.jpg
[22:31:10] 删除多余图片: 1753626660666-1209359625.jpg
[22:31:10] 删除多余图片: 1753626660709-1902243945.jpg
[22:31:10] 删除多余图片: 1753626660740-333858737.jpg
[22:31:10] 删除多余图片: 1753626660786-1052787518.jpg
[22:31:10] 删除多余图片: 1753626660849-877359325.jpg
[22:31:10] 删除多余图片: 1753626660881-1230956995.jpg
[22:31:10] 重命名: 1753626501373-1315797334.jpg → 主图1.jpg
[22:31:10] 重命名: 1753626501484-1645306700.jpg → 主图2.jpg
[22:31:10] 重命名: 1753626501256--945688330.jpg → 主图3.jpg
[22:31:10] 重命名: 1753626501297-483977703.jpg → 主图4.jpg
[22:31:10] ✅ 主图处理完成
[22:31:12] 步骤13.2: 使用AutoHotkey向下滚动40次...
[22:31:12] 执行AutoHotkey脚本: scroll_down.ahk
[22:31:16] ✅ AutoHotkey脚本执行成功: scroll_down
[22:31:16] ✅ AutoHotkey滚动执行成功
[22:31:16] 步骤13.3: 移动到(477,300)并点击左键...
[22:31:17] ✅ 点击操作完成
[22:31:17] 步骤13.4: 移动到(480,495)并长按0.5秒...
[22:31:18] ✅ 长按操作完成
[22:31:19] 步骤13.5: 查找并点击保存按钮...
[22:31:19] 尝试查找图片 BAOCUN1.png (第1次/共3次)
[22:31:19] 正在查找图片: BAOCUN1.png (路径: image\IQOO\BAOCUN1.png)
[22:31:19] 找到图片 BAOCUN1.png，位置: (454, 808)
[22:31:20] 移动到图片上，等待0.5秒...
[22:31:20] 已点击图片: BAOCUN1.png
[22:31:20] 步骤13.6: 检测详情页保存状态...
[22:31:20] 开始检测图片: BAOCUNZHONG.png (路径: image\IQOO\BAOCUNZHONG.png)
[22:31:20] 最多检测 80 次，每隔 0.2 秒检测一次
[22:31:20] 第 1/80 次检测...
[22:31:21] 第 2/80 次检测...
[22:31:21] 第 3/80 次检测...
[22:31:21] ✅ 找到图片 BAOCUNZHONG.png！位置: (443, 507)
[22:31:21] ✅ 检测到详情页保存中状态，等待保存完成...
[22:31:26] 步骤13.7: 复制详情页图片...
[22:31:26] 第 1/3 次尝试复制详情页图片...
[22:31:26] 开始从MTP设备复制图片到商品 3 文件夹...
[22:31:26] 正在使用Windows Shell API查找MTP设备...
[22:31:26] 找到设备: iQOO Z1
[22:31:26] 进入文件夹: 内部存储设备
[22:31:26] 进入文件夹: DCIM
[22:31:26] 进入文件夹: Pindd
[22:31:26] 进入文件夹: goods
[22:31:26] 成功找到目标文件夹: 内部存储设备\DCIM\Pindd\goods
[22:31:26] 目标路径: F:\自动捕获数据\商品图片\3
[22:31:26] 复制文件: 1753626501373-1315797334.jpg
[22:31:26] ✅ 成功复制: 1753626501373-1315797334.jpg
[22:31:26] 复制文件: 1753626501484-1645306700.jpg
[22:31:26] ✅ 成功复制: 1753626501484-1645306700.jpg
[22:31:26] 复制文件: 1753626501256--945688330.jpg
[22:31:26] ✅ 成功复制: 1753626501256--945688330.jpg
[22:31:26] 复制文件: 1753626501297-483977703.jpg
[22:31:26] ✅ 成功复制: 1753626501297-483977703.jpg
[22:31:26] 复制文件: 1753626501401--1182398115.jpg
[22:31:26] ✅ 成功复制: 1753626501401--1182398115.jpg
[22:31:26] 复制文件: 1753626501042--850234244.jpg
[22:31:27] ✅ 成功复制: 1753626501042--850234244.jpg
[22:31:27] 复制文件: 1753626500939-199319035.jpg
[22:31:27] ✅ 成功复制: 1753626500939-199319035.jpg
[22:31:27] 复制文件: 1753626501073--278957689.jpg
[22:31:27] ✅ 成功复制: 1753626501073--278957689.jpg
[22:31:27] 复制文件: 1753626501332-1706905320.jpg
[22:31:27] ✅ 成功复制: 1753626501332-1706905320.jpg
[22:31:27] 复制文件: 1753626501176--1680485554.jpg
[22:31:27] ✅ 成功复制: 1753626501176--1680485554.jpg
[22:31:27] 复制文件: 1753626501138--1193025293.jpg
[22:31:27] ✅ 成功复制: 1753626501138--1193025293.jpg
[22:31:27] 复制文件: 1753626501207--778938302.jpg
[22:31:27] ✅ 成功复制: 1753626501207--778938302.jpg
[22:31:27] 复制文件: 1753626501435-1066972225.jpg
[22:31:27] ✅ 成功复制: 1753626501435-1066972225.jpg
[22:31:27] 复制文件: 1753626518152-660672411.jpg
[22:31:27] ✅ 成功复制: 1753626518152-660672411.jpg
[22:31:27] 复制文件: 1753626518186-2125388435.jpg
[22:31:27] ✅ 成功复制: 1753626518186-2125388435.jpg
[22:31:27] 复制文件: 1753626518219-1804250823.jpg
[22:31:27] ✅ 成功复制: 1753626518219-1804250823.jpg
[22:31:27] 复制文件: 1753626518249--1065563707.jpg
[22:31:27] ✅ 成功复制: 1753626518249--1065563707.jpg
[22:31:27] 复制文件: 1753626518295-152127411.jpg
[22:31:27] ✅ 成功复制: 1753626518295-152127411.jpg
[22:31:27] 复制文件: 1753626518357-1552964844.jpg
[22:31:27] ✅ 成功复制: 1753626518357-1552964844.jpg
[22:31:27] 复制文件: 1753626518406-1752797653.jpg
[22:31:27] ✅ 成功复制: 1753626518406-1752797653.jpg
[22:31:27] 复制文件: 1753626518441--1753262296.jpg
[22:31:28] ✅ 成功复制: 1753626518441--1753262296.jpg
[22:31:28] 复制文件: 1753626518489-842254062.jpg
[22:31:28] ✅ 成功复制: 1753626518489-842254062.jpg
[22:31:28] 复制文件: 1753626518530--255483989.jpg
[22:31:28] ✅ 成功复制: 1753626518530--255483989.jpg
[22:31:28] 复制文件: 1753626518571--1285728568.jpg
[22:31:28] ✅ 成功复制: 1753626518571--1285728568.jpg
[22:31:28] 复制文件: 1753626518627-396311468.jpg
[22:31:28] ✅ 成功复制: 1753626518627-396311468.jpg
[22:31:28] 复制文件: 1753626518662-1252897153.jpg
[22:31:28] ✅ 成功复制: 1753626518662-1252897153.jpg
[22:31:28] 复制文件: 1753626518696--1537897804.jpg
[22:31:28] ✅ 成功复制: 1753626518696--1537897804.jpg
[22:31:28] 复制文件: 1753626518746--1146868097.jpg
[22:31:28] ✅ 成功复制: 1753626518746--1146868097.jpg
[22:31:28] 复制文件: 1753626518780--1712757656.jpg
[22:31:28] ✅ 成功复制: 1753626518780--1712757656.jpg
[22:31:28] 复制文件: 1753626518827-184866696.jpg
[22:31:28] ✅ 成功复制: 1753626518827-184866696.jpg
[22:31:28] 复制文件: 1753626584506--174181552.jpg
[22:31:28] ✅ 成功复制: 1753626584506--174181552.jpg
[22:31:28] 复制文件: 1753626584568-649400088.jpg
[22:31:28] ✅ 成功复制: 1753626584568-649400088.jpg
[22:31:28] 复制文件: 1753626584616--75726748.jpg
[22:31:28] ✅ 成功复制: 1753626584616--75726748.jpg
[22:31:28] 复制文件: 1753626584693--1636531876.jpg
[22:31:28] ✅ 成功复制: 1753626584693--1636531876.jpg
[22:31:28] 复制文件: 1753626584727-1492996970.jpg
[22:31:29] ✅ 成功复制: 1753626584727-1492996970.jpg
[22:31:29] 复制文件: 1753626584762-2021427306.jpg
[22:31:29] ✅ 成功复制: 1753626584762-2021427306.jpg
[22:31:29] 复制文件: 1753626603375-1223281507.jpg
[22:31:29] ✅ 成功复制: 1753626603375-1223281507.jpg
[22:31:29] 复制文件: 1753626603420--59820335.jpg
[22:31:29] ✅ 成功复制: 1753626603420--59820335.jpg
[22:31:29] 复制文件: 1753626603446--1882576375.jpg
[22:31:29] ✅ 成功复制: 1753626603446--1882576375.jpg
[22:31:29] 复制文件: 1753626603487--2039576347.jpg
[22:31:29] ✅ 成功复制: 1753626603487--2039576347.jpg
[22:31:29] 复制文件: 1753626603536--320016844.jpg
[22:31:29] ✅ 成功复制: 1753626603536--320016844.jpg
[22:31:29] 复制文件: 1753626603577--1854090630.jpg
[22:31:29] ✅ 成功复制: 1753626603577--1854090630.jpg
[22:31:29] 复制文件: 1753626660614--1428446268.jpg
[22:31:29] ✅ 成功复制: 1753626660614--1428446268.jpg
[22:31:29] 复制文件: 1753626660666-1209359625.jpg
[22:31:29] ✅ 成功复制: 1753626660666-1209359625.jpg
[22:31:29] 复制文件: 1753626660709-1902243945.jpg
[22:31:29] ✅ 成功复制: 1753626660709-1902243945.jpg
[22:31:29] 复制文件: 1753626660740-333858737.jpg
[22:31:29] ✅ 成功复制: 1753626660740-333858737.jpg
[22:31:29] 复制文件: 1753626660786-1052787518.jpg
[22:31:29] ✅ 成功复制: 1753626660786-1052787518.jpg
[22:31:29] 复制文件: 1753626660849-877359325.jpg
[22:31:30] ✅ 成功复制: 1753626660849-877359325.jpg
[22:31:30] 复制文件: 1753626660881-1230956995.jpg
[22:31:30] ✅ 成功复制: 1753626660881-1230956995.jpg
[22:31:30] 复制文件: 1753626680297--251064384.jpg
[22:31:30] ✅ 成功复制: 1753626680297--251064384.jpg
[22:31:30] 复制文件: 1753626680360-1069102764.jpg
[22:31:30] ✅ 成功复制: 1753626680360-1069102764.jpg
[22:31:30] 复制文件: 1753626680401--482777265.jpg
[22:31:30] ✅ 成功复制: 1753626680401--482777265.jpg
[22:31:30] 复制文件: 1753626680449--1838772425.jpg
[22:31:30] ✅ 成功复制: 1753626680449--1838772425.jpg
[22:31:30] 复制文件: 1753626680498--618659008.jpg
[22:31:30] ✅ 成功复制: 1753626680498--618659008.jpg
[22:31:30] 复制文件: 1753626680546--1085139102.jpg
[22:31:30] ✅ 成功复制: 1753626680546--1085139102.jpg
[22:31:30] 复制文件: 1753626680588-1076043545.jpg
[22:31:30] ✅ 成功复制: 1753626680588-1076043545.jpg
[22:31:30] 复制文件: 1753626680636-552573400.jpg
[22:31:30] ✅ 成功复制: 1753626680636-552573400.jpg
[22:31:30] 复制文件: 1753626680677--1331103116.jpg
[22:31:30] ✅ 成功复制: 1753626680677--1331103116.jpg
[22:31:30] 成功复制 58 个文件
[22:31:30] ✅ 成功复制 58 个图片到商品 3 文件夹
[22:31:30] 复制的文件:
[22:31:30]   - 1753626501373-1315797334.jpg
[22:31:30]   - 1753626501484-1645306700.jpg
[22:31:30]   - 1753626501256--945688330.jpg
[22:31:30]   - 1753626501297-483977703.jpg
[22:31:30]   - 1753626501401--1182398115.jpg
[22:31:30]   - 1753626501042--850234244.jpg
[22:31:30]   - 1753626500939-199319035.jpg
[22:31:30]   - 1753626501073--278957689.jpg
[22:31:30]   - 1753626501332-1706905320.jpg
[22:31:30]   - 1753626501176--1680485554.jpg
[22:31:30]   - 1753626501138--1193025293.jpg
[22:31:30]   - 1753626501207--778938302.jpg
[22:31:30]   - 1753626501435-1066972225.jpg
[22:31:30]   - 1753626518152-660672411.jpg
[22:31:30]   - 1753626518186-2125388435.jpg
[22:31:30]   - 1753626518219-1804250823.jpg
[22:31:30]   - 1753626518249--1065563707.jpg
[22:31:30]   - 1753626518295-152127411.jpg
[22:31:30]   - 1753626518357-1552964844.jpg
[22:31:30]   - 1753626518406-1752797653.jpg
[22:31:30]   - 1753626518441--1753262296.jpg
[22:31:30]   - 1753626518489-842254062.jpg
[22:31:30]   - 1753626518530--255483989.jpg
[22:31:30]   - 1753626518571--1285728568.jpg
[22:31:30]   - 1753626518627-396311468.jpg
[22:31:30]   - 1753626518662-1252897153.jpg
[22:31:30]   - 1753626518696--1537897804.jpg
[22:31:30]   - 1753626518746--1146868097.jpg
[22:31:30]   - 1753626518780--1712757656.jpg
[22:31:30]   - 1753626518827-184866696.jpg
[22:31:30]   - 1753626584506--174181552.jpg
[22:31:30]   - 1753626584568-649400088.jpg
[22:31:30]   - 1753626584616--75726748.jpg
[22:31:30]   - 1753626584693--1636531876.jpg
[22:31:30]   - 1753626584727-1492996970.jpg
[22:31:30]   - 1753626584762-2021427306.jpg
[22:31:30]   - 1753626603375-1223281507.jpg
[22:31:30]   - 1753626603420--59820335.jpg
[22:31:30]   - 1753626603446--1882576375.jpg
[22:31:30]   - 1753626603487--2039576347.jpg
[22:31:30]   - 1753626603536--320016844.jpg
[22:31:30]   - 1753626603577--1854090630.jpg
[22:31:30]   - 1753626660614--1428446268.jpg
[22:31:30]   - 1753626660666-1209359625.jpg
[22:31:30]   - 1753626660709-1902243945.jpg
[22:31:30]   - 1753626660740-333858737.jpg
[22:31:30]   - 1753626660786-1052787518.jpg
[22:31:30]   - 1753626660849-877359325.jpg
[22:31:30]   - 1753626660881-1230956995.jpg
[22:31:30]   - 1753626680297--251064384.jpg
[22:31:30]   - 1753626680360-1069102764.jpg
[22:31:30]   - 1753626680401--482777265.jpg
[22:31:30]   - 1753626680449--1838772425.jpg
[22:31:30]   - 1753626680498--618659008.jpg
[22:31:30]   - 1753626680546--1085139102.jpg
[22:31:30]   - 1753626680588-1076043545.jpg
[22:31:30]   - 1753626680636-552573400.jpg
[22:31:30]   - 1753626680677--1331103116.jpg
[22:31:30] ⚠️  注意：由于MTP协议限制，无法自动删除手机上的原文件
[22:31:30] 请手动删除手机 DCIM/Pindd/goods 文件夹中的图片以释放空间
[22:31:30] ✅ 详情页图片复制完成
[22:31:30] 步骤13.8: 删除手机上的详情页图片...
[22:31:30] 开始删除手机文件操作...
[22:31:30] 查找并点击GOODS.png...
[22:31:30] 正在查找图片: GOODS.png (路径: image\IQOO\GOODS.png)
[22:31:31] 查找或点击图片 GOODS.png 时出错: 
[22:31:31] ❌ 未找到GOODS.png图片
[22:31:31] ⚠️ 手机详情页文件删除失败，请手动删除
[22:31:31] 步骤13.8: 处理详情页图片...
[22:31:31] 处理详情页图片文件夹: 商品图片\3
[22:31:31] 跳过已处理的主图: 主图1.jpg
[22:31:31] 跳过已处理的主图: 主图2.jpg
[22:31:31] 跳过已处理的主图: 主图3.jpg
[22:31:31] 跳过已处理的主图: 主图4.jpg
[22:31:31] 找到 58 张详情页图片
[22:31:31] 删除多余详情页图片: 1753626584506--174181552.jpg
[22:31:31] 删除多余详情页图片: 1753626584568-649400088.jpg
[22:31:31] 删除多余详情页图片: 1753626584616--75726748.jpg
[22:31:31] 删除多余详情页图片: 1753626584693--1636531876.jpg
[22:31:31] 删除多余详情页图片: 1753626584727-1492996970.jpg
[22:31:31] 删除多余详情页图片: 1753626584762-2021427306.jpg
[22:31:31] 删除多余详情页图片: 1753626603375-1223281507.jpg
[22:31:31] 删除多余详情页图片: 1753626603420--59820335.jpg
[22:31:31] 删除多余详情页图片: 1753626603446--1882576375.jpg
[22:31:31] 删除多余详情页图片: 1753626603487--2039576347.jpg
[22:31:31] 删除多余详情页图片: 1753626603536--320016844.jpg
[22:31:31] 删除多余详情页图片: 1753626603577--1854090630.jpg
[22:31:31] 删除多余详情页图片: 1753626660614--1428446268.jpg
[22:31:31] 删除多余详情页图片: 1753626660666-1209359625.jpg
[22:31:31] 删除多余详情页图片: 1753626660709-1902243945.jpg
[22:31:31] 删除多余详情页图片: 1753626660740-333858737.jpg
[22:31:31] 删除多余详情页图片: 1753626660786-1052787518.jpg
[22:31:31] 删除多余详情页图片: 1753626660849-877359325.jpg
[22:31:31] 删除多余详情页图片: 1753626660881-1230956995.jpg
[22:31:31] 删除多余详情页图片: 1753626680297--251064384.jpg
[22:31:31] 删除多余详情页图片: 1753626680360-1069102764.jpg
[22:31:31] 删除多余详情页图片: 1753626680401--482777265.jpg
[22:31:31] 删除多余详情页图片: 1753626680449--1838772425.jpg
[22:31:31] 删除多余详情页图片: 1753626680498--618659008.jpg
[22:31:31] 删除多余详情页图片: 1753626680546--1085139102.jpg
[22:31:31] 删除多余详情页图片: 1753626680588-1076043545.jpg
[22:31:31] 删除多余详情页图片: 1753626680636-552573400.jpg
[22:31:31] 删除多余详情页图片: 1753626680677--1331103116.jpg
[22:31:31] 重命名详情页图片: 1753626501373-1315797334.jpg → 1.jpg
[22:31:31] 重命名详情页图片: 1753626501484-1645306700.jpg → 2.jpg
[22:31:31] 重命名详情页图片: 1753626501256--945688330.jpg → 3.jpg
[22:31:31] 重命名详情页图片: 1753626501297-483977703.jpg → 4.jpg
[22:31:31] 重命名详情页图片: 1753626501401--1182398115.jpg → 5.jpg
[22:31:31] 重命名详情页图片: 1753626501042--850234244.jpg → 6.jpg
[22:31:31] 重命名详情页图片: 1753626500939-199319035.jpg → 7.jpg
[22:31:31] 重命名详情页图片: 1753626501073--278957689.jpg → 8.jpg
[22:31:31] 重命名详情页图片: 1753626501332-1706905320.jpg → 9.jpg
[22:31:31] 重命名详情页图片: 1753626501176--1680485554.jpg → 10.jpg
[22:31:31] 重命名详情页图片: 1753626501138--1193025293.jpg → 11.jpg
[22:31:31] 重命名详情页图片: 1753626501207--778938302.jpg → 12.jpg
[22:31:31] 重命名详情页图片: 1753626501435-1066972225.jpg → 13.jpg
[22:31:31] 重命名详情页图片: 1753626518152-660672411.jpg → 14.jpg
[22:31:31] 重命名详情页图片: 1753626518186-2125388435.jpg → 15.jpg
[22:31:31] 重命名详情页图片: 1753626518219-1804250823.jpg → 16.jpg
[22:31:31] 重命名详情页图片: 1753626518249--1065563707.jpg → 17.jpg
[22:31:31] 重命名详情页图片: 1753626518295-152127411.jpg → 18.jpg
[22:31:31] 重命名详情页图片: 1753626518357-1552964844.jpg → 19.jpg
[22:31:31] 重命名详情页图片: 1753626518406-1752797653.jpg → 20.jpg
[22:31:31] 重命名详情页图片: 1753626518441--1753262296.jpg → 21.jpg
[22:31:31] 重命名详情页图片: 1753626518489-842254062.jpg → 22.jpg
[22:31:31] 重命名详情页图片: 1753626518530--255483989.jpg → 23.jpg
[22:31:31] 重命名详情页图片: 1753626518571--1285728568.jpg → 24.jpg
[22:31:31] 重命名详情页图片: 1753626518627-396311468.jpg → 25.jpg
[22:31:31] 重命名详情页图片: 1753626518662-1252897153.jpg → 26.jpg
[22:31:31] 重命名详情页图片: 1753626518696--1537897804.jpg → 27.jpg
[22:31:31] 重命名详情页图片: 1753626518746--1146868097.jpg → 28.jpg
[22:31:31] 重命名详情页图片: 1753626518780--1712757656.jpg → 29.jpg
[22:31:31] 重命名详情页图片: 1753626518827-184866696.jpg → 30.jpg
[22:31:31] ✅ 详情页图片处理完成
[22:31:31] ✅ 主图保持'主图X'格式，详情页图片重命名为1-30
[22:31:31] ✅ 详情页图片处理完成
[22:31:31] 步骤13.9: 执行最后的鼠标操作...
[22:31:31] 开始执行最后的鼠标操作序列...
[22:31:31] 移动到位置(475,125)...
[22:31:31] 点击右键...
[22:31:32] 等待1秒...
[22:31:33] 移动到位置(670,940)...
[22:31:33] 点击左键...
[22:31:33] 等待2.5秒让页面加载完成...
[22:31:36] ✅ 最后的鼠标操作序列完成
[22:31:36] 步骤13.10: 执行智能OCR分析...
[22:31:36] ==================================================
[22:31:36] 🧠 智能OCR分析开始
[22:31:36] ==================================================
[22:31:36] 🔍 开始智能OCR分析屏幕区域: (270, 230) 到 (691, 883)
[22:31:36] ✅ OCR截图已保存: 商品图片\3.jpg
[22:31:36] 正在执行OCR识别...
[22:31:38] OCR原始结果类型: <class 'list'>
[22:31:38] OCR原始结果长度: 1
[22:31:38] 第一页结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[22:31:38] OCRResult对象属性: ['_save_funcs', '_json_writer', '_rand_fn', '_img_writer']
[22:31:38]   _save_funcs: <class 'list'> - [<bound method JsonMixin.save_to_json of {'input_path': None, 'page_index': None, 'doc_preprocessor_...
[22:31:38]   _json_writer: <class 'paddlex.inference.utils.io.writers.JsonWriter'> - <paddlex.inference.utils.io.writers.JsonWriter object at 0x00000200E8FDDB50>
[22:31:38]   _rand_fn: <class 'NoneType'> - None
[22:31:38]   _img_writer: <class 'paddlex.inference.utils.io.writers.ImageWriter'> - <paddlex.inference.utils.io.writers.ImageWriter object at 0x00000200E8FE7830>
[22:31:38] 方式2成功：识别到 20 个文本
[22:31:38] 文本 0: '¥21.9-29.99', 置信度: 0.8353
[22:31:38] OCR识别: ¥21.9-29.99 (置信度: 0.8353)
[22:31:38] 文本 1: '1', 置信度: 0.9954
[22:31:38] OCR识别: 1 (置信度: 0.9954)
[22:31:38] 文本 2: '满50减2', 置信度: 0.9990
[22:31:38] OCR识别: 满50减2 (置信度: 0.9990)
[22:31:38] 文本 3: '请选择：参考分类', 置信度: 0.9889
[22:31:38] OCR识别: 请选择：参考分类 (置信度: 0.9889)
[22:31:38] 文本 4: '优惠', 置信度: 0.9997
[22:31:38] OCR识别: 优惠 (置信度: 0.9997)
[22:31:38] 文本 5: '选择款式后查看优惠', 置信度: 0.9983
[22:31:38] OCR识别: 选择款式后查看优惠 (置信度: 0.9983)
[22:31:38] 文本 6: '颜色分类', 置信度: 0.9998
[22:31:38] OCR识别: 颜色分类 (置信度: 0.9998)
[22:31:38] 文本 7: '蓝色狮子套装', 置信度: 0.9988
[22:31:38] OCR识别: 蓝色狮子套装 (置信度: 0.9988)
[22:31:38] 文本 8: '咖色狮子套装', 置信度: 0.9991
[22:31:38] OCR识别: 咖色狮子套装 (置信度: 0.9991)
[22:31:38] 文本 9: '参考分类', 置信度: 0.9999
[22:31:38] OCR识别: 参考分类 (置信度: 0.9999)
[22:31:38] 文本 10: '80￥29.99', 置信度: 0.9574
[22:31:38] OCR识别: 80￥29.99 (置信度: 0.9574)
[22:31:38] 文本 11: '90￥29.99', 置信度: 0.9615
[22:31:38] OCR识别: 90￥29.99 (置信度: 0.9615)
[22:31:38] 文本 12: '100￥29.99', 置信度: 0.9475
[22:31:38] OCR识别: 100￥29.99 (置信度: 0.9475)
[22:31:38] 文本 13: '110￥29.99', 置信度: 0.9597
[22:31:38] OCR识别: 110￥29.99 (置信度: 0.9597)
[22:31:38] 文本 14: '120￥29.99', 置信度: 0.9451
[22:31:38] OCR识别: 120￥29.99 (置信度: 0.9451)
[22:31:38] 文本 15: '来样定制￥21.9', 置信度: 0.9760
[22:31:38] OCR识别: 来样定制￥21.9 (置信度: 0.9760)
[22:31:38] 文本 16: '带图定制￥21.9', 置信度: 0.9744
[22:31:38] OCR识别: 带图定制￥21.9 (置信度: 0.9744)
[22:31:38] 文本 17: '免费服务', 置信度: 0.9998
[22:31:38] OCR识别: 免费服务 (置信度: 0.9998)
[22:31:38] 文本 18: '退货包运费(商家赠送)', 置信度: 0.9664
[22:31:38] OCR识别: 退货包运费(商家赠送) (置信度: 0.9664)
[22:31:38] 文本 19: '一次选多款，不满意可极速退款>', 置信度: 0.9707
[22:31:38] OCR识别: 一次选多款，不满意可极速退款> (置信度: 0.9707)
[22:31:38] ✅ OCR识别完成，共识别到 20 个文本
[22:31:38] 💾 保存OCR结果到文件...
[22:31:38] ✅ OCR结果已保存: OCR\商品3_OCR结果.txt
[22:31:38] 🧠 开始智能分析商品信息...
[22:31:38] 🔧 开始智能拼接被分割的文本...
[22:31:38] 🎯 找到上方紧贴文本:
[22:31:38]    上方文本: '¥21.9-29.99' Y范围: 8.0-39.0, X范围: 1.0-124.0
[22:31:38]    当前文本: '满50减2' Y范围: 38.0-64.0, X起始: 5.0
[22:31:38]    垂直间距: 1.0px, X重叠: True
[22:31:38] ✅ 向上拼接: '¥21.9-29.99' + '满50减2' = '¥21.9-29.99满50减2'
[22:31:38] 🔗 完成向上拼接:
[22:31:38]    结果: '¥21.9-29.99满50减2'
[22:31:38] 🎯 找到上方紧贴文本:
[22:31:38]    上方文本: '满50减2' Y范围: 38.0-64.0, X范围: 5.0-70.0
[22:31:38]    当前文本: '请选择：参考分类' Y范围: 70.0-89.0, X起始: 4.0
[22:31:38]    垂直间距: 6.0px, X重叠: True
[22:31:38] ✅ 向上拼接: '满50减2' + '请选择：参考分类' = '满50减2请选择：参考分类'
[22:31:38] 🔗 完成向上拼接:
[22:31:38]    结果: '满50减2请选择：参考分类'
[22:31:38] 🎯 找到上方紧贴文本:
[22:31:38]    上方文本: '优惠' Y范围: 107.0-136.0, X范围: 1.0-45.0
[22:31:38]    当前文本: '颜色分类' Y范围: 151.0-178.0, X起始: 2.0
[22:31:38]    垂直间距: 15.0px, X重叠: True
[22:31:38] ✅ 向上拼接: '优惠' + '颜色分类' = '优惠颜色分类'
[22:31:38] 🔗 完成向上拼接:
[22:31:38]    结果: '优惠颜色分类'
[22:31:38] 🎯 找到上方紧贴文本:
[22:31:38]    上方文本: '参考分类' Y范围: 359.0-385.0, X范围: 2.0-73.0
[22:31:38]    当前文本: '80￥29.99' Y范围: 397.0-416.0, X起始: 11.0
[22:31:38]    垂直间距: 12.0px, X重叠: True
[22:31:38] ✅ 向上拼接: '参考分类' + '80￥29.99' = '参考分类80￥29.99'
[22:31:38] 🔗 完成向上拼接:
[22:31:38]    结果: '参考分类80￥29.99'
[22:31:38] 🎯 找到上方紧贴文本:
[22:31:38]    上方文本: '参考分类' Y范围: 359.0-385.0, X范围: 2.0-73.0
[22:31:38]    当前文本: '90￥29.99' Y范围: 397.0-416.0, X起始: 101.0
[22:31:38]    垂直间距: 12.0px, X重叠: True
[22:31:38] ✅ 向上拼接: '参考分类' + '90￥29.99' = '参考分类90￥29.99'
[22:31:38] 🔗 完成向上拼接:
[22:31:38]    结果: '参考分类90￥29.99'
[22:31:38] 🎯 找到上方紧贴文本:
[22:31:38]    上方文本: '免费服务' Y范围: 485.0-510.0, X范围: 2.0-73.0
[22:31:38]    当前文本: '退货包运费(商家赠送)' Y范围: 522.0-544.0, X起始: 13.0
[22:31:38]    垂直间距: 12.0px, X重叠: True
[22:31:38] ✅ 向上拼接: '免费服务' + '退货包运费(商家赠送)' = '免费服务退货包运费(商家赠送)'
[22:31:38] 🔗 完成向上拼接:
[22:31:38]    结果: '免费服务退货包运费(商家赠送)'
[22:31:38] 📊 拼接结果: 原始20个文本 → 拼接后16个文本
[22:31:38] 📝 拼接后的文本列表:
[22:31:38]    1. '1'
[22:31:38]    2. '¥21.9-29.99满50减2'
[22:31:38]    3. '满50减2请选择：参考分类'
[22:31:38]    4. '选择款式后查看优惠'
[22:31:38]    5. '优惠颜色分类'
[22:31:38]    6. '蓝色狮子套装'
[22:31:38]    7. '咖色狮子套装'
[22:31:38]    8. '参考分类80￥29.99'
[22:31:38]    9. '参考分类90￥29.99'
[22:31:38]    10. '100￥29.99'
[22:31:38]    11. '110￥29.99'
[22:31:38]    12. '120￥29.99'
[22:31:38]    13. '来样定制￥21.9'
[22:31:38]    14. '带图定制￥21.9'
[22:31:38]    15. '免费服务退货包运费(商家赠送)'
[22:31:38]    16. '一次选多款，不满意可极速退款>'
[22:31:38] 🎯 找到尺码区域开始位置: 第3行 '满50减2请选择：参考分类'
[22:31:38] 📝 尺码区域文本无数字: '选择款式后查看优惠'
[22:31:38] 📝 尺码区域文本无数字: '优惠颜色分类'
[22:31:38] 📝 尺码区域文本无数字: '蓝色狮子套装'
[22:31:38] 📝 尺码区域文本无数字: '咖色狮子套装'
[22:31:38] 🔧 过滤￥符号后的文本: '参考分类80'
[22:31:38] ✅ 提取尺码: 80 (来源: '参考分类80')
[22:31:38] 🔍 处理bbox: [2, 359, 73, 385] (长度: 4)
[22:31:38] ✅ 计算坐标成功: (37, 372)
[22:31:38] 📍 记录尺码坐标: 80 -> (37, 372)
[22:31:38] 🔧 过滤￥符号后的文本: '参考分类90'
[22:31:38] ✅ 提取尺码: 90 (来源: '参考分类90')
[22:31:38] 🔍 处理bbox: [2, 359, 73, 385] (长度: 4)
[22:31:38] ✅ 计算坐标成功: (37, 372)
[22:31:38] 📍 记录尺码坐标: 90 -> (37, 372)
[22:31:38] 🔧 过滤￥符号后的文本: '100'
[22:31:38] ✅ 提取尺码: 100 (来源: '100')
[22:31:38] 🔍 处理bbox: [189, 397, 266, 416] (长度: 4)
[22:31:38] ✅ 计算坐标成功: (227, 406)
[22:31:38] 📍 记录尺码坐标: 100 -> (227, 406)
[22:31:38] 🔧 过滤￥符号后的文本: '110'
[22:31:38] ✅ 提取尺码: 110 (来源: '110')
[22:31:38] 🔍 处理bbox: [286, 397, 362, 416] (长度: 4)
[22:31:38] ✅ 计算坐标成功: (324, 406)
[22:31:38] 📍 记录尺码坐标: 110 -> (324, 406)
[22:31:38] 🔧 过滤￥符号后的文本: '120'
[22:31:38] ✅ 提取尺码: 120 (来源: '120')
[22:31:38] 🔍 处理bbox: [10, 437, 87, 456] (长度: 4)
[22:31:38] ✅ 计算坐标成功: (48, 446)
[22:31:38] 📍 记录尺码坐标: 120 -> (48, 446)
[22:31:38] 🔧 过滤￥符号后的文本: '来样定制'
[22:31:38] 📝 尺码区域文本无数字: '来样定制'
[22:31:38] 🔧 过滤￥符号后的文本: '带图定制'
[22:31:38] 📝 尺码区域文本无数字: '带图定制'
[22:31:38] 🛑 遇到停止关键词，结束尺码提取: '免费服务退货包运费(商家赠送)'
[22:31:38] 📊 尺码提取结果: 数字=[80, 90, 100, 110, 120], 范围=80-120, 原始文本数量=5
[22:31:38] 尺码信息: {'optimized_range': '80-120', 'original_texts': ['参考分类80', '参考分类90', '100', '110', '120'], 'size_numbers': [80, 90, 100, 110, 120]}
[22:31:38] 📍 已记录尺码坐标: {100: (227, 406), 110: (324, 406), 120: (48, 446), 130: (282, 336), 140: (344, 336), 150: (90, 455), 90: (37, 372), 80: (37, 372)}
[22:31:38] 🔍 发现颜色嵌入格式: '优惠颜色分类' → 提取: '分类'
[22:31:38] 🎯 发现嵌入颜色分类: '分类' (来源: '优惠颜色分类')
[22:31:38] 🎯 找到颜色分类开始位置: 第4行 '优惠颜色分类'
[22:31:38] 🎯 找到颜色分类结束位置: 第7行 '参考分类80￥29.99'
[22:31:38] 🔍 开始提取颜色分类: 从第5行到第6行
[22:31:38] ✅ 保留有效文本: '蓝色狮子套装'
[22:31:38] ✅ 保留有效文本: '咖色狮子套装'
[22:31:38] ✅ 添加嵌入颜色分类: '分类'
[22:31:38] 🔍 检查颜色文本: '蓝色狮子套装' (长度: 6)
[22:31:38] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [ 33 ... 328]
[22:31:38] 🔍 价格检测结果: False
[22:31:38] 🔍 处理bbox: [33, 309, 120, 328] (长度: 4)
[22:31:38] ✅ 计算坐标成功: (76, 318)
[22:31:38] 📍 记录颜色坐标: 蓝色狮子套装 -> (76, 318)
[22:31:38] ✅ 提取到无价格颜色: 蓝色狮子套装
[22:31:38] 🔍 检查颜色文本: '咖色狮子套装' (长度: 6)
[22:31:38] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [166 ... 328]
[22:31:38] 🔍 价格检测结果: False
[22:31:38] 🔍 处理bbox: [166, 308, 252, 328] (长度: 4)
[22:31:38] ✅ 计算坐标成功: (209, 318)
[22:31:38] 📍 记录颜色坐标: 咖色狮子套装 -> (209, 318)
[22:31:38] ✅ 提取到无价格颜色: 咖色狮子套装
[22:31:38] 🔍 检查颜色文本: '分类' (长度: 2)
[22:31:38] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [  1 ... 136]
[22:31:38] 🔍 价格检测结果: False
[22:31:38] 🔍 处理bbox: [1, 107, 45, 136] (长度: 4)
[22:31:38] ✅ 计算坐标成功: (23, 121)
[22:31:38] 📍 记录颜色坐标: 分类 -> (23, 121)
[22:31:38] ✅ 提取到无价格颜色: 分类
[22:31:38] 🎨 颜色分类提取完成: 共提取到 3 个颜色
[22:31:38] 📍 坐标记录完成: 共记录 3 个坐标
[22:31:38] 提取到颜色分类: [{'pure_name': '蓝色狮子套装', 'original_text': '蓝色狮子套装', 'has_direct_price': False, 'direct_price': None, 'bbox': array([ 33, ..., 328], dtype=int16)}, {'pure_name': '咖色狮子套装', 'original_text': '咖色狮子套装', 'has_direct_price': False, 'direct_price': None, 'bbox': array([166, ..., 328], dtype=int16)}, {'pure_name': '分类', 'original_text': '分类', 'has_direct_price': False, 'direct_price': None, 'bbox': array([  1, ..., 136], dtype=int16)}]
[22:31:38] 颜色分类数量: 3
[22:31:38] 📍 已记录颜色坐标: {'蓝色狮子套装': (76, 318), '咖色狮子套装': (209, 318), '分类': (23, 121)}
[22:31:38] 🎯 从页面提取价格信息
[22:31:38] ✅ 页面通用价格: 29.99 (来源: 参考分类80￥29.99)
[22:31:38] 🔧 不分配页面价格给颜色，使用交互式方案获取准确价格
[22:31:38] 🔍 商品类型分析:
[22:31:38]    颜色数量: 3
[22:31:38]    颜色直接带价格: False
[22:31:38]    页面有券前/券后价格: False
[22:31:38]    尺码带价格: True
[22:31:38] 📊 分析类型: type4_multiple_colors_size_prices
[22:31:38] 🔧 处理方案: interactive
[22:31:38] 📊 智能分析结果:
[22:31:38]   优化尺码范围: 80-120
[22:31:38]   原始尺码文本: ['参考分类80', '参考分类90', '100', '110', '120']
[22:31:38]   颜色分类: ['蓝色狮子套装', '咖色狮子套装', '分类']
[22:31:38]   颜色价格: {}
[22:31:38]   分析类型: type4_multiple_colors_size_prices
[22:31:38]   处理方案: interactive
[22:31:38] 🔄 切换到交互式价格获取方案
[22:31:38] 🚀 开始交互式价格获取...
[22:31:38] 需要交互获取价格的颜色: ['蓝色狮子套装', '咖色狮子套装', '分类']
[22:31:38] 📏 尺码选择策略: 从5个尺码中选择中间值 100
[22:31:38]    完整尺码列表: [80, 90, 100, 110, 120]
[22:31:38]    避免最小码: 80，避免最大码: 120
[22:31:38] 选择中间尺码: 100
[22:31:38] 🔍 检查尺码坐标记录: {100: (227, 406), 110: (324, 406), 120: (48, 446), 130: (282, 336), 140: (344, 336), 150: (90, 455), 90: (37, 372), 80: (37, 372)}
[22:31:38] 🔍 查找尺码: 100
[22:31:39] 📍 使用记录的尺码坐标: 100
[22:31:39]    相对坐标: (227, 406)
[22:31:39]    绝对坐标: (497, 636)
[22:31:39] 🎯 移动到尺码 100 坐标: (497, 636)
[22:31:40] 🎯 点击尺码 100
[22:31:42] 🎯 检测到多颜色商品，需要依次点击 3 个颜色
[22:31:42] 🎨 处理颜色 1/3: 蓝色狮子套装
[22:31:42] 📍 使用记录的坐标: 蓝色狮子套装
[22:31:42]    相对坐标: (76, 318)
[22:31:42]    绝对坐标: (346, 548)
[22:31:42] 🎯 移动到颜色 蓝色狮子套装 坐标: (346, 548)
[22:31:43] 🎯 点击颜色 蓝色狮子套装
[22:31:45] 📸 截取颜色 蓝色狮子套装 更新后的页面...
[22:31:45] ✅ 价格OCR截图已保存: 商品图片\3-1.jpg
[22:31:47] 价格OCR结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[22:31:47] 字典格式价格OCR识别到 20 个文本
[22:31:47] 价格OCR识别: ¥21.9-29.99 (置信度: 0.883)
[22:31:47] 价格OCR识别: 1 (置信度: 0.998)
[22:31:47] 价格OCR识别: 满50减2 (置信度: 0.999)
[22:31:47] 价格OCR识别: 请选择：参考分类 (置信度: 0.979)
[22:31:47] 价格OCR识别: 优惠 (置信度: 1.000)
[22:31:47] 价格OCR识别: 选择款式后查看优惠 (置信度: 0.998)
[22:31:47] 价格OCR识别: 颜色分类 (置信度: 1.000)
[22:31:47] 价格OCR识别: 蓝色狮子套装 (置信度: 1.000)
[22:31:47] 价格OCR识别: 咖色狮子套装 (置信度: 0.999)
[22:31:47] 价格OCR识别: 参考分类 (置信度: 1.000)
[22:31:47] 价格OCR识别: 80￥29.99 (置信度: 0.946)
[22:31:47] 价格OCR识别: 90￥29.99 (置信度: 0.953)
[22:31:47] 价格OCR识别: 100￥29.99 (置信度: 0.960)
[22:31:47] 价格OCR识别: 110￥29.99 (置信度: 0.954)
[22:31:47] 价格OCR识别: 120￥29.99 (置信度: 0.960)
[22:31:47] 价格OCR识别: 来样定制￥21.9 (置信度: 0.962)
[22:31:47] 价格OCR识别: 带图定制￥21.9 (置信度: 0.967)
[22:31:47] 价格OCR识别: 免费服务 (置信度: 1.000)
[22:31:47] 价格OCR识别: 退货包运费（商家赠送） (置信度: 0.951)
[22:31:47] 价格OCR识别: 一次选多款，不满意可极速退款> (置信度: 0.972)
[22:31:47] ✅ 价格OCR识别完成，共识别到 20 个文本
[22:31:47] ✅ 提取到通用价格: 29.99 (来源: 80￥29.99)
[22:31:47] ✅ 获取到颜色 蓝色狮子套装 的价格: 29.99
[22:31:47] 🔍 开始坐标校验，重新进行完整OCR识别...
[22:31:47] 🔍 执行坐标校验OCR识别...
[22:31:47] 🔍 开始智能OCR分析屏幕区域: (270, 230) 到 (691, 883)
[22:31:47] ✅ OCR截图已保存: 商品图片\coordinate_verify.jpg
[22:31:47] 正在执行OCR识别...
[22:31:49] OCR原始结果类型: <class 'list'>
[22:31:49] OCR原始结果长度: 1
[22:31:49] 第一页结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[22:31:49] OCRResult对象属性: ['_save_funcs', '_json_writer', '_rand_fn', '_img_writer']
[22:31:50]   _save_funcs: <class 'list'> - [<bound method JsonMixin.save_to_json of {'input_path': None, 'page_index': None, 'doc_preprocessor_...
[22:31:50]   _json_writer: <class 'paddlex.inference.utils.io.writers.JsonWriter'> - <paddlex.inference.utils.io.writers.JsonWriter object at 0x00000200E6E47BC0>
[22:31:50]   _rand_fn: <class 'NoneType'> - None
[22:31:50]   _img_writer: <class 'paddlex.inference.utils.io.writers.ImageWriter'> - <paddlex.inference.utils.io.writers.ImageWriter object at 0x00000200E8FDCC50>
[22:31:50] 方式2成功：识别到 20 个文本
[22:31:50] 文本 0: '¥21.9-29.99', 置信度: 0.8834
[22:31:50] OCR识别: ¥21.9-29.99 (置信度: 0.8834)
[22:31:50] 文本 1: '1', 置信度: 0.9978
[22:31:50] OCR识别: 1 (置信度: 0.9978)
[22:31:50] 文本 2: '满50减2', 置信度: 0.9990
[22:31:50] OCR识别: 满50减2 (置信度: 0.9990)
[22:31:50] 文本 3: '请选择：参考分类', 置信度: 0.9792
[22:31:50] OCR识别: 请选择：参考分类 (置信度: 0.9792)
[22:31:50] 文本 4: '优惠', 置信度: 0.9997
[22:31:50] OCR识别: 优惠 (置信度: 0.9997)
[22:31:50] 文本 5: '选择款式后查看优惠', 置信度: 0.9984
[22:31:50] OCR识别: 选择款式后查看优惠 (置信度: 0.9984)
[22:31:50] 文本 6: '颜色分类', 置信度: 0.9999
[22:31:50] OCR识别: 颜色分类 (置信度: 0.9999)
[22:31:50] 文本 7: '蓝色狮子套装', 置信度: 0.9995
[22:31:50] OCR识别: 蓝色狮子套装 (置信度: 0.9995)
[22:31:50] 文本 8: '咖色狮子套装', 置信度: 0.9991
[22:31:50] OCR识别: 咖色狮子套装 (置信度: 0.9991)
[22:31:50] 文本 9: '参考分类', 置信度: 0.9999
[22:31:50] OCR识别: 参考分类 (置信度: 0.9999)
[22:31:50] 文本 10: '80￥29.99', 置信度: 0.9464
[22:31:50] OCR识别: 80￥29.99 (置信度: 0.9464)
[22:31:50] 文本 11: '90￥29.99', 置信度: 0.9534
[22:31:50] OCR识别: 90￥29.99 (置信度: 0.9534)
[22:31:50] 文本 12: '100￥29.99', 置信度: 0.9597
[22:31:50] OCR识别: 100￥29.99 (置信度: 0.9597)
[22:31:50] 文本 13: '110￥29.99', 置信度: 0.9543
[22:31:50] OCR识别: 110￥29.99 (置信度: 0.9543)
[22:31:50] 文本 14: '120￥29.99', 置信度: 0.9600
[22:31:50] OCR识别: 120￥29.99 (置信度: 0.9600)
[22:31:50] 文本 15: '来样定制￥21.9', 置信度: 0.9622
[22:31:50] OCR识别: 来样定制￥21.9 (置信度: 0.9622)
[22:31:50] 文本 16: '带图定制￥21.9', 置信度: 0.9669
[22:31:50] OCR识别: 带图定制￥21.9 (置信度: 0.9669)
[22:31:50] 文本 17: '免费服务', 置信度: 0.9998
[22:31:50] OCR识别: 免费服务 (置信度: 0.9998)
[22:31:50] 文本 18: '退货包运费（商家赠送）', 置信度: 0.9510
[22:31:50] OCR识别: 退货包运费（商家赠送） (置信度: 0.9510)
[22:31:50] 文本 19: '一次选多款，不满意可极速退款>', 置信度: 0.9723
[22:31:50] OCR识别: 一次选多款，不满意可极速退款> (置信度: 0.9723)
[22:31:50] ✅ OCR识别完成，共识别到 20 个文本
[22:31:50] 🧠 开始智能分析商品信息...
[22:31:50] 🔧 开始智能拼接被分割的文本...
[22:31:50] 🎯 找到上方紧贴文本:
[22:31:50]    上方文本: '¥21.9-29.99' Y范围: 8.0-39.0, X范围: 1.0-124.0
[22:31:50]    当前文本: '满50减2' Y范围: 37.0-63.0, X起始: 5.0
[22:31:50]    垂直间距: 2.0px, X重叠: True
[22:31:50] ✅ 向上拼接: '¥21.9-29.99' + '满50减2' = '¥21.9-29.99满50减2'
[22:31:50] 🔗 完成向上拼接:
[22:31:50]    结果: '¥21.9-29.99满50减2'
[22:31:50] 🎯 找到上方紧贴文本:
[22:31:50]    上方文本: '满50减2' Y范围: 37.0-63.0, X范围: 5.0-70.0
[22:31:50]    当前文本: '请选择：参考分类' Y范围: 70.0-89.0, X起始: 4.0
[22:31:50]    垂直间距: 7.0px, X重叠: True
[22:31:50] ✅ 向上拼接: '满50减2' + '请选择：参考分类' = '满50减2请选择：参考分类'
[22:31:50] 🔗 完成向上拼接:
[22:31:50]    结果: '满50减2请选择：参考分类'
[22:31:50] 🎯 找到上方紧贴文本:
[22:31:50]    上方文本: '参考分类' Y范围: 359.0-385.0, X范围: 2.0-73.0
[22:31:50]    当前文本: '80￥29.99' Y范围: 397.0-416.0, X起始: 11.0
[22:31:50]    垂直间距: 12.0px, X重叠: True
[22:31:50] ✅ 向上拼接: '参考分类' + '80￥29.99' = '参考分类80￥29.99'
[22:31:50] 🔗 完成向上拼接:
[22:31:50]    结果: '参考分类80￥29.99'
[22:31:50] 🎯 找到上方紧贴文本:
[22:31:50]    上方文本: '参考分类' Y范围: 359.0-385.0, X范围: 2.0-73.0
[22:31:50]    当前文本: '90￥29.99' Y范围: 397.0-416.0, X起始: 101.0
[22:31:50]    垂直间距: 12.0px, X重叠: True
[22:31:50] ✅ 向上拼接: '参考分类' + '90￥29.99' = '参考分类90￥29.99'
[22:31:50] 🔗 完成向上拼接:
[22:31:50]    结果: '参考分类90￥29.99'
[22:31:50] 🎯 找到上方紧贴文本:
[22:31:50]    上方文本: '免费服务' Y范围: 485.0-510.0, X范围: 2.0-73.0
[22:31:50]    当前文本: '退货包运费（商家赠送）' Y范围: 522.0-544.0, X起始: 25.0
[22:31:50]    垂直间距: 12.0px, X重叠: True
[22:31:50] ✅ 向上拼接: '免费服务' + '退货包运费（商家赠送）' = '免费服务退货包运费（商家赠送）'
[22:31:50] 🔗 完成向上拼接:
[22:31:50]    结果: '免费服务退货包运费（商家赠送）'
[22:31:50] 📊 拼接结果: 原始20个文本 → 拼接后17个文本
[22:31:50] 📝 拼接后的文本列表:
[22:31:50]    1. '1'
[22:31:50]    2. '¥21.9-29.99满50减2'
[22:31:50]    3. '满50减2请选择：参考分类'
[22:31:50]    4. '优惠'
[22:31:50]    5. '选择款式后查看优惠'
[22:31:50]    6. '颜色分类'
[22:31:50]    7. '蓝色狮子套装'
[22:31:50]    8. '咖色狮子套装'
[22:31:50]    9. '参考分类80￥29.99'
[22:31:50]    10. '参考分类90￥29.99'
[22:31:50]    11. '100￥29.99'
[22:31:50]    12. '110￥29.99'
[22:31:50]    13. '120￥29.99'
[22:31:50]    14. '来样定制￥21.9'
[22:31:50]    15. '带图定制￥21.9'
[22:31:50]    16. '免费服务退货包运费（商家赠送）'
[22:31:50]    17. '一次选多款，不满意可极速退款>'
[22:31:50] 🎯 找到尺码区域开始位置: 第3行 '满50减2请选择：参考分类'
[22:31:50] 📝 尺码区域文本无数字: '优惠'
[22:31:50] 📝 尺码区域文本无数字: '选择款式后查看优惠'
[22:31:50] 📝 尺码区域文本无数字: '颜色分类'
[22:31:50] 📝 尺码区域文本无数字: '蓝色狮子套装'
[22:31:50] 📝 尺码区域文本无数字: '咖色狮子套装'
[22:31:50] 🔧 过滤￥符号后的文本: '参考分类80'
[22:31:50] ✅ 提取尺码: 80 (来源: '参考分类80')
[22:31:50] 🔍 处理bbox: [2, 359, 73, 385] (长度: 4)
[22:31:50] ✅ 计算坐标成功: (37, 372)
[22:31:50] 📍 记录尺码坐标: 80 -> (37, 372)
[22:31:50] 🔧 过滤￥符号后的文本: '参考分类90'
[22:31:50] ✅ 提取尺码: 90 (来源: '参考分类90')
[22:31:50] 🔍 处理bbox: [2, 359, 73, 385] (长度: 4)
[22:31:50] ✅ 计算坐标成功: (37, 372)
[22:31:50] 📍 记录尺码坐标: 90 -> (37, 372)
[22:31:50] 🔧 过滤￥符号后的文本: '100'
[22:31:50] ✅ 提取尺码: 100 (来源: '100')
[22:31:50] 🔍 处理bbox: [189, 397, 266, 416] (长度: 4)
[22:31:50] ✅ 计算坐标成功: (227, 406)
[22:31:50] 📍 记录尺码坐标: 100 -> (227, 406)
[22:31:50] 🔧 过滤￥符号后的文本: '110'
[22:31:50] ✅ 提取尺码: 110 (来源: '110')
[22:31:50] 🔍 处理bbox: [286, 397, 362, 416] (长度: 4)
[22:31:50] ✅ 计算坐标成功: (324, 406)
[22:31:50] 📍 记录尺码坐标: 110 -> (324, 406)
[22:31:50] 🔧 过滤￥符号后的文本: '120'
[22:31:50] ✅ 提取尺码: 120 (来源: '120')
[22:31:50] 🔍 处理bbox: [10, 437, 88, 456] (长度: 4)
[22:31:50] ✅ 计算坐标成功: (49, 446)
[22:31:50] 📍 记录尺码坐标: 120 -> (49, 446)
[22:31:50] 🔧 过滤￥符号后的文本: '来样定制'
[22:31:50] 📝 尺码区域文本无数字: '来样定制'
[22:31:50] 🔧 过滤￥符号后的文本: '带图定制'
[22:31:50] 📝 尺码区域文本无数字: '带图定制'
[22:31:50] 🛑 遇到停止关键词，结束尺码提取: '免费服务退货包运费（商家赠送）'
[22:31:50] 📊 尺码提取结果: 数字=[80, 90, 100, 110, 120], 范围=80-120, 原始文本数量=5
[22:31:50] 尺码信息: {'optimized_range': '80-120', 'original_texts': ['参考分类80', '参考分类90', '100', '110', '120'], 'size_numbers': [80, 90, 100, 110, 120]}
[22:31:50] 📍 已记录尺码坐标: {100: (227, 406), 110: (324, 406), 120: (49, 446), 130: (282, 336), 140: (344, 336), 150: (90, 455), 90: (37, 372), 80: (37, 372)}
[22:31:50] 🔍 发现颜色嵌入格式: '颜色分类' → 提取: '分类'
[22:31:50] 🎯 发现嵌入颜色分类: '分类' (来源: '颜色分类')
[22:31:50] 🎯 找到颜色分类开始位置: 第5行 '颜色分类'
[22:31:50] 🎯 找到颜色分类结束位置: 第8行 '参考分类80￥29.99'
[22:31:50] 🔍 开始提取颜色分类: 从第6行到第7行
[22:31:50] ✅ 保留有效文本: '蓝色狮子套装'
[22:31:50] ✅ 保留有效文本: '咖色狮子套装'
[22:31:50] ✅ 添加嵌入颜色分类: '分类'
[22:31:50] 🔍 检查颜色文本: '蓝色狮子套装' (长度: 6)
[22:31:50] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [ 34 ... 328]
[22:31:50] 🔍 价格检测结果: False
[22:31:50] 🔍 处理bbox: [34, 308, 121, 328] (长度: 4)
[22:31:50] ✅ 计算坐标成功: (77, 318)
[22:31:50] 📍 记录颜色坐标: 蓝色狮子套装 -> (77, 318)
[22:31:50] ✅ 提取到无价格颜色: 蓝色狮子套装
[22:31:50] 🔍 检查颜色文本: '咖色狮子套装' (长度: 6)
[22:31:50] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [166 ... 328]
[22:31:50] 🔍 价格检测结果: False
[22:31:50] 🔍 处理bbox: [166, 309, 252, 328] (长度: 4)
[22:31:50] ✅ 计算坐标成功: (209, 318)
[22:31:50] 📍 记录颜色坐标: 咖色狮子套装 -> (209, 318)
[22:31:50] ✅ 提取到无价格颜色: 咖色狮子套装
[22:31:50] 🔍 检查颜色文本: '分类' (长度: 2)
[22:31:50] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [  2 ... 179]
[22:31:50] 🔍 价格检测结果: False
[22:31:50] 🔍 处理bbox: [2, 152, 74, 179] (长度: 4)
[22:31:50] ✅ 计算坐标成功: (38, 165)
[22:31:50] 📍 记录颜色坐标: 分类 -> (38, 165)
[22:31:50] ✅ 提取到无价格颜色: 分类
[22:31:50] 🎨 颜色分类提取完成: 共提取到 3 个颜色
[22:31:50] 📍 坐标记录完成: 共记录 3 个坐标
[22:31:50] 提取到颜色分类: [{'pure_name': '蓝色狮子套装', 'original_text': '蓝色狮子套装', 'has_direct_price': False, 'direct_price': None, 'bbox': array([ 34, ..., 328], dtype=int16)}, {'pure_name': '咖色狮子套装', 'original_text': '咖色狮子套装', 'has_direct_price': False, 'direct_price': None, 'bbox': array([166, ..., 328], dtype=int16)}, {'pure_name': '分类', 'original_text': '分类', 'has_direct_price': False, 'direct_price': None, 'bbox': array([  2, ..., 179], dtype=int16)}]
[22:31:50] 颜色分类数量: 3
[22:31:50] 📍 已记录颜色坐标: {'蓝色狮子套装': (77, 318), '咖色狮子套装': (209, 318), '分类': (38, 165)}
[22:31:50] 🎯 从页面提取价格信息
[22:31:50] ✅ 页面通用价格: 29.99 (来源: 参考分类80￥29.99)
[22:31:50] 🔧 不分配页面价格给颜色，使用交互式方案获取准确价格
[22:31:50] 🔍 商品类型分析:
[22:31:50]    颜色数量: 3
[22:31:50]    颜色直接带价格: False
[22:31:50]    页面有券前/券后价格: False
[22:31:50]    尺码带价格: True
[22:31:50] 📊 分析类型: type4_multiple_colors_size_prices
[22:31:50] 🔧 处理方案: interactive
[22:31:50] 📊 坐标对比结果（仅对比原有颜色）:
[22:31:50]    蓝色狮子套装: (76, 318) → (77, 318) (差异: X±1, Y±0)
[22:31:50]    ✅ 蓝色狮子套装 坐标无显著变化
[22:31:50]    咖色狮子套装: (209, 318) → (209, 318) (差异: X±0, Y±0)
[22:31:50]    ✅ 咖色狮子套装 坐标无显著变化
[22:31:50]    分类: (23, 121) → (38, 165) (差异: X±15, Y±44)
[22:31:50]    🔄 分类 坐标变化超过阈值(15px)，将更新
[22:31:50] 🔄 检测到坐标变化，将使用更新后的坐标
[22:31:50] ✅ 坐标校验完成，后续颜色将使用更新后的坐标
[22:31:50] 🎨 处理颜色 2/3: 咖色狮子套装
[22:31:50] 📍 使用记录的坐标: 咖色狮子套装
[22:31:50]    相对坐标: (209, 318)
[22:31:50]    绝对坐标: (479, 548)
[22:31:50] 🎯 移动到颜色 咖色狮子套装 坐标: (479, 548)
[22:31:51] 🎯 点击颜色 咖色狮子套装
[22:31:53] 📸 截取颜色 咖色狮子套装 更新后的页面...
[22:31:53] ✅ 价格OCR截图已保存: 商品图片\3-2.jpg
[22:31:55] 价格OCR结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[22:31:55] 字典格式价格OCR识别到 20 个文本
[22:31:55] 价格OCR识别: ¥21.9-29.99 (置信度: 0.883)
[22:31:55] 价格OCR识别: 1 (置信度: 0.998)
[22:31:55] 价格OCR识别: 满50减2 (置信度: 0.999)
[22:31:55] 价格OCR识别: 请选择：参考分类 (置信度: 0.985)
[22:31:55] 价格OCR识别: 优惠 (置信度: 1.000)
[22:31:55] 价格OCR识别: 选择款式后查看优惠 (置信度: 0.998)
[22:31:55] 价格OCR识别: 颜色分类 (置信度: 1.000)
[22:31:55] 价格OCR识别: 蓝色狮子套装 (置信度: 0.999)
[22:31:55] 价格OCR识别: 咖色狮子套装 (置信度: 0.999)
[22:31:55] 价格OCR识别: 参考分类 (置信度: 1.000)
[22:31:55] 价格OCR识别: 80￥29.99 (置信度: 0.954)
[22:31:55] 价格OCR识别: 90￥29.99 (置信度: 0.956)
[22:31:55] 价格OCR识别: 100¥29.99 (置信度: 0.946)
[22:31:55] 价格OCR识别: 110￥29.99 (置信度: 0.915)
[22:31:55] 价格OCR识别: 120￥29.99 (置信度: 0.957)
[22:31:55] 价格OCR识别: 来样定制￥21.9 (置信度: 0.972)
[22:31:55] 价格OCR识别: 带图定制￥21.9 (置信度: 0.967)
[22:31:55] 价格OCR识别: 免费服务 (置信度: 1.000)
[22:31:55] 价格OCR识别: 退货包运费 (商家赠送) (置信度: 0.941)
[22:31:55] 价格OCR识别: 一次选多款，不满意可极速退款> (置信度: 0.972)
[22:31:55] ✅ 价格OCR识别完成，共识别到 20 个文本
[22:31:55] ✅ 提取到通用价格: 29.99 (来源: 80￥29.99)
[22:31:55] ✅ 获取到颜色 咖色狮子套装 的价格: 29.99
[22:31:55] 🎨 处理颜色 3/3: 分类
[22:31:55] 📍 使用记录的坐标: 分类
[22:31:55]    相对坐标: (38, 165)
[22:31:55]    绝对坐标: (308, 395)
[22:31:55] 🎯 移动到颜色 分类 坐标: (308, 395)
[22:31:56] 🎯 点击颜色 分类
[22:31:59] 📸 截取颜色 分类 更新后的页面...
[22:31:59] ✅ 价格OCR截图已保存: 商品图片\3-3.jpg
[22:32:01] 价格OCR结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[22:32:01] 字典格式价格OCR识别到 20 个文本
[22:32:01] 价格OCR识别: ¥21.9-29.99 (置信度: 0.883)
[22:32:01] 价格OCR识别: 1 (置信度: 0.998)
[22:32:01] 价格OCR识别: 满50减2 (置信度: 0.999)
[22:32:01] 价格OCR识别: 请选择：参考分类 (置信度: 0.985)
[22:32:01] 价格OCR识别: 优惠 (置信度: 1.000)
[22:32:01] 价格OCR识别: 选择款式后查看优惠 (置信度: 0.998)
[22:32:01] 价格OCR识别: 颜色分类 (置信度: 1.000)
[22:32:01] 价格OCR识别: 蓝色狮子套装 (置信度: 0.999)
[22:32:01] 价格OCR识别: 咖色狮子套装 (置信度: 0.999)
[22:32:01] 价格OCR识别: 参考分类 (置信度: 1.000)
[22:32:01] 价格OCR识别: 80￥29.99 (置信度: 0.954)
[22:32:01] 价格OCR识别: 90￥29.99 (置信度: 0.956)
[22:32:01] 价格OCR识别: 100¥29.99 (置信度: 0.946)
[22:32:01] 价格OCR识别: 110￥29.99 (置信度: 0.915)
[22:32:01] 价格OCR识别: 120￥29.99 (置信度: 0.957)
[22:32:01] 价格OCR识别: 来样定制￥21.9 (置信度: 0.972)
[22:32:01] 价格OCR识别: 带图定制￥21.9 (置信度: 0.967)
[22:32:01] 价格OCR识别: 免费服务 (置信度: 1.000)
[22:32:01] 价格OCR识别: 退货包运费 (商家赠送) (置信度: 0.941)
[22:32:01] 价格OCR识别: 一次选多款，不满意可极速退款> (置信度: 0.972)
[22:32:01] ✅ 价格OCR识别完成，共识别到 20 个文本
[22:32:01] ✅ 提取到通用价格: 29.99 (来源: 80￥29.99)
[22:32:01] ✅ 获取到颜色 分类 的价格: 29.99
[22:32:01] 💾 保存分析结果到Excel...
[22:32:01] 找到目标商品链接在第 34 行
[22:32:01] ✅ 保存有价格颜色: 蓝色狮子套装 -> 29.99
[22:32:01] ✅ 保存有价格颜色: 咖色狮子套装 -> 29.99
[22:32:01] ✅ 保存有价格颜色: 分类 -> 29.99
[22:32:01] 找到下一个商品链接在第 45 行
[22:32:01] 清空第 35 行到第 44 行中的 0 行有内容数据，保留空行
[22:32:01] ✅ 分析结果已保存到商品3下方: 商品图片\商品SKU信息.xlsx
[22:32:01]    插入了 5 行新数据
[22:32:01] 🎉 交互式价格获取完成
[22:32:01] ==================================================
[22:32:01] ✅ 智能OCR分析完成
[22:32:01] ==================================================
[22:32:01] ✅ 详情页图片捕获完成
[22:32:01] ✅ OCR分析和Excel保存已在详情页处理中完成
[22:32:01] ✅ 第 3 个商品处理完成
[22:32:01] 🔄 准备处理下一个商品...
[22:32:01] 🔄 开始返回搜索页面...
[22:32:01] 移动到位置(470,590)...
[22:32:01] 点击鼠标右键...
[22:32:03] 再次点击鼠标右键...
[22:32:04] ✅ 返回搜索页面操作完成
[22:32:04] 
============================================================
[22:32:04] 🎯 开始处理第 4 个商品
[22:32:04] 商品链接: https://mobile.yangkeduo.com/goods.html?ps=MSQnVD8B4H
[22:32:04] ============================================================
[22:32:04] 步骤1: 设置剪贴板内容...
[22:32:04] 已复制商品链接到剪贴板: https://mobile.yangkeduo.com/goods.html?ps=MSQnVD8B4H
[22:32:04] ✅ 剪贴板内容验证成功
[22:32:04] 步骤2: 点击搜索框并输入链接...
[22:32:04] 移动到位置(480,96)并点击...
[22:32:05] 按下Ctrl+A全选...
[22:32:05] 执行AutoHotkey脚本: ctrl_a.ahk
[22:32:06] ✅ AutoHotkey脚本执行成功: ctrl_a
[22:32:07] 粘贴商品链接...
[22:32:07] 使用外部脚本软件执行真正的Ctrl+V...
[22:32:07] Python的键盘模拟无法被escrcpy识别，尝试其他方案
[22:32:07] 方法1: 尝试AutoHotkey...
[22:32:07] 找到脚本文件: paste_v2.ahk
[22:32:07] 检查路径: AutoHotkey.exe
[22:32:07] 检查路径: C:\Program Files\AutoHotkey\AutoHotkey.exe
[22:32:07] 检查路径: C:\Program Files (x86)\AutoHotkey\AutoHotkey.exe
[22:32:07] 检查路径: C:\Program Files\AutoHotkey\v2\AutoHotkey.exe
[22:32:07] 找到AutoHotkey: C:\Program Files\AutoHotkey\v2\AutoHotkey.exe
[22:32:07] 执行AutoHotkey脚本: paste_v2.ahk
[22:32:08] AutoHotkey返回码: 0
[22:32:08] ✅ AutoHotkey执行成功
[22:32:08] ✅ Ctrl+V操作执行成功
[22:32:11] 步骤3: 点击搜索按钮...
[22:32:11] 正在查找图片: SOUSUO2.png (路径: image\IQOO\SOUSUO2.png)
[22:32:11] 找到图片 SOUSUO2.png，位置: (641, 81)
[22:32:11] 点击位置: (664, 96)
[22:32:12] 已点击图片: SOUSUO2.png
[22:32:12] 步骤4: 等待3秒进入商品页面...
[22:32:15] 步骤5: 执行从右往左的滑动操作...
[22:32:15] 开始执行从右往左的滑动操作...
[22:32:15] 📍 起始位置: (630, 185)
[22:32:15] 📍 结束位置: (284, 185)
[22:32:15] 📏 滑动距离: 346 像素
[22:32:15] ⏱️ 滑动时长: 0.3 秒
[22:32:15] 移动到起始位置(630, 185)
[22:32:15] 按住左键从(630, 185)拖拽到(284, 185)
[22:32:16] 开始滑动，时长0.3秒...
[22:32:16] 释放左键，滑动操作完成
[22:32:17] ✅ 从右往左的滑动操作完成
[22:32:17] 步骤6: 移动到位置(470,185)...
[22:32:17] 已移动到位置(470,185)
[22:32:17] 步骤7: 执行鼠标点击和长按操作...
[22:32:17] 点击鼠标左键一次
[22:32:18] 在位置(475, 323)长按鼠标左键0.5秒
[22:32:19] 鼠标操作完成
[22:32:20] 步骤8: 查找并点击保存按钮...
[22:32:20] 尝试查找图片 BAOCUN1.png (第1次/共3次)
[22:32:20] 正在查找图片: BAOCUN1.png (路径: image\IQOO\BAOCUN1.png)
[22:32:20] 找到图片 BAOCUN1.png，位置: (450, 808)
[22:32:20] 移动到图片上，等待0.5秒...
[22:32:21] 已点击图片: BAOCUN1.png
[22:32:21] 步骤9: 检测保存状态...
[22:32:21] 开始检测图片: BAOCUNZHONG.png (路径: image\IQOO\BAOCUNZHONG.png)
[22:32:21] 最多检测 80 次，每隔 0.2 秒检测一次
[22:32:21] 第 1/80 次检测...
[22:32:21] 第 2/80 次检测...
[22:32:21] 第 3/80 次检测...
[22:32:22] ✅ 找到图片 BAOCUNZHONG.png！位置: (443, 507)
[22:32:22] ✅ 检测到保存中状态，等待保存完成...
[22:32:22] 等待5秒让图片完全保存到手机...
[22:32:27] 步骤10: 复制手机图片到商品 4 文件夹...
[22:32:27] 第 1/3 次尝试复制图片...
[22:32:27] 开始从MTP设备复制图片到商品 4 文件夹...
[22:32:27] 正在使用Windows Shell API查找MTP设备...
[22:32:27] 找到设备: iQOO Z1
[22:32:27] 进入文件夹: 内部存储设备
[22:32:27] 进入文件夹: DCIM
[22:32:27] 进入文件夹: Pindd
[22:32:27] 进入文件夹: goods
[22:32:27] 成功找到目标文件夹: 内部存储设备\DCIM\Pindd\goods
[22:32:27] 目标路径: F:\自动捕获数据\商品图片\4
[22:32:27] 复制文件: 1753626501373-1315797334.jpg
[22:32:27] ✅ 成功复制: 1753626501373-1315797334.jpg
[22:32:27] 复制文件: 1753626501484-1645306700.jpg
[22:32:27] ✅ 成功复制: 1753626501484-1645306700.jpg
[22:32:27] 复制文件: 1753626501256--945688330.jpg
[22:32:27] ✅ 成功复制: 1753626501256--945688330.jpg
[22:32:27] 复制文件: 1753626501297-483977703.jpg
[22:32:27] ✅ 成功复制: 1753626501297-483977703.jpg
[22:32:27] 复制文件: 1753626501401--1182398115.jpg
[22:32:27] ✅ 成功复制: 1753626501401--1182398115.jpg
[22:32:27] 复制文件: 1753626501042--850234244.jpg
[22:32:27] ✅ 成功复制: 1753626501042--850234244.jpg
[22:32:27] 复制文件: 1753626500939-199319035.jpg
[22:32:27] ✅ 成功复制: 1753626500939-199319035.jpg
[22:32:27] 复制文件: 1753626501073--278957689.jpg
[22:32:27] ✅ 成功复制: 1753626501073--278957689.jpg
[22:32:27] 复制文件: 1753626501332-1706905320.jpg
[22:32:27] ✅ 成功复制: 1753626501332-1706905320.jpg
[22:32:27] 复制文件: 1753626501176--1680485554.jpg
[22:32:27] ✅ 成功复制: 1753626501176--1680485554.jpg
[22:32:27] 复制文件: 1753626501138--1193025293.jpg
[22:32:28] ✅ 成功复制: 1753626501138--1193025293.jpg
[22:32:28] 复制文件: 1753626501207--778938302.jpg
[22:32:28] ✅ 成功复制: 1753626501207--778938302.jpg
[22:32:28] 复制文件: 1753626501435-1066972225.jpg
[22:32:28] ✅ 成功复制: 1753626501435-1066972225.jpg
[22:32:28] 复制文件: 1753626518152-660672411.jpg
[22:32:28] ✅ 成功复制: 1753626518152-660672411.jpg
[22:32:28] 复制文件: 1753626518186-2125388435.jpg
[22:32:28] ✅ 成功复制: 1753626518186-2125388435.jpg
[22:32:28] 复制文件: 1753626518219-1804250823.jpg
[22:32:28] ✅ 成功复制: 1753626518219-1804250823.jpg
[22:32:28] 复制文件: 1753626518249--1065563707.jpg
[22:32:28] ✅ 成功复制: 1753626518249--1065563707.jpg
[22:32:28] 复制文件: 1753626518295-152127411.jpg
[22:32:28] ✅ 成功复制: 1753626518295-152127411.jpg
[22:32:28] 复制文件: 1753626518357-1552964844.jpg
[22:32:28] ✅ 成功复制: 1753626518357-1552964844.jpg
[22:32:28] 复制文件: 1753626518406-1752797653.jpg
[22:32:28] ✅ 成功复制: 1753626518406-1752797653.jpg
[22:32:28] 复制文件: 1753626518441--1753262296.jpg
[22:32:28] ✅ 成功复制: 1753626518441--1753262296.jpg
[22:32:28] 复制文件: 1753626518489-842254062.jpg
[22:32:28] ✅ 成功复制: 1753626518489-842254062.jpg
[22:32:28] 复制文件: 1753626518530--255483989.jpg
[22:32:28] ✅ 成功复制: 1753626518530--255483989.jpg
[22:32:28] 复制文件: 1753626518571--1285728568.jpg
[22:32:28] ✅ 成功复制: 1753626518571--1285728568.jpg
[22:32:28] 复制文件: 1753626518627-396311468.jpg
[22:32:29] ✅ 成功复制: 1753626518627-396311468.jpg
[22:32:29] 复制文件: 1753626518662-1252897153.jpg
[22:32:29] ✅ 成功复制: 1753626518662-1252897153.jpg
[22:32:29] 复制文件: 1753626518696--1537897804.jpg
[22:32:29] ✅ 成功复制: 1753626518696--1537897804.jpg
[22:32:29] 复制文件: 1753626518746--1146868097.jpg
[22:32:29] ✅ 成功复制: 1753626518746--1146868097.jpg
[22:32:29] 复制文件: 1753626518780--1712757656.jpg
[22:32:29] ✅ 成功复制: 1753626518780--1712757656.jpg
[22:32:29] 复制文件: 1753626518827-184866696.jpg
[22:32:29] ✅ 成功复制: 1753626518827-184866696.jpg
[22:32:29] 复制文件: 1753626584506--174181552.jpg
[22:32:29] ✅ 成功复制: 1753626584506--174181552.jpg
[22:32:29] 复制文件: 1753626584568-649400088.jpg
[22:32:29] ✅ 成功复制: 1753626584568-649400088.jpg
[22:32:29] 复制文件: 1753626584616--75726748.jpg
[22:32:29] ✅ 成功复制: 1753626584616--75726748.jpg
[22:32:29] 复制文件: 1753626584693--1636531876.jpg
[22:32:29] ✅ 成功复制: 1753626584693--1636531876.jpg
[22:32:29] 复制文件: 1753626584727-1492996970.jpg
[22:32:29] ✅ 成功复制: 1753626584727-1492996970.jpg
[22:32:29] 复制文件: 1753626584762-2021427306.jpg
[22:32:29] ✅ 成功复制: 1753626584762-2021427306.jpg
[22:32:29] 复制文件: 1753626603375-1223281507.jpg
[22:32:29] ✅ 成功复制: 1753626603375-1223281507.jpg
[22:32:29] 复制文件: 1753626603420--59820335.jpg
[22:32:30] ✅ 成功复制: 1753626603420--59820335.jpg
[22:32:30] 复制文件: 1753626603446--1882576375.jpg
[22:32:30] ✅ 成功复制: 1753626603446--1882576375.jpg
[22:32:30] 复制文件: 1753626603487--2039576347.jpg
[22:32:30] ✅ 成功复制: 1753626603487--2039576347.jpg
[22:32:30] 复制文件: 1753626603536--320016844.jpg
[22:32:30] ✅ 成功复制: 1753626603536--320016844.jpg
[22:32:30] 复制文件: 1753626603577--1854090630.jpg
[22:32:30] ✅ 成功复制: 1753626603577--1854090630.jpg
[22:32:30] 复制文件: 1753626660614--1428446268.jpg
[22:32:30] ✅ 成功复制: 1753626660614--1428446268.jpg
[22:32:30] 复制文件: 1753626660666-1209359625.jpg
[22:32:30] ✅ 成功复制: 1753626660666-1209359625.jpg
[22:32:30] 复制文件: 1753626660709-1902243945.jpg
[22:32:30] ✅ 成功复制: 1753626660709-1902243945.jpg
[22:32:30] 复制文件: 1753626660740-333858737.jpg
[22:32:30] ✅ 成功复制: 1753626660740-333858737.jpg
[22:32:30] 复制文件: 1753626660786-1052787518.jpg
[22:32:30] ✅ 成功复制: 1753626660786-1052787518.jpg
[22:32:30] 复制文件: 1753626660849-877359325.jpg
[22:32:30] ✅ 成功复制: 1753626660849-877359325.jpg
[22:32:30] 复制文件: 1753626660881-1230956995.jpg
[22:32:30] ✅ 成功复制: 1753626660881-1230956995.jpg
[22:32:30] 复制文件: 1753626680297--251064384.jpg
[22:32:30] ✅ 成功复制: 1753626680297--251064384.jpg
[22:32:30] 复制文件: 1753626680360-1069102764.jpg
[22:32:31] ✅ 成功复制: 1753626680360-1069102764.jpg
[22:32:31] 复制文件: 1753626680401--482777265.jpg
[22:32:31] ✅ 成功复制: 1753626680401--482777265.jpg
[22:32:31] 复制文件: 1753626680449--1838772425.jpg
[22:32:31] ✅ 成功复制: 1753626680449--1838772425.jpg
[22:32:31] 复制文件: 1753626680498--618659008.jpg
[22:32:31] ✅ 成功复制: 1753626680498--618659008.jpg
[22:32:31] 复制文件: 1753626680546--1085139102.jpg
[22:32:31] ✅ 成功复制: 1753626680546--1085139102.jpg
[22:32:31] 复制文件: 1753626680588-1076043545.jpg
[22:32:31] ✅ 成功复制: 1753626680588-1076043545.jpg
[22:32:31] 复制文件: 1753626680636-552573400.jpg
[22:32:31] ✅ 成功复制: 1753626680636-552573400.jpg
[22:32:31] 复制文件: 1753626680677--1331103116.jpg
[22:32:31] ✅ 成功复制: 1753626680677--1331103116.jpg
[22:32:31] 复制文件: 1753626740875--1213573602.jpg
[22:32:31] ✅ 成功复制: 1753626740875--1213573602.jpg
[22:32:31] 复制文件: 1753626740918--268036214.jpg
[22:32:31] ✅ 成功复制: 1753626740918--268036214.jpg
[22:32:31] 复制文件: 1753626740959--1636983126.jpg
[22:32:31] ✅ 成功复制: 1753626740959--1636983126.jpg
[22:32:31] 复制文件: 1753626741004--597443972.jpg
[22:32:32] ✅ 成功复制: 1753626741004--597443972.jpg
[22:32:32] 复制文件: 1753626741049--805566952.jpg
[22:32:32] ✅ 成功复制: 1753626741049--805566952.jpg
[22:32:32] 复制文件: 1753626741112-715278496.jpg
[22:32:32] ✅ 成功复制: 1753626741112-715278496.jpg
[22:32:32] 复制文件: 1753626741153--106487342.jpg
[22:32:32] ✅ 成功复制: 1753626741153--106487342.jpg
[22:32:32] 复制文件: 1753626741194-1887506479.jpg
[22:32:32] ✅ 成功复制: 1753626741194-1887506479.jpg
[22:32:32] 复制文件: 1753626741230-639229492.jpg
[22:32:32] ✅ 成功复制: 1753626741230-639229492.jpg
[22:32:32] 复制文件: 1753626741263-1180235664.jpg
[22:32:32] ✅ 成功复制: 1753626741263-1180235664.jpg
[22:32:32] 复制文件: 1753626741298-1944811228.jpg
[22:32:32] ✅ 成功复制: 1753626741298-1944811228.jpg
[22:32:32] 复制文件: 1753626741339--1475076813.jpg
[22:32:32] ✅ 成功复制: 1753626741339--1475076813.jpg
[22:32:32] 复制文件: 1753626741374-913913680.jpg
[22:32:32] ✅ 成功复制: 1753626741374-913913680.jpg
[22:32:32] 复制文件: 1753626741409-1189978779.jpg
[22:32:32] ✅ 成功复制: 1753626741409-1189978779.jpg
[22:32:32] 成功复制 72 个文件
[22:32:32] ✅ 成功复制 72 个图片到商品 4 文件夹
[22:32:32] 复制的文件:
[22:32:32]   - 1753626501373-1315797334.jpg
[22:32:32]   - 1753626501484-1645306700.jpg
[22:32:32]   - 1753626501256--945688330.jpg
[22:32:32]   - 1753626501297-483977703.jpg
[22:32:32]   - 1753626501401--1182398115.jpg
[22:32:32]   - 1753626501042--850234244.jpg
[22:32:32]   - 1753626500939-199319035.jpg
[22:32:32]   - 1753626501073--278957689.jpg
[22:32:32]   - 1753626501332-1706905320.jpg
[22:32:32]   - 1753626501176--1680485554.jpg
[22:32:32]   - 1753626501138--1193025293.jpg
[22:32:32]   - 1753626501207--778938302.jpg
[22:32:32]   - 1753626501435-1066972225.jpg
[22:32:32]   - 1753626518152-660672411.jpg
[22:32:32]   - 1753626518186-2125388435.jpg
[22:32:32]   - 1753626518219-1804250823.jpg
[22:32:32]   - 1753626518249--1065563707.jpg
[22:32:32]   - 1753626518295-152127411.jpg
[22:32:32]   - 1753626518357-1552964844.jpg
[22:32:32]   - 1753626518406-1752797653.jpg
[22:32:32]   - 1753626518441--1753262296.jpg
[22:32:32]   - 1753626518489-842254062.jpg
[22:32:32]   - 1753626518530--255483989.jpg
[22:32:32]   - 1753626518571--1285728568.jpg
[22:32:32]   - 1753626518627-396311468.jpg
[22:32:32]   - 1753626518662-1252897153.jpg
[22:32:32]   - 1753626518696--1537897804.jpg
[22:32:32]   - 1753626518746--1146868097.jpg
[22:32:32]   - 1753626518780--1712757656.jpg
[22:32:32]   - 1753626518827-184866696.jpg
[22:32:32]   - 1753626584506--174181552.jpg
[22:32:32]   - 1753626584568-649400088.jpg
[22:32:32]   - 1753626584616--75726748.jpg
[22:32:32]   - 1753626584693--1636531876.jpg
[22:32:32]   - 1753626584727-1492996970.jpg
[22:32:32]   - 1753626584762-2021427306.jpg
[22:32:32]   - 1753626603375-1223281507.jpg
[22:32:32]   - 1753626603420--59820335.jpg
[22:32:32]   - 1753626603446--1882576375.jpg
[22:32:32]   - 1753626603487--2039576347.jpg
[22:32:32]   - 1753626603536--320016844.jpg
[22:32:32]   - 1753626603577--1854090630.jpg
[22:32:32]   - 1753626660614--1428446268.jpg
[22:32:32]   - 1753626660666-1209359625.jpg
[22:32:32]   - 1753626660709-1902243945.jpg
[22:32:32]   - 1753626660740-333858737.jpg
[22:32:32]   - 1753626660786-1052787518.jpg
[22:32:32]   - 1753626660849-877359325.jpg
[22:32:32]   - 1753626660881-1230956995.jpg
[22:32:32]   - 1753626680297--251064384.jpg
[22:32:32]   - 1753626680360-1069102764.jpg
[22:32:32]   - 1753626680401--482777265.jpg
[22:32:32]   - 1753626680449--1838772425.jpg
[22:32:32]   - 1753626680498--618659008.jpg
[22:32:32]   - 1753626680546--1085139102.jpg
[22:32:32]   - 1753626680588-1076043545.jpg
[22:32:32]   - 1753626680636-552573400.jpg
[22:32:32]   - 1753626680677--1331103116.jpg
[22:32:32]   - 1753626740875--1213573602.jpg
[22:32:32]   - 1753626740918--268036214.jpg
[22:32:32]   - 1753626740959--1636983126.jpg
[22:32:32]   - 1753626741004--597443972.jpg
[22:32:32]   - 1753626741049--805566952.jpg
[22:32:32]   - 1753626741112-715278496.jpg
[22:32:32]   - 1753626741153--106487342.jpg
[22:32:32]   - 1753626741194-1887506479.jpg
[22:32:32]   - 1753626741230-639229492.jpg
[22:32:32]   - 1753626741263-1180235664.jpg
[22:32:32]   - 1753626741298-1944811228.jpg
[22:32:32]   - 1753626741339--1475076813.jpg
[22:32:32]   - 1753626741374-913913680.jpg
[22:32:32]   - 1753626741409-1189978779.jpg
[22:32:32] ⚠️  注意：由于MTP协议限制，无法自动删除手机上的原文件
[22:32:32] 请手动删除手机 DCIM/Pindd/goods 文件夹中的图片以释放空间
[22:32:32] ✅ 图片复制完成
[22:32:32] 步骤11: 删除手机上的原文件...
[22:32:32] 开始删除手机文件操作...
[22:32:32] 查找并点击GOODS.png...
[22:32:32] 正在查找图片: GOODS.png (路径: image\IQOO\GOODS.png)
[22:32:33] 查找或点击图片 GOODS.png 时出错: 
[22:32:33] ❌ 未找到GOODS.png图片
[22:32:33] ⚠️ 手机文件删除失败，请手动删除
[22:32:33] 步骤12: 开始处理主图...
[22:32:33] 步骤13: 开始第二轮操作（详情页）...
[22:32:33] 处理主图文件夹: 商品图片\4
[22:32:33] === 开始详情页图片捕获流程 ===
[22:32:33] 步骤13.1: 移动到(475,230)并点击右键...
[22:32:33] 找到 72 张图片
[22:32:33] 删除多余图片: 1753626501401--1182398115.jpg
[22:32:33] 删除多余图片: 1753626501042--850234244.jpg
[22:32:33] 删除多余图片: 1753626500939-199319035.jpg
[22:32:33] 删除多余图片: 1753626501073--278957689.jpg
[22:32:33] 删除多余图片: 1753626501332-1706905320.jpg
[22:32:33] 删除多余图片: 1753626501176--1680485554.jpg
[22:32:33] 删除多余图片: 1753626501138--1193025293.jpg
[22:32:33] 删除多余图片: 1753626501207--778938302.jpg
[22:32:33] 删除多余图片: 1753626501435-1066972225.jpg
[22:32:33] 删除多余图片: 1753626518152-660672411.jpg
[22:32:33] 删除多余图片: 1753626518186-2125388435.jpg
[22:32:33] 删除多余图片: 1753626518219-1804250823.jpg
[22:32:33] 删除多余图片: 1753626518249--1065563707.jpg
[22:32:33] 删除多余图片: 1753626518295-152127411.jpg
[22:32:33] 删除多余图片: 1753626518357-1552964844.jpg
[22:32:33] 删除多余图片: 1753626518406-1752797653.jpg
[22:32:33] 删除多余图片: 1753626518441--1753262296.jpg
[22:32:33] 删除多余图片: 1753626518489-842254062.jpg
[22:32:33] 删除多余图片: 1753626518530--255483989.jpg
[22:32:33] 删除多余图片: 1753626518571--1285728568.jpg
[22:32:33] 删除多余图片: 1753626518627-396311468.jpg
[22:32:33] 删除多余图片: 1753626518662-1252897153.jpg
[22:32:33] 删除多余图片: 1753626518696--1537897804.jpg
[22:32:33] 删除多余图片: 1753626518746--1146868097.jpg
[22:32:33] 删除多余图片: 1753626518780--1712757656.jpg
[22:32:33] 删除多余图片: 1753626518827-184866696.jpg
[22:32:33] 删除多余图片: 1753626584506--174181552.jpg
[22:32:33] 删除多余图片: 1753626584568-649400088.jpg
[22:32:33] 删除多余图片: 1753626584616--75726748.jpg
[22:32:33] 删除多余图片: 1753626584693--1636531876.jpg
[22:32:33] 删除多余图片: 1753626584727-1492996970.jpg
[22:32:33] 删除多余图片: 1753626584762-2021427306.jpg
[22:32:33] 删除多余图片: 1753626603375-1223281507.jpg
[22:32:33] 删除多余图片: 1753626603420--59820335.jpg
[22:32:33] 删除多余图片: 1753626603446--1882576375.jpg
[22:32:33] 删除多余图片: 1753626603487--2039576347.jpg
[22:32:33] 删除多余图片: 1753626603536--320016844.jpg
[22:32:33] 删除多余图片: 1753626603577--1854090630.jpg
[22:32:33] 删除多余图片: 1753626660614--1428446268.jpg
[22:32:33] 删除多余图片: 1753626660666-1209359625.jpg
[22:32:33] 删除多余图片: 1753626660709-1902243945.jpg
[22:32:33] 删除多余图片: 1753626660740-333858737.jpg
[22:32:33] 删除多余图片: 1753626660786-1052787518.jpg
[22:32:33] 删除多余图片: 1753626660849-877359325.jpg
[22:32:33] 删除多余图片: 1753626660881-1230956995.jpg
[22:32:33] 删除多余图片: 1753626680297--251064384.jpg
[22:32:33] 删除多余图片: 1753626680360-1069102764.jpg
[22:32:33] 删除多余图片: 1753626680401--482777265.jpg
[22:32:33] 删除多余图片: 1753626680449--1838772425.jpg
[22:32:33] 删除多余图片: 1753626680498--618659008.jpg
[22:32:33] 删除多余图片: 1753626680546--1085139102.jpg
[22:32:33] 删除多余图片: 1753626680588-1076043545.jpg
[22:32:33] 删除多余图片: 1753626680636-552573400.jpg
[22:32:33] 删除多余图片: 1753626680677--1331103116.jpg
[22:32:33] 删除多余图片: 1753626740875--1213573602.jpg
[22:32:33] 删除多余图片: 1753626740918--268036214.jpg
[22:32:33] 删除多余图片: 1753626740959--1636983126.jpg
[22:32:33] 删除多余图片: 1753626741004--597443972.jpg
[22:32:33] 删除多余图片: 1753626741049--805566952.jpg
[22:32:33] 删除多余图片: 1753626741112-715278496.jpg
[22:32:33] 删除多余图片: 1753626741153--106487342.jpg
[22:32:33] 删除多余图片: 1753626741194-1887506479.jpg
[22:32:33] 删除多余图片: 1753626741230-639229492.jpg
[22:32:33] 删除多余图片: 1753626741263-1180235664.jpg
[22:32:33] 删除多余图片: 1753626741298-1944811228.jpg
[22:32:33] 删除多余图片: 1753626741339--1475076813.jpg
[22:32:33] 删除多余图片: 1753626741374-913913680.jpg
[22:32:33] 删除多余图片: 1753626741409-1189978779.jpg
[22:32:33] 重命名: 1753626501373-1315797334.jpg → 主图1.jpg
[22:32:33] 重命名: 1753626501484-1645306700.jpg → 主图2.jpg
[22:32:33] 重命名: 1753626501256--945688330.jpg → 主图3.jpg
[22:32:33] 重命名: 1753626501297-483977703.jpg → 主图4.jpg
[22:32:33] ✅ 主图处理完成
[22:32:35] 步骤13.2: 使用AutoHotkey向下滚动40次...
[22:32:35] 执行AutoHotkey脚本: scroll_down.ahk
[22:32:39] ✅ AutoHotkey脚本执行成功: scroll_down
[22:32:39] ✅ AutoHotkey滚动执行成功
[22:32:39] 步骤13.3: 移动到(477,300)并点击左键...
[22:32:40] ✅ 点击操作完成
[22:32:40] 步骤13.4: 移动到(480,495)并长按0.5秒...
[22:32:41] ✅ 长按操作完成
[22:32:42] 步骤13.5: 查找并点击保存按钮...
[22:32:42] 尝试查找图片 BAOCUN1.png (第1次/共3次)
[22:32:42] 正在查找图片: BAOCUN1.png (路径: image\IQOO\BAOCUN1.png)
[22:32:42] 找到图片 BAOCUN1.png，位置: (454, 808)
[22:32:42] 移动到图片上，等待0.5秒...
[22:32:43] 已点击图片: BAOCUN1.png
[22:32:43] 步骤13.6: 检测详情页保存状态...
[22:32:43] 开始检测图片: BAOCUNZHONG.png (路径: image\IQOO\BAOCUNZHONG.png)
[22:32:43] 最多检测 80 次，每隔 0.2 秒检测一次
[22:32:43] 第 1/80 次检测...
[22:32:43] 第 2/80 次检测...
[22:32:43] ✅ 找到图片 BAOCUNZHONG.png！位置: (443, 508)
[22:32:43] ✅ 检测到详情页保存中状态，等待保存完成...
[22:32:48] 步骤13.7: 复制详情页图片...
[22:32:48] 第 1/3 次尝试复制详情页图片...
[22:32:48] 开始从MTP设备复制图片到商品 4 文件夹...
[22:32:48] 正在使用Windows Shell API查找MTP设备...
[22:32:48] 找到设备: iQOO Z1
[22:32:48] 进入文件夹: 内部存储设备
[22:32:48] 进入文件夹: DCIM
[22:32:48] 进入文件夹: Pindd
[22:32:48] 进入文件夹: goods
[22:32:48] 成功找到目标文件夹: 内部存储设备\DCIM\Pindd\goods
[22:32:48] 目标路径: F:\自动捕获数据\商品图片\4
[22:32:48] 复制文件: 1753626501373-1315797334.jpg
[22:32:48] ✅ 成功复制: 1753626501373-1315797334.jpg
[22:32:48] 复制文件: 1753626501484-1645306700.jpg
[22:32:49] ✅ 成功复制: 1753626501484-1645306700.jpg
[22:32:49] 复制文件: 1753626501256--945688330.jpg
[22:32:49] ✅ 成功复制: 1753626501256--945688330.jpg
[22:32:49] 复制文件: 1753626501297-483977703.jpg
[22:32:49] ✅ 成功复制: 1753626501297-483977703.jpg
[22:32:49] 复制文件: 1753626501401--1182398115.jpg
[22:32:49] ✅ 成功复制: 1753626501401--1182398115.jpg
[22:32:49] 复制文件: 1753626501042--850234244.jpg
[22:32:49] ✅ 成功复制: 1753626501042--850234244.jpg
[22:32:49] 复制文件: 1753626500939-199319035.jpg
[22:32:49] ✅ 成功复制: 1753626500939-199319035.jpg
[22:32:49] 复制文件: 1753626501073--278957689.jpg
[22:32:49] ✅ 成功复制: 1753626501073--278957689.jpg
[22:32:49] 复制文件: 1753626501332-1706905320.jpg
[22:32:49] ✅ 成功复制: 1753626501332-1706905320.jpg
[22:32:49] 复制文件: 1753626501176--1680485554.jpg
[22:32:49] ✅ 成功复制: 1753626501176--1680485554.jpg
[22:32:49] 复制文件: 1753626501138--1193025293.jpg
[22:32:49] ✅ 成功复制: 1753626501138--1193025293.jpg
[22:32:49] 复制文件: 1753626501207--778938302.jpg
[22:32:49] ✅ 成功复制: 1753626501207--778938302.jpg
[22:32:49] 复制文件: 1753626501435-1066972225.jpg
[22:32:49] ✅ 成功复制: 1753626501435-1066972225.jpg
[22:32:49] 复制文件: 1753626518152-660672411.jpg
[22:32:49] ✅ 成功复制: 1753626518152-660672411.jpg
[22:32:49] 复制文件: 1753626518186-2125388435.jpg
[22:32:49] ✅ 成功复制: 1753626518186-2125388435.jpg
[22:32:49] 复制文件: 1753626518219-1804250823.jpg
[22:32:50] ✅ 成功复制: 1753626518219-1804250823.jpg
[22:32:50] 复制文件: 1753626518249--1065563707.jpg
[22:32:50] ✅ 成功复制: 1753626518249--1065563707.jpg
[22:32:50] 复制文件: 1753626518295-152127411.jpg
[22:32:50] ✅ 成功复制: 1753626518295-152127411.jpg
[22:32:50] 复制文件: 1753626518357-1552964844.jpg
[22:32:50] ✅ 成功复制: 1753626518357-1552964844.jpg
[22:32:50] 复制文件: 1753626518406-1752797653.jpg
[22:32:50] ✅ 成功复制: 1753626518406-1752797653.jpg
[22:32:50] 复制文件: 1753626518441--1753262296.jpg
[22:32:50] ✅ 成功复制: 1753626518441--1753262296.jpg
[22:32:50] 复制文件: 1753626518489-842254062.jpg
[22:32:50] ✅ 成功复制: 1753626518489-842254062.jpg
[22:32:50] 复制文件: 1753626518530--255483989.jpg
[22:32:50] ✅ 成功复制: 1753626518530--255483989.jpg
[22:32:50] 复制文件: 1753626518571--1285728568.jpg
[22:32:50] ✅ 成功复制: 1753626518571--1285728568.jpg
[22:32:50] 复制文件: 1753626518627-396311468.jpg
[22:32:50] ✅ 成功复制: 1753626518627-396311468.jpg
[22:32:50] 复制文件: 1753626518662-1252897153.jpg
[22:32:50] ✅ 成功复制: 1753626518662-1252897153.jpg
[22:32:50] 复制文件: 1753626518696--1537897804.jpg
[22:32:50] ✅ 成功复制: 1753626518696--1537897804.jpg
[22:32:50] 复制文件: 1753626518746--1146868097.jpg
[22:32:50] ✅ 成功复制: 1753626518746--1146868097.jpg
[22:32:50] 复制文件: 1753626518780--1712757656.jpg
[22:32:51] ✅ 成功复制: 1753626518780--1712757656.jpg
[22:32:51] 复制文件: 1753626518827-184866696.jpg
[22:32:51] ✅ 成功复制: 1753626518827-184866696.jpg
[22:32:51] 复制文件: 1753626584506--174181552.jpg
[22:32:51] ✅ 成功复制: 1753626584506--174181552.jpg
[22:32:51] 复制文件: 1753626584568-649400088.jpg
[22:32:51] ✅ 成功复制: 1753626584568-649400088.jpg
[22:32:51] 复制文件: 1753626584616--75726748.jpg
[22:32:51] ✅ 成功复制: 1753626584616--75726748.jpg
[22:32:51] 复制文件: 1753626584693--1636531876.jpg
[22:32:51] ✅ 成功复制: 1753626584693--1636531876.jpg
[22:32:51] 复制文件: 1753626584727-1492996970.jpg
[22:32:51] ✅ 成功复制: 1753626584727-1492996970.jpg
[22:32:51] 复制文件: 1753626584762-2021427306.jpg
[22:32:51] ✅ 成功复制: 1753626584762-2021427306.jpg
[22:32:51] 复制文件: 1753626603375-1223281507.jpg
[22:32:51] ✅ 成功复制: 1753626603375-1223281507.jpg
[22:32:51] 复制文件: 1753626603420--59820335.jpg
[22:32:51] ✅ 成功复制: 1753626603420--59820335.jpg
[22:32:51] 复制文件: 1753626603446--1882576375.jpg
[22:32:51] ✅ 成功复制: 1753626603446--1882576375.jpg
[22:32:51] 复制文件: 1753626603487--2039576347.jpg
[22:32:51] ✅ 成功复制: 1753626603487--2039576347.jpg
[22:32:51] 复制文件: 1753626603536--320016844.jpg
[22:32:51] ✅ 成功复制: 1753626603536--320016844.jpg
[22:32:51] 复制文件: 1753626603577--1854090630.jpg
[22:32:51] ✅ 成功复制: 1753626603577--1854090630.jpg
[22:32:51] 复制文件: 1753626660614--1428446268.jpg
[22:32:51] ✅ 成功复制: 1753626660614--1428446268.jpg
[22:32:51] 复制文件: 1753626660666-1209359625.jpg
[22:32:52] ✅ 成功复制: 1753626660666-1209359625.jpg
[22:32:52] 复制文件: 1753626660709-1902243945.jpg
[22:32:52] ✅ 成功复制: 1753626660709-1902243945.jpg
[22:32:52] 复制文件: 1753626660740-333858737.jpg
[22:32:52] ✅ 成功复制: 1753626660740-333858737.jpg
[22:32:52] 复制文件: 1753626660786-1052787518.jpg
[22:32:52] ✅ 成功复制: 1753626660786-1052787518.jpg
[22:32:52] 复制文件: 1753626660849-877359325.jpg
[22:32:52] ✅ 成功复制: 1753626660849-877359325.jpg
[22:32:52] 复制文件: 1753626660881-1230956995.jpg
[22:32:52] ✅ 成功复制: 1753626660881-1230956995.jpg
[22:32:52] 复制文件: 1753626680297--251064384.jpg
[22:32:52] ✅ 成功复制: 1753626680297--251064384.jpg
[22:32:52] 复制文件: 1753626680360-1069102764.jpg
[22:32:52] ✅ 成功复制: 1753626680360-1069102764.jpg
[22:32:52] 复制文件: 1753626680401--482777265.jpg
[22:32:52] ✅ 成功复制: 1753626680401--482777265.jpg
[22:32:52] 复制文件: 1753626680449--1838772425.jpg
[22:32:52] ✅ 成功复制: 1753626680449--1838772425.jpg
[22:32:52] 复制文件: 1753626680498--618659008.jpg
[22:32:52] ✅ 成功复制: 1753626680498--618659008.jpg
[22:32:52] 复制文件: 1753626680546--1085139102.jpg
[22:32:52] ✅ 成功复制: 1753626680546--1085139102.jpg
[22:32:52] 复制文件: 1753626680588-1076043545.jpg
[22:32:53] ✅ 成功复制: 1753626680588-1076043545.jpg
[22:32:53] 复制文件: 1753626680636-552573400.jpg
[22:32:53] ✅ 成功复制: 1753626680636-552573400.jpg
[22:32:53] 复制文件: 1753626680677--1331103116.jpg
[22:32:53] ✅ 成功复制: 1753626680677--1331103116.jpg
[22:32:53] 复制文件: 1753626740875--1213573602.jpg
[22:32:53] ✅ 成功复制: 1753626740875--1213573602.jpg
[22:32:53] 复制文件: 1753626740918--268036214.jpg
[22:32:53] ✅ 成功复制: 1753626740918--268036214.jpg
[22:32:53] 复制文件: 1753626740959--1636983126.jpg
[22:32:53] ✅ 成功复制: 1753626740959--1636983126.jpg
[22:32:53] 复制文件: 1753626741004--597443972.jpg
[22:32:53] ✅ 成功复制: 1753626741004--597443972.jpg
[22:32:53] 复制文件: 1753626741049--805566952.jpg
[22:32:53] ✅ 成功复制: 1753626741049--805566952.jpg
[22:32:53] 复制文件: 1753626741112-715278496.jpg
[22:32:53] ✅ 成功复制: 1753626741112-715278496.jpg
[22:32:53] 复制文件: 1753626741153--106487342.jpg
[22:32:53] ✅ 成功复制: 1753626741153--106487342.jpg
[22:32:53] 复制文件: 1753626741194-1887506479.jpg
[22:32:53] ✅ 成功复制: 1753626741194-1887506479.jpg
[22:32:53] 复制文件: 1753626741230-639229492.jpg
[22:32:54] ✅ 成功复制: 1753626741230-639229492.jpg
[22:32:54] 复制文件: 1753626741263-1180235664.jpg
[22:32:54] ✅ 成功复制: 1753626741263-1180235664.jpg
[22:32:54] 复制文件: 1753626741298-1944811228.jpg
[22:32:54] ✅ 成功复制: 1753626741298-1944811228.jpg
[22:32:54] 复制文件: 1753626741339--1475076813.jpg
[22:32:54] ✅ 成功复制: 1753626741339--1475076813.jpg
[22:32:54] 复制文件: 1753626741374-913913680.jpg
[22:32:54] ✅ 成功复制: 1753626741374-913913680.jpg
[22:32:54] 复制文件: 1753626741409-1189978779.jpg
[22:32:54] ✅ 成功复制: 1753626741409-1189978779.jpg
[22:32:54] 复制文件: 1753626762896--238727143.jpg
[22:32:54] ✅ 成功复制: 1753626762896--238727143.jpg
[22:32:54] 复制文件: 1753626762954--655740314.jpg
[22:32:54] ✅ 成功复制: 1753626762954--655740314.jpg
[22:32:54] 复制文件: 1753626762988-953387863.jpg
[22:32:54] ✅ 成功复制: 1753626762988-953387863.jpg
[22:32:54] 复制文件: 1753626763036--744338388.jpg
[22:32:54] ✅ 成功复制: 1753626763036--744338388.jpg
[22:32:54] 复制文件: 1753626763085-730639924.jpg
[22:32:54] ✅ 成功复制: 1753626763085-730639924.jpg
[22:32:54] 复制文件: 1753626763126--112046204.jpg
[22:32:55] ✅ 成功复制: 1753626763126--112046204.jpg
[22:32:55] 复制文件: 1753626763160--1229046725.jpg
[22:32:55] ✅ 成功复制: 1753626763160--1229046725.jpg
[22:32:55] 复制文件: 1753626763195-203300966.jpg
[22:32:55] ✅ 成功复制: 1753626763195-203300966.jpg
[22:32:55] 成功复制 80 个文件
[22:32:55] ✅ 成功复制 80 个图片到商品 4 文件夹
[22:32:55] 复制的文件:
[22:32:55]   - 1753626501373-1315797334.jpg
[22:32:55]   - 1753626501484-1645306700.jpg
[22:32:55]   - 1753626501256--945688330.jpg
[22:32:55]   - 1753626501297-483977703.jpg
[22:32:55]   - 1753626501401--1182398115.jpg
[22:32:55]   - 1753626501042--850234244.jpg
[22:32:55]   - 1753626500939-199319035.jpg
[22:32:55]   - 1753626501073--278957689.jpg
[22:32:55]   - 1753626501332-1706905320.jpg
[22:32:55]   - 1753626501176--1680485554.jpg
[22:32:55]   - 1753626501138--1193025293.jpg
[22:32:55]   - 1753626501207--778938302.jpg
[22:32:55]   - 1753626501435-1066972225.jpg
[22:32:55]   - 1753626518152-660672411.jpg
[22:32:55]   - 1753626518186-2125388435.jpg
[22:32:55]   - 1753626518219-1804250823.jpg
[22:32:55]   - 1753626518249--1065563707.jpg
[22:32:55]   - 1753626518295-152127411.jpg
[22:32:55]   - 1753626518357-1552964844.jpg
[22:32:55]   - 1753626518406-1752797653.jpg
[22:32:55]   - 1753626518441--1753262296.jpg
[22:32:55]   - 1753626518489-842254062.jpg
[22:32:55]   - 1753626518530--255483989.jpg
[22:32:55]   - 1753626518571--1285728568.jpg
[22:32:55]   - 1753626518627-396311468.jpg
[22:32:55]   - 1753626518662-1252897153.jpg
[22:32:55]   - 1753626518696--1537897804.jpg
[22:32:55]   - 1753626518746--1146868097.jpg
[22:32:55]   - 1753626518780--1712757656.jpg
[22:32:55]   - 1753626518827-184866696.jpg
[22:32:55]   - 1753626584506--174181552.jpg
[22:32:55]   - 1753626584568-649400088.jpg
[22:32:55]   - 1753626584616--75726748.jpg
[22:32:55]   - 1753626584693--1636531876.jpg
[22:32:55]   - 1753626584727-1492996970.jpg
[22:32:55]   - 1753626584762-2021427306.jpg
[22:32:55]   - 1753626603375-1223281507.jpg
[22:32:55]   - 1753626603420--59820335.jpg
[22:32:55]   - 1753626603446--1882576375.jpg
[22:32:55]   - 1753626603487--2039576347.jpg
[22:32:55]   - 1753626603536--320016844.jpg
[22:32:55]   - 1753626603577--1854090630.jpg
[22:32:55]   - 1753626660614--1428446268.jpg
[22:32:55]   - 1753626660666-1209359625.jpg
[22:32:55]   - 1753626660709-1902243945.jpg
[22:32:55]   - 1753626660740-333858737.jpg
[22:32:55]   - 1753626660786-1052787518.jpg
[22:32:55]   - 1753626660849-877359325.jpg
[22:32:55]   - 1753626660881-1230956995.jpg
[22:32:55]   - 1753626680297--251064384.jpg
[22:32:55]   - 1753626680360-1069102764.jpg
[22:32:55]   - 1753626680401--482777265.jpg
[22:32:55]   - 1753626680449--1838772425.jpg
[22:32:55]   - 1753626680498--618659008.jpg
[22:32:55]   - 1753626680546--1085139102.jpg
[22:32:55]   - 1753626680588-1076043545.jpg
[22:32:55]   - 1753626680636-552573400.jpg
[22:32:55]   - 1753626680677--1331103116.jpg
[22:32:55]   - 1753626740875--1213573602.jpg
[22:32:55]   - 1753626740918--268036214.jpg
[22:32:55]   - 1753626740959--1636983126.jpg
[22:32:55]   - 1753626741004--597443972.jpg
[22:32:55]   - 1753626741049--805566952.jpg
[22:32:55]   - 1753626741112-715278496.jpg
[22:32:55]   - 1753626741153--106487342.jpg
[22:32:55]   - 1753626741194-1887506479.jpg
[22:32:55]   - 1753626741230-639229492.jpg
[22:32:55]   - 1753626741263-1180235664.jpg
[22:32:55]   - 1753626741298-1944811228.jpg
[22:32:55]   - 1753626741339--1475076813.jpg
[22:32:55]   - 1753626741374-913913680.jpg
[22:32:55]   - 1753626741409-1189978779.jpg
[22:32:55]   - 1753626762896--238727143.jpg
[22:32:55]   - 1753626762954--655740314.jpg
[22:32:55]   - 1753626762988-953387863.jpg
[22:32:55]   - 1753626763036--744338388.jpg
[22:32:55]   - 1753626763085-730639924.jpg
[22:32:55]   - 1753626763126--112046204.jpg
[22:32:55]   - 1753626763160--1229046725.jpg
[22:32:55]   - 1753626763195-203300966.jpg
[22:32:55] ⚠️  注意：由于MTP协议限制，无法自动删除手机上的原文件
[22:32:55] 请手动删除手机 DCIM/Pindd/goods 文件夹中的图片以释放空间
[22:32:55] ✅ 详情页图片复制完成
[22:32:55] 步骤13.8: 删除手机上的详情页图片...
[22:32:55] 开始删除手机文件操作...
[22:32:55] 查找并点击GOODS.png...
[22:32:55] 正在查找图片: GOODS.png (路径: image\IQOO\GOODS.png)
[22:32:55] 查找或点击图片 GOODS.png 时出错: 
[22:32:55] ❌ 未找到GOODS.png图片
[22:32:55] ⚠️ 手机详情页文件删除失败，请手动删除
[22:32:55] 步骤13.8: 处理详情页图片...
[22:32:55] 处理详情页图片文件夹: 商品图片\4
[22:32:55] 跳过已处理的主图: 主图1.jpg
[22:32:55] 跳过已处理的主图: 主图2.jpg
[22:32:55] 跳过已处理的主图: 主图3.jpg
[22:32:55] 跳过已处理的主图: 主图4.jpg
[22:32:55] 找到 80 张详情页图片
[22:32:55] 删除多余详情页图片: 1753626584506--174181552.jpg
[22:32:55] 删除多余详情页图片: 1753626584568-649400088.jpg
[22:32:55] 删除多余详情页图片: 1753626584616--75726748.jpg
[22:32:55] 删除多余详情页图片: 1753626584693--1636531876.jpg
[22:32:55] 删除多余详情页图片: 1753626584727-1492996970.jpg
[22:32:55] 删除多余详情页图片: 1753626584762-2021427306.jpg
[22:32:55] 删除多余详情页图片: 1753626603375-1223281507.jpg
[22:32:55] 删除多余详情页图片: 1753626603420--59820335.jpg
[22:32:55] 删除多余详情页图片: 1753626603446--1882576375.jpg
[22:32:55] 删除多余详情页图片: 1753626603487--2039576347.jpg
[22:32:55] 删除多余详情页图片: 1753626603536--320016844.jpg
[22:32:55] 删除多余详情页图片: 1753626603577--1854090630.jpg
[22:32:55] 删除多余详情页图片: 1753626660614--1428446268.jpg
[22:32:55] 删除多余详情页图片: 1753626660666-1209359625.jpg
[22:32:55] 删除多余详情页图片: 1753626660709-1902243945.jpg
[22:32:55] 删除多余详情页图片: 1753626660740-333858737.jpg
[22:32:55] 删除多余详情页图片: 1753626660786-1052787518.jpg
[22:32:55] 删除多余详情页图片: 1753626660849-877359325.jpg
[22:32:55] 删除多余详情页图片: 1753626660881-1230956995.jpg
[22:32:55] 删除多余详情页图片: 1753626680297--251064384.jpg
[22:32:55] 删除多余详情页图片: 1753626680360-1069102764.jpg
[22:32:55] 删除多余详情页图片: 1753626680401--482777265.jpg
[22:32:55] 删除多余详情页图片: 1753626680449--1838772425.jpg
[22:32:55] 删除多余详情页图片: 1753626680498--618659008.jpg
[22:32:55] 删除多余详情页图片: 1753626680546--1085139102.jpg
[22:32:55] 删除多余详情页图片: 1753626680588-1076043545.jpg
[22:32:55] 删除多余详情页图片: 1753626680636-552573400.jpg
[22:32:55] 删除多余详情页图片: 1753626680677--1331103116.jpg
[22:32:55] 删除多余详情页图片: 1753626740875--1213573602.jpg
[22:32:55] 删除多余详情页图片: 1753626740918--268036214.jpg
[22:32:55] 删除多余详情页图片: 1753626740959--1636983126.jpg
[22:32:55] 删除多余详情页图片: 1753626741004--597443972.jpg
[22:32:55] 删除多余详情页图片: 1753626741049--805566952.jpg
[22:32:55] 删除多余详情页图片: 1753626741112-715278496.jpg
[22:32:55] 删除多余详情页图片: 1753626741153--106487342.jpg
[22:32:55] 删除多余详情页图片: 1753626741194-1887506479.jpg
[22:32:55] 删除多余详情页图片: 1753626741230-639229492.jpg
[22:32:55] 删除多余详情页图片: 1753626741263-1180235664.jpg
[22:32:55] 删除多余详情页图片: 1753626741298-1944811228.jpg
[22:32:55] 删除多余详情页图片: 1753626741339--1475076813.jpg
[22:32:55] 删除多余详情页图片: 1753626741374-913913680.jpg
[22:32:55] 删除多余详情页图片: 1753626741409-1189978779.jpg
[22:32:55] 删除多余详情页图片: 1753626762896--238727143.jpg
[22:32:55] 删除多余详情页图片: 1753626762954--655740314.jpg
[22:32:55] 删除多余详情页图片: 1753626762988-953387863.jpg
[22:32:55] 删除多余详情页图片: 1753626763036--744338388.jpg
[22:32:55] 删除多余详情页图片: 1753626763085-730639924.jpg
[22:32:55] 删除多余详情页图片: 1753626763126--112046204.jpg
[22:32:55] 删除多余详情页图片: 1753626763160--1229046725.jpg
[22:32:55] 删除多余详情页图片: 1753626763195-203300966.jpg
[22:32:55] 重命名详情页图片: 1753626501373-1315797334.jpg → 1.jpg
[22:32:55] 重命名详情页图片: 1753626501484-1645306700.jpg → 2.jpg
[22:32:55] 重命名详情页图片: 1753626501256--945688330.jpg → 3.jpg
[22:32:55] 重命名详情页图片: 1753626501297-483977703.jpg → 4.jpg
[22:32:55] 重命名详情页图片: 1753626501401--1182398115.jpg → 5.jpg
[22:32:55] 重命名详情页图片: 1753626501042--850234244.jpg → 6.jpg
[22:32:55] 重命名详情页图片: 1753626500939-199319035.jpg → 7.jpg
[22:32:55] 重命名详情页图片: 1753626501073--278957689.jpg → 8.jpg
[22:32:55] 重命名详情页图片: 1753626501332-1706905320.jpg → 9.jpg
[22:32:55] 重命名详情页图片: 1753626501176--1680485554.jpg → 10.jpg
[22:32:55] 重命名详情页图片: 1753626501138--1193025293.jpg → 11.jpg
[22:32:55] 重命名详情页图片: 1753626501207--778938302.jpg → 12.jpg
[22:32:55] 重命名详情页图片: 1753626501435-1066972225.jpg → 13.jpg
[22:32:55] 重命名详情页图片: 1753626518152-660672411.jpg → 14.jpg
[22:32:55] 重命名详情页图片: 1753626518186-2125388435.jpg → 15.jpg
[22:32:55] 重命名详情页图片: 1753626518219-1804250823.jpg → 16.jpg
[22:32:55] 重命名详情页图片: 1753626518249--1065563707.jpg → 17.jpg
[22:32:55] 重命名详情页图片: 1753626518295-152127411.jpg → 18.jpg
[22:32:55] 重命名详情页图片: 1753626518357-1552964844.jpg → 19.jpg
[22:32:55] 重命名详情页图片: 1753626518406-1752797653.jpg → 20.jpg
[22:32:55] 重命名详情页图片: 1753626518441--1753262296.jpg → 21.jpg
[22:32:55] 重命名详情页图片: 1753626518489-842254062.jpg → 22.jpg
[22:32:55] 重命名详情页图片: 1753626518530--255483989.jpg → 23.jpg
[22:32:55] 重命名详情页图片: 1753626518571--1285728568.jpg → 24.jpg
[22:32:55] 重命名详情页图片: 1753626518627-396311468.jpg → 25.jpg
[22:32:55] 重命名详情页图片: 1753626518662-1252897153.jpg → 26.jpg
[22:32:55] 重命名详情页图片: 1753626518696--1537897804.jpg → 27.jpg
[22:32:55] 重命名详情页图片: 1753626518746--1146868097.jpg → 28.jpg
[22:32:55] 重命名详情页图片: 1753626518780--1712757656.jpg → 29.jpg
[22:32:55] 重命名详情页图片: 1753626518827-184866696.jpg → 30.jpg
[22:32:55] ✅ 详情页图片处理完成
[22:32:55] ✅ 主图保持'主图X'格式，详情页图片重命名为1-30
[22:32:55] ✅ 详情页图片处理完成
[22:32:55] 步骤13.9: 执行最后的鼠标操作...
[22:32:55] 开始执行最后的鼠标操作序列...
[22:32:55] 移动到位置(475,125)...
[22:32:56] 点击右键...
[22:32:56] 等待1秒...
[22:32:57] 移动到位置(670,940)...
[22:32:57] 点击左键...
[22:32:58] 等待2.5秒让页面加载完成...
[22:33:00] ✅ 最后的鼠标操作序列完成
[22:33:00] 步骤13.10: 执行智能OCR分析...
[22:33:00] ==================================================
[22:33:00] 🧠 智能OCR分析开始
[22:33:00] ==================================================
[22:33:00] 🔍 开始智能OCR分析屏幕区域: (270, 230) 到 (691, 883)
[22:33:00] ✅ OCR截图已保存: 商品图片\4.jpg
[22:33:00] 正在执行OCR识别...
[22:33:03] OCR原始结果类型: <class 'list'>
[22:33:03] OCR原始结果长度: 1
[22:33:03] 第一页结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[22:33:03] OCRResult对象属性: ['_save_funcs', '_json_writer', '_rand_fn', '_img_writer']
[22:33:03]   _save_funcs: <class 'list'> - [<bound method JsonMixin.save_to_json of {'input_path': None, 'page_index': None, 'doc_preprocessor_...
[22:33:03]   _json_writer: <class 'paddlex.inference.utils.io.writers.JsonWriter'> - <paddlex.inference.utils.io.writers.JsonWriter object at 0x00000200E6E196D0>
[22:33:03]   _rand_fn: <class 'NoneType'> - None
[22:33:03]   _img_writer: <class 'paddlex.inference.utils.io.writers.ImageWriter'> - <paddlex.inference.utils.io.writers.ImageWriter object at 0x00000200E7AF1550>
[22:33:03] 方式2成功：识别到 30 个文本
[22:33:03] 文本 0: '￥35.96- 79.11', 置信度: 0.9295
[22:33:03] OCR识别: ￥35.96- 79.11 (置信度: 0.9295)
[22:33:03] 文本 1: '1', 置信度: 0.9975
[22:33:03] OCR识别: 1 (置信度: 0.9975)
[22:33:03] 文本 2: '请选择：颜色分类参考分类', 置信度: 0.9982
[22:33:03] OCR识别: 请选择：颜色分类参考分类 (置信度: 0.9982)
[22:33:03] 文本 3: '颜色分类', 置信度: 0.9999
[22:33:03] OCR识别: 颜色分类 (置信度: 0.9999)
[22:33:03] 文本 4: '撞色毛衣马甲', 置信度: 0.9981
[22:33:03] OCR识别: 撞色毛衣马甲 (置信度: 0.9981)
[22:33:03] 文本 5: '黄色衬衫+领带', 置信度: 0.9949
[22:33:03] OCR识别: 黄色衬衫+领带 (置信度: 0.9949)
[22:33:03] 文本 6: '蓝色破洞牛仔裤', 置信度: 0.9970
[22:33:03] OCR识别: 蓝色破洞牛仔裤 (置信度: 0.9970)
[22:33:03] 文本 7: '撞色马', 置信度: 0.9974
[22:33:03] OCR识别: 撞色马 (置信度: 0.9974)
[22:33:03] 文本 8: '￥35.96', 置信度: 0.9604
[22:33:03] OCR识别: ￥35.96 (置信度: 0.9604)
[22:33:03] 文本 9: '【两件套】￥39.55', 置信度: 0.9747
[22:33:03] OCR识别: 【两件套】￥39.55 (置信度: 0.9747)
[22:33:03] 文本 10: '￥35.96', 置信度: 0.9474
[22:33:03] OCR识别: ￥35.96 (置信度: 0.9474)
[22:33:03] 文本 11: '衬衫+领带', 置信度: 0.9940
[22:33:03] OCR识别: 衬衫+领带 (置信度: 0.9940)
[22:33:03] 文本 12: 'W', 置信度: 0.6767
[22:33:03] OCR识别: W (置信度: 0.6767)
[22:33:03] 文本 13: '1141', 置信度: 0.4347
[22:33:03] OCR识别: 1141 (置信度: 0.4347)
[22:33:03] 文本 14: '撞色马甲+牛仔裤【', 置信度: 0.9916
[22:33:03] OCR识别: 撞色马甲+牛仔裤【 (置信度: 0.9916)
[22:33:03] 文本 15: '黄色衬衫+领带+牛', 置信度: 0.9880
[22:33:03] OCR识别: 黄色衬衫+领带+牛 (置信度: 0.9880)
[22:33:03] 文本 16: '撞色马甲+黄色衬衫', 置信度: 0.9927
[22:33:03] OCR识别: 撞色马甲+黄色衬衫 (置信度: 0.9927)
[22:33:03] 文本 17: '两件套】￥62.03', 置信度: 0.9749
[22:33:03] OCR识别: 两件套】￥62.03 (置信度: 0.9749)
[22:33:03] 文本 18: '仔裤【三件￥62.03', 置信度: 0.9869
[22:33:03] OCR识别: 仔裤【三件￥62.03 (置信度: 0.9869)
[22:33:03] 文本 19: '+领带+牛仔￥79.11', 置信度: 0.9777
[22:33:03] OCR识别: +领带+牛仔￥79.11 (置信度: 0.9777)
[22:33:03] 文本 20: '参考分类', 置信度: 1.0000
[22:33:03] OCR识别: 参考分类 (置信度: 1.0000)
[22:33:03] 文本 21: '90码数', 置信度: 0.9996
[22:33:03] OCR识别: 90码数 (置信度: 0.9996)
[22:33:03] 文本 22: '100码数', 置信度: 0.9995
[22:33:03] OCR识别: 100码数 (置信度: 0.9995)
[22:33:03] 文本 23: '110码数', 置信度: 0.9996
[22:33:03] OCR识别: 110码数 (置信度: 0.9996)
[22:33:03] 文本 24: '120码数', 置信度: 0.9995
[22:33:03] OCR识别: 120码数 (置信度: 0.9995)
[22:33:03] 文本 25: '130码数', 置信度: 0.9997
[22:33:03] OCR识别: 130码数 (置信度: 0.9997)
[22:33:03] 文本 26: '140码数', 置信度: 0.9997
[22:33:03] OCR识别: 140码数 (置信度: 0.9997)
[22:33:03] 文本 27: '免费服务', 置信度: 0.9998
[22:33:03] OCR识别: 免费服务 (置信度: 0.9998)
[22:33:03] 文本 28: '退货包运费(商家赠送)', 置信度: 0.9176
[22:33:03] OCR识别: 退货包运费(商家赠送) (置信度: 0.9176)
[22:33:03] 文本 29: '一次选多款，不满意可极速退款>', 置信度: 0.9716
[22:33:03] OCR识别: 一次选多款，不满意可极速退款> (置信度: 0.9716)
[22:33:03] ✅ OCR识别完成，共识别到 30 个文本
[22:33:03] 💾 保存OCR结果到文件...
[22:33:03] ✅ OCR结果已保存: OCR\商品4_OCR结果.txt
[22:33:03] 🧠 开始智能分析商品信息...
[22:33:03] 🔧 开始智能拼接被分割的文本...
[22:33:03] 🎯 找到上方紧贴文本:
[22:33:03]    上方文本: '撞色毛衣马甲' Y范围: 210.0-227.0, X范围: 23.0-98.0
[22:33:03]    当前文本: '￥35.96' Y范围: 224.0-241.0, X起始: 40.0
[22:33:03]    垂直间距: 3.0px, X重叠: True
[22:33:03] ✅ 向上拼接: '撞色毛衣马甲' + '￥35.96' = '撞色毛衣马甲￥35.96'
[22:33:03] 🔗 完成向上拼接:
[22:33:03]    结果: '撞色毛衣马甲￥35.96'
[22:33:03] 🎯 找到上方紧贴文本:
[22:33:03]    上方文本: '黄色衬衫+领带' Y范围: 211.0-227.0, X范围: 144.0-226.0
[22:33:03]    当前文本: '【两件套】￥39.55' Y范围: 224.0-240.0, X起始: 134.0
[22:33:03]    垂直间距: 3.0px, X重叠: True
[22:33:03] ✅ 向上拼接: '黄色衬衫+领带' + '【两件套】￥39.55' = '黄色衬衫+领带【两件套】￥39.55'
[22:33:03] 🔗 完成向上拼接:
[22:33:03]    结果: '黄色衬衫+领带【两件套】￥39.55'
[22:33:03] 🎯 找到上方紧贴文本:
[22:33:03]    上方文本: '蓝色破洞牛仔裤' Y范围: 209.0-228.0, X范围: 252.0-339.0
[22:33:03]    当前文本: '￥35.96' Y范围: 224.0-241.0, X起始: 274.0
[22:33:03]    垂直间距: 4.0px, X重叠: True
[22:33:03] ✅ 向上拼接: '蓝色破洞牛仔裤' + '￥35.96' = '蓝色破洞牛仔裤￥35.96'
[22:33:03] 🔗 完成向上拼接:
[22:33:03]    结果: '蓝色破洞牛仔裤￥35.96'
[22:33:03] 🎯 找到上方紧贴文本:
[22:33:03]    上方文本: '蓝色破洞牛仔裤' Y范围: 209.0-228.0, X范围: 252.0-339.0
[22:33:03]    当前文本: '衬衫+领带' Y范围: 223.0-240.0, X起始: 361.0
[22:33:03]    垂直间距: 5.0px, X重叠: True
[22:33:03] 🎯 找到上方紧贴文本:
[22:33:03]    上方文本: '撞色马' Y范围: 210.0-227.0, X范围: 377.0-421.0
[22:33:03]    当前文本: '衬衫+领带' Y范围: 223.0-240.0, X起始: 361.0
[22:33:03]    垂直间距: 4.0px, X重叠: True
[22:33:03] ✅ 向上拼接: '撞色马' + '衬衫+领带' = '撞色马衬衫+领带'
[22:33:03] 🔗 完成向上拼接:
[22:33:03]    结果: '撞色马衬衫+领带'
[22:33:03] 🎯 找到上方紧贴文本:
[22:33:03]    上方文本: '撞色马甲+牛仔裤【' Y范围: 347.0-365.0, X范围: 8.0-115.0
[22:33:03]    当前文本: '两件套】￥62.03' Y范围: 362.0-378.0, X起始: 16.0
[22:33:03]    垂直间距: 3.0px, X重叠: True
[22:33:03] ✅ 向上拼接: '撞色马甲+牛仔裤【' + '两件套】￥62.03' = '撞色马甲+牛仔裤【两件套】￥62.03'
[22:33:03] 🔗 完成向上拼接:
[22:33:03]    结果: '撞色马甲+牛仔裤【两件套】￥62.03'
[22:33:03] 🎯 找到上方紧贴文本:
[22:33:03]    上方文本: '撞色马甲+牛仔裤【' Y范围: 347.0-365.0, X范围: 8.0-115.0
[22:33:03]    当前文本: '仔裤【三件￥62.03' Y范围: 362.0-378.0, X起始: 127.0
[22:33:03]    垂直间距: 3.0px, X重叠: True
[22:33:03] ✅ 向上拼接: '撞色马甲+牛仔裤【' + '仔裤【三件￥62.03' = '撞色马甲+牛仔裤【仔裤【三件￥62.03'
[22:33:03] 🔗 完成向上拼接:
[22:33:03]    结果: '撞色马甲+牛仔裤【仔裤【三件￥62.03'
[22:33:03] 🎯 找到上方紧贴文本:
[22:33:03]    上方文本: '参考分类' Y范围: 405.0-428.0, X范围: 4.0-73.0
[22:33:03]    当前文本: '90码数' Y范围: 440.0-462.0, X起始: 10.0
[22:33:03]    垂直间距: 12.0px, X重叠: True
[22:33:03] ✅ 向上拼接: '参考分类' + '90码数' = '参考分类90码数'
[22:33:03] 🔗 完成向上拼接:
[22:33:03]    结果: '参考分类90码数'
[22:33:03] 🎯 找到上方紧贴文本:
[22:33:03]    上方文本: '参考分类' Y范围: 405.0-428.0, X范围: 4.0-73.0
[22:33:03]    当前文本: '100码数' Y范围: 441.0-460.0, X起始: 83.0
[22:33:03]    垂直间距: 13.0px, X重叠: True
[22:33:03] ✅ 向上拼接: '参考分类' + '100码数' = '参考分类100码数'
[22:33:03] 🔗 完成向上拼接:
[22:33:03]    结果: '参考分类100码数'
[22:33:03] 🎯 找到上方紧贴文本:
[22:33:03]    上方文本: '免费服务' Y范围: 530.0-555.0, X范围: 3.0-73.0
[22:33:03]    当前文本: '退货包运费(商家赠送)' Y范围: 566.0-588.0, X起始: 29.0
[22:33:03]    垂直间距: 11.0px, X重叠: True
[22:33:03] ✅ 向上拼接: '免费服务' + '退货包运费(商家赠送)' = '免费服务退货包运费(商家赠送)'
[22:33:03] 🔗 完成向上拼接:
[22:33:03]    结果: '免费服务退货包运费(商家赠送)'
[22:33:03] 📊 拼接结果: 原始30个文本 → 拼接后23个文本
[22:33:03] 📝 拼接后的文本列表:
[22:33:03]    1. '￥35.96- 79.11'
[22:33:03]    2. '1'
[22:33:03]    3. '请选择：颜色分类参考分类'
[22:33:03]    4. '颜色分类'
[22:33:03]    5. '撞色毛衣马甲￥35.96'
[22:33:03]    6. '黄色衬衫+领带【两件套】￥39.55'
[22:33:03]    7. '蓝色破洞牛仔裤￥35.96'
[22:33:03]    8. '撞色马衬衫+领带'
[22:33:03]    9. 'W'
[22:33:03]    10. '1141'
[22:33:03]    11. '黄色衬衫+领带+牛'
[22:33:03]    12. '撞色马甲+黄色衬衫'
[22:33:03]    13. '撞色马甲+牛仔裤【两件套】￥62.03'
[22:33:03]    14. '撞色马甲+牛仔裤【仔裤【三件￥62.03'
[22:33:03]    15. '+领带+牛仔￥79.11'
[22:33:03]    16. '参考分类90码数'
[22:33:03]    17. '参考分类100码数'
[22:33:03]    18. '110码数'
[22:33:03]    19. '120码数'
[22:33:03]    20. '130码数'
[22:33:03]    21. '140码数'
[22:33:03]    22. '免费服务退货包运费(商家赠送)'
[22:33:03]    23. '一次选多款，不满意可极速退款>'
[22:33:03] 🎯 找到尺码区域开始位置: 第3行 '请选择：颜色分类参考分类'
[22:33:03] 📝 尺码区域文本无数字: '颜色分类'
[22:33:03] 🔧 过滤￥符号后的文本: '撞色毛衣马甲'
[22:33:03] 📝 尺码区域文本无数字: '撞色毛衣马甲'
[22:33:03] 🔧 过滤￥符号后的文本: '黄色衬衫+领带【两件套】'
[22:33:03] 📝 尺码区域文本无数字: '黄色衬衫+领带【两件套】'
[22:33:03] 🔧 过滤￥符号后的文本: '蓝色破洞牛仔裤'
[22:33:03] 📝 尺码区域文本无数字: '蓝色破洞牛仔裤'
[22:33:03] 📝 尺码区域文本无数字: '撞色马衬衫+领带'
[22:33:03] 📝 尺码区域文本无数字: 'W'
[22:33:03] ⚠️ 跳过无效尺码: 1141 (来源: '1141')
[22:33:03] 📝 尺码区域文本无数字: '黄色衬衫+领带+牛'
[22:33:03] 📝 尺码区域文本无数字: '撞色马甲+黄色衬衫'
[22:33:03] 🔧 过滤￥符号后的文本: '撞色马甲+牛仔裤【两件套】'
[22:33:03] 📝 尺码区域文本无数字: '撞色马甲+牛仔裤【两件套】'
[22:33:03] 🔧 过滤￥符号后的文本: '撞色马甲+牛仔裤【仔裤【三件'
[22:33:03] 📝 尺码区域文本无数字: '撞色马甲+牛仔裤【仔裤【三件'
[22:33:03] 🔧 过滤￥符号后的文本: '+领带+牛仔'
[22:33:03] 📝 尺码区域文本无数字: '+领带+牛仔'
[22:33:03] ✅ 提取尺码: 90 (来源: '参考分类90码数')
[22:33:03] 🔍 处理bbox: [4, 405, 73, 428] (长度: 4)
[22:33:03] ✅ 计算坐标成功: (38, 416)
[22:33:03] 📍 记录尺码坐标: 90 -> (38, 416)
[22:33:03] ✅ 提取尺码: 100 (来源: '参考分类100码数')
[22:33:03] 🔍 处理bbox: [4, 405, 73, 428] (长度: 4)
[22:33:03] ✅ 计算坐标成功: (38, 416)
[22:33:03] 📍 记录尺码坐标: 100 -> (38, 416)
[22:33:03] ✅ 提取尺码: 110 (来源: '110码数')
[22:33:03] 🔍 处理bbox: [161, 440, 222, 462] (长度: 4)
[22:33:03] ✅ 计算坐标成功: (191, 451)
[22:33:03] 📍 记录尺码坐标: 110 -> (191, 451)
[22:33:03] ✅ 提取尺码: 120 (来源: '120码数')
[22:33:03] 🔍 处理bbox: [239, 440, 302, 462] (长度: 4)
[22:33:03] ✅ 计算坐标成功: (270, 451)
[22:33:03] 📍 记录尺码坐标: 120 -> (270, 451)
[22:33:03] ✅ 提取尺码: 130 (来源: '130码数')
[22:33:03] 🔍 处理bbox: [317, 438, 382, 463] (长度: 4)
[22:33:03] ✅ 计算坐标成功: (349, 450)
[22:33:03] 📍 记录尺码坐标: 130 -> (349, 450)
[22:33:03] ✅ 提取尺码: 140 (来源: '140码数')
[22:33:03] 🔍 处理bbox: [8, 478, 73, 503] (长度: 4)
[22:33:03] ✅ 计算坐标成功: (40, 490)
[22:33:03] 📍 记录尺码坐标: 140 -> (40, 490)
[22:33:03] 🛑 遇到停止关键词，结束尺码提取: '免费服务退货包运费(商家赠送)'
[22:33:03] 📊 尺码提取结果: 数字=[90, 100, 110, 120, 130, 140], 范围=90-140, 原始文本数量=6
[22:33:03] 尺码信息: {'optimized_range': '90-140', 'original_texts': ['参考分类90码数', '参考分类100码数', '110码数', '120码数', '130码数', '140码数'], 'size_numbers': [90, 100, 110, 120, 130, 140]}
[22:33:03] 📍 已记录尺码坐标: {100: (38, 416), 110: (191, 451), 120: (270, 451), 130: (349, 450), 140: (40, 490), 150: (90, 455), 90: (38, 416), 80: (37, 372)}
[22:33:03] 🔍 发现颜色分类嵌入格式: '请选择：颜色分类参考分类' → 提取: '参考分类'
[22:33:03] 🎯 找到颜色分类开始位置: 第2行 '请选择：颜色分类参考分类'
[22:33:03] 🔍 发现颜色嵌入格式: '颜色分类' → 提取: '分类'
[22:33:03] 🎯 发现嵌入颜色分类: '分类' (来源: '颜色分类')
[22:33:04] 🎯 找到颜色分类开始位置: 第3行 '颜色分类'
[22:33:04] 🎯 找到颜色分类结束位置: 第15行 '参考分类90码数'
[22:33:04] 🔍 开始提取颜色分类: 从第4行到第14行
[22:33:04] ✅ 保留有效文本: '撞色毛衣马甲￥35.96'
[22:33:04] ✅ 保留有效文本: '黄色衬衫+领带【两件套】￥39.55'
[22:33:04] ✅ 保留有效文本: '蓝色破洞牛仔裤￥35.96'
[22:33:04] ✅ 保留有效文本: '撞色马衬衫+领带'
[22:33:04] 🚫 过滤纯英文文本: 'W'
[22:33:04] 🚫 过滤纯数字文本: '1141'
[22:33:04] ✅ 保留有效文本: '黄色衬衫+领带+牛'
[22:33:04] ✅ 保留有效文本: '撞色马甲+黄色衬衫'
[22:33:04] ✅ 保留有效文本: '撞色马甲+牛仔裤【两件套】￥62.03'
[22:33:04] ✅ 保留有效文本: '撞色马甲+牛仔裤【仔裤【三件￥62.03'
[22:33:04] ✅ 保留有效文本: '+领带+牛仔￥79.11'
[22:33:04] ✅ 添加嵌入颜色分类: '分类'
[22:33:04] 🔍 检查颜色文本: '撞色毛衣马甲￥35.96' (长度: 12)
[22:33:04] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [ 23 ... 227]
[22:33:04] 🔍 价格检测结果: True
[22:33:04] 🔍 价格检测: 文本='撞色毛衣马甲￥35.96', 检测到价格=True, 提取价格=35.96
[22:33:04] 🔍 处理bbox: [23, 210, 98, 227] (长度: 4)
[22:33:04] ✅ 计算坐标成功: (60, 218)
[22:33:04] 📍 记录颜色坐标: 撞色毛衣马甲 -> (60, 218)
[22:33:04] ✅ 提取到带价格颜色: 撞色毛衣马甲 -> ¥35.96 (来源: 撞色毛衣马甲￥35.96)
[22:33:04] 🔍 检查颜色文本: '黄色衬衫+领带【两件套】￥39.55' (长度: 18)
[22:33:04] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [144 ... 227]
[22:33:04] 🔍 价格检测结果: True
[22:33:04] 🔍 价格检测: 文本='黄色衬衫+领带【两件套】￥39.55', 检测到价格=True, 提取价格=39.55
[22:33:04] 🔍 处理bbox: [144, 211, 226, 227] (长度: 4)
[22:33:04] ✅ 计算坐标成功: (185, 219)
[22:33:04] 📍 记录颜色坐标: 黄色衬衫+领带【两件套】 -> (185, 219)
[22:33:04] ✅ 提取到带价格颜色: 黄色衬衫+领带【两件套】 -> ¥39.55 (来源: 黄色衬衫+领带【两件套】￥39.55)
[22:33:04] 🔍 检查颜色文本: '蓝色破洞牛仔裤￥35.96' (长度: 13)
[22:33:04] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [252 ... 228]
[22:33:04] 🔍 价格检测结果: True
[22:33:04] 🔍 价格检测: 文本='蓝色破洞牛仔裤￥35.96', 检测到价格=True, 提取价格=35.96
[22:33:04] 🔍 处理bbox: [252, 209, 339, 228] (长度: 4)
[22:33:04] ✅ 计算坐标成功: (295, 218)
[22:33:04] 📍 记录颜色坐标: 蓝色破洞牛仔裤 -> (295, 218)
[22:33:04] ✅ 提取到带价格颜色: 蓝色破洞牛仔裤 -> ¥35.96 (来源: 蓝色破洞牛仔裤￥35.96)
[22:33:04] 🔍 检查颜色文本: '撞色马衬衫+领带' (长度: 8)
[22:33:04] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [377 ... 227]
[22:33:04] 🔍 价格检测结果: False
[22:33:04] 🔍 处理bbox: [377, 210, 421, 227] (长度: 4)
[22:33:04] ✅ 计算坐标成功: (399, 218)
[22:33:04] 📍 记录颜色坐标: 撞色马衬衫+领带 -> (399, 218)
[22:33:04] ✅ 提取到无价格颜色: 撞色马衬衫+领带
[22:33:04] 🔍 检查颜色文本: '黄色衬衫+领带+牛' (长度: 9)
[22:33:04] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [128 ... 365]
[22:33:04] 🔍 价格检测结果: False
[22:33:04] 🔍 处理bbox: [128, 347, 230, 365] (长度: 4)
[22:33:04] ✅ 计算坐标成功: (179, 356)
[22:33:04] 📍 记录颜色坐标: 黄色衬衫+领带+牛 -> (179, 356)
[22:33:04] ✅ 提取到无价格颜色: 黄色衬衫+领带+牛
[22:33:04] 🔍 检查颜色文本: '撞色马甲+黄色衬衫' (长度: 9)
[22:33:04] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [242 ... 364]
[22:33:04] 🔍 价格检测结果: False
[22:33:04] 🔍 处理bbox: [242, 346, 348, 364] (长度: 4)
[22:33:04] ✅ 计算坐标成功: (295, 355)
[22:33:04] 📍 记录颜色坐标: 撞色马甲+黄色衬衫 -> (295, 355)
[22:33:04] ✅ 提取到无价格颜色: 撞色马甲+黄色衬衫
[22:33:04] 🔍 检查颜色文本: '撞色马甲+牛仔裤【两件套】￥62.03' (长度: 19)
[22:33:04] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [  8 ... 365]
[22:33:04] 🔍 价格检测结果: True
[22:33:04] 🔍 价格检测: 文本='撞色马甲+牛仔裤【两件套】￥62.03', 检测到价格=True, 提取价格=62.03
[22:33:04] 🔍 处理bbox: [8, 347, 115, 365] (长度: 4)
[22:33:04] ✅ 计算坐标成功: (61, 356)
[22:33:04] 📍 记录颜色坐标: 撞色马甲+牛仔裤【两件套】 -> (61, 356)
[22:33:04] ✅ 提取到带价格颜色: 撞色马甲+牛仔裤【两件套】 -> ¥62.03 (来源: 撞色马甲+牛仔裤【两件套】￥62.03)
[22:33:04] 🔍 检查颜色文本: '撞色马甲+牛仔裤【仔裤【三件￥62.03' (长度: 20)
[22:33:04] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [  8 ... 365]
[22:33:04] 🔍 价格检测结果: True
[22:33:04] 🔍 价格检测: 文本='撞色马甲+牛仔裤【仔裤【三件￥62.03', 检测到价格=True, 提取价格=62.03
[22:33:04] 🔍 处理bbox: [8, 347, 115, 365] (长度: 4)
[22:33:04] ✅ 计算坐标成功: (61, 356)
[22:33:04] 📍 记录颜色坐标: 撞色马甲+牛仔裤【仔裤【三件 -> (61, 356)
[22:33:04] ✅ 提取到带价格颜色: 撞色马甲+牛仔裤【仔裤【三件 -> ¥62.03 (来源: 撞色马甲+牛仔裤【仔裤【三件￥62.03)
[22:33:04] 🔍 检查颜色文本: '+领带+牛仔￥79.11' (长度: 12)
[22:33:04] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [243 ... 378]
[22:33:04] 🔍 价格检测结果: True
[22:33:04] 🔍 价格检测: 文本='+领带+牛仔￥79.11', 检测到价格=True, 提取价格=79.11
[22:33:04] 🔍 处理bbox: [243, 362, 348, 378] (长度: 4)
[22:33:04] ✅ 计算坐标成功: (295, 370)
[22:33:04] 📍 记录颜色坐标: +领带+牛仔 -> (295, 370)
[22:33:04] ✅ 提取到带价格颜色: +领带+牛仔 -> ¥79.11 (来源: +领带+牛仔￥79.11)
[22:33:04] 🔍 检查颜色文本: '分类' (长度: 2)
[22:33:04] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [  3 ... 107]
[22:33:04] 🔍 价格检测结果: False
[22:33:04] 🔍 处理bbox: [3, 82, 74, 107] (长度: 4)
[22:33:04] ✅ 计算坐标成功: (38, 94)
[22:33:04] 📍 记录颜色坐标: 分类 -> (38, 94)
[22:33:04] ✅ 提取到无价格颜色: 分类
[22:33:04] 🎨 颜色分类提取完成: 共提取到 10 个颜色
[22:33:04] 📍 坐标记录完成: 共记录 10 个坐标
[22:33:04] 提取到颜色分类: [{'pure_name': '撞色毛衣马甲', 'original_text': '撞色毛衣马甲￥35.96', 'has_direct_price': True, 'direct_price': '35.96', 'bbox': array([ 23, ..., 227], dtype=int16)}, {'pure_name': '黄色衬衫+领带【两件套】', 'original_text': '黄色衬衫+领带【两件套】￥39.55', 'has_direct_price': True, 'direct_price': '39.55', 'bbox': array([144, ..., 227], dtype=int16)}, {'pure_name': '蓝色破洞牛仔裤', 'original_text': '蓝色破洞牛仔裤￥35.96', 'has_direct_price': True, 'direct_price': '35.96', 'bbox': array([252, ..., 228], dtype=int16)}, {'pure_name': '撞色马衬衫+领带', 'original_text': '撞色马衬衫+领带', 'has_direct_price': False, 'direct_price': None, 'bbox': array([377, ..., 227], dtype=int16)}, {'pure_name': '黄色衬衫+领带+牛', 'original_text': '黄色衬衫+领带+牛', 'has_direct_price': False, 'direct_price': None, 'bbox': array([128, ..., 365], dtype=int16)}, {'pure_name': '撞色马甲+黄色衬衫', 'original_text': '撞色马甲+黄色衬衫', 'has_direct_price': False, 'direct_price': None, 'bbox': array([242, ..., 364], dtype=int16)}, {'pure_name': '撞色马甲+牛仔裤【两件套】', 'original_text': '撞色马甲+牛仔裤【两件套】￥62.03', 'has_direct_price': True, 'direct_price': '62.03', 'bbox': array([  8, ..., 365], dtype=int16)}, {'pure_name': '撞色马甲+牛仔裤【仔裤【三件', 'original_text': '撞色马甲+牛仔裤【仔裤【三件￥62.03', 'has_direct_price': True, 'direct_price': '62.03', 'bbox': array([  8, ..., 365], dtype=int16)}, {'pure_name': '+领带+牛仔', 'original_text': '+领带+牛仔￥79.11', 'has_direct_price': True, 'direct_price': '79.11', 'bbox': array([243, ..., 378], dtype=int16)}, {'pure_name': '分类', 'original_text': '分类', 'has_direct_price': False, 'direct_price': None, 'bbox': array([  3, ..., 107], dtype=int16)}]
[22:33:04] 颜色分类数量: 10
[22:33:04] 📍 已记录颜色坐标: {'撞色毛衣马甲': (60, 218), '黄色衬衫+领带【两件套】': (185, 219), '蓝色破洞牛仔裤': (295, 218), '撞色马衬衫+领带': (399, 218), '黄色衬衫+领带+牛': (179, 356), '撞色马甲+黄色衬衫': (295, 355), '撞色马甲+牛仔裤【两件套】': (61, 356), '撞色马甲+牛仔裤【仔裤【三件': (61, 356), '+领带+牛仔': (295, 370), '分类': (38, 94)}
[22:33:04] 🎯 第一类商品：从颜色直接提取价格
[22:33:04] ✅ 直接价格: 撞色毛衣马甲 -> 35.96
[22:33:04] ✅ 直接价格: 黄色衬衫+领带【两件套】 -> 39.55
[22:33:04] ✅ 直接价格: 蓝色破洞牛仔裤 -> 35.96
[22:33:04] ✅ 直接价格: 撞色马甲+牛仔裤【两件套】 -> 62.03
[22:33:04] ✅ 直接价格: 撞色马甲+牛仔裤【仔裤【三件 -> 62.03
[22:33:04] ✅ 直接价格: +领带+牛仔 -> 79.11
[22:33:04] 🔍 商品类型分析:
[22:33:04]    颜色数量: 10
[22:33:04]    颜色直接带价格: True
[22:33:04]    页面有券前/券后价格: False
[22:33:04]    尺码带价格: False
[22:33:04] 📊 分析类型: type1_multiple_colors_direct_prices
[22:33:04] 🔧 处理方案: basic
[22:33:04] 📊 智能分析结果:
[22:33:04]   优化尺码范围: 90-140
[22:33:04]   原始尺码文本: ['参考分类90码数', '参考分类100码数', '110码数', '120码数', '130码数', '140码数']
[22:33:04]   颜色分类: ['撞色毛衣马甲', '黄色衬衫+领带【两件套】', '蓝色破洞牛仔裤', '撞色马衬衫+领带', '黄色衬衫+领带+牛', '撞色马甲+黄色衬衫', '撞色马甲+牛仔裤【两件套】', '撞色马甲+牛仔裤【仔裤【三件', '+领带+牛仔', '分类']
[22:33:04]   颜色价格: {'撞色毛衣马甲': '35.96', '黄色衬衫+领带【两件套】': '39.55', '蓝色破洞牛仔裤': '35.96', '撞色马甲+牛仔裤【两件套】': '62.03', '撞色马甲+牛仔裤【仔裤【三件': '62.03', '+领带+牛仔': '79.11'}
[22:33:04]   分析类型: type1_multiple_colors_direct_prices
[22:33:04]   处理方案: basic
[22:33:04] 📝 使用基础处理方案，直接保存分析结果
[22:33:04] 💾 保存分析结果到Excel...
[22:33:04] 找到目标商品链接在第 50 行
[22:33:04] ✅ 保存有价格颜色: 撞色毛衣马甲 -> 35.96
[22:33:04] ✅ 保存有价格颜色: 黄色衬衫+领带【两件套】 -> 39.55
[22:33:04] ✅ 保存有价格颜色: 蓝色破洞牛仔裤 -> 35.96
[22:33:04] ✅ 保存有价格颜色: 撞色马衬衫+领带 -> 未获取
[22:33:04] ✅ 保存有价格颜色: 黄色衬衫+领带+牛 -> 未获取
[22:33:04] ✅ 保存有价格颜色: 撞色马甲+黄色衬衫 -> 未获取
[22:33:04] ✅ 保存有价格颜色: 撞色马甲+牛仔裤【两件套】 -> 62.03
[22:33:04] ✅ 保存有价格颜色: 撞色马甲+牛仔裤【仔裤【三件 -> 62.03
[22:33:04] ✅ 保存有价格颜色: +领带+牛仔 -> 79.11
[22:33:04] ✅ 保存有价格颜色: 分类 -> 未获取
[22:33:04] 找到下一个商品链接在第 61 行
[22:33:04] 清空第 51 行到第 60 行中的 0 行有内容数据，保留空行
[22:33:04] ✅ 分析结果已保存到商品4下方: 商品图片\商品SKU信息.xlsx
[22:33:04]    插入了 12 行新数据
[22:33:04] ==================================================
[22:33:04] ✅ 智能OCR分析完成
[22:33:04] ==================================================
[22:33:04] ✅ 详情页图片捕获完成
[22:33:04] ✅ OCR分析和Excel保存已在详情页处理中完成
[22:33:04] ✅ 第 4 个商品处理完成
[22:33:04] 🔄 准备处理下一个商品...
[22:33:04] 🔄 开始返回搜索页面...
[22:33:04] 移动到位置(470,590)...
[22:33:05] 点击鼠标右键...
[22:33:06] 再次点击鼠标右键...
[22:33:07] ✅ 返回搜索页面操作完成
[22:33:07] 
============================================================
[22:33:07] 🎯 开始处理第 5 个商品
[22:33:07] 商品链接: https://mobile.yangkeduo.com/goods2.html?ps=ttcnJUxbGg
[22:33:07] ============================================================
[22:33:07] 步骤1: 设置剪贴板内容...
[22:33:07] 已复制商品链接到剪贴板: https://mobile.yangkeduo.com/goods2.html?ps=ttcnJUxbGg
[22:33:08] ✅ 剪贴板内容验证成功
[22:33:08] 步骤2: 点击搜索框并输入链接...
[22:33:08] 移动到位置(480,96)并点击...
[22:33:08] 按下Ctrl+A全选...
[22:33:08] 执行AutoHotkey脚本: ctrl_a.ahk
[22:33:10] ✅ AutoHotkey脚本执行成功: ctrl_a
[22:33:10] 粘贴商品链接...
[22:33:10] 使用外部脚本软件执行真正的Ctrl+V...
[22:33:10] Python的键盘模拟无法被escrcpy识别，尝试其他方案
[22:33:10] 方法1: 尝试AutoHotkey...
[22:33:10] 找到脚本文件: paste_v2.ahk
[22:33:10] 检查路径: AutoHotkey.exe
[22:33:10] 检查路径: C:\Program Files\AutoHotkey\AutoHotkey.exe
[22:33:10] 检查路径: C:\Program Files (x86)\AutoHotkey\AutoHotkey.exe
[22:33:10] 检查路径: C:\Program Files\AutoHotkey\v2\AutoHotkey.exe
[22:33:10] 找到AutoHotkey: C:\Program Files\AutoHotkey\v2\AutoHotkey.exe
[22:33:10] 执行AutoHotkey脚本: paste_v2.ahk
[22:33:12] AutoHotkey返回码: 0
[22:33:12] ✅ AutoHotkey执行成功
[22:33:12] ✅ Ctrl+V操作执行成功
[22:33:15] 步骤3: 点击搜索按钮...
[22:33:15] 正在查找图片: SOUSUO2.png (路径: image\IQOO\SOUSUO2.png)
[22:33:15] 找到图片 SOUSUO2.png，位置: (641, 81)
[22:33:15] 点击位置: (664, 96)
[22:33:15] 已点击图片: SOUSUO2.png
[22:33:15] 步骤4: 等待3秒进入商品页面...
[22:33:18] 步骤5: 执行从右往左的滑动操作...
[22:33:18] 开始执行从右往左的滑动操作...
[22:33:18] 📍 起始位置: (630, 185)
[22:33:18] 📍 结束位置: (284, 185)
[22:33:18] 📏 滑动距离: 346 像素
[22:33:18] ⏱️ 滑动时长: 0.3 秒
[22:33:18] 移动到起始位置(630, 185)
[22:33:18] 按住左键从(630, 185)拖拽到(284, 185)
[22:33:19] 开始滑动，时长0.3秒...
[22:33:20] 释放左键，滑动操作完成
[22:33:20] ✅ 从右往左的滑动操作完成
[22:33:20] 步骤6: 移动到位置(470,185)...
[22:33:20] 已移动到位置(470,185)
[22:33:20] 步骤7: 执行鼠标点击和长按操作...
[22:33:20] 点击鼠标左键一次
[22:33:21] 在位置(475, 323)长按鼠标左键0.5秒
[22:33:22] 鼠标操作完成
[22:33:23] 步骤8: 查找并点击保存按钮...
[22:33:23] 尝试查找图片 BAOCUN1.png (第1次/共3次)
[22:33:23] 正在查找图片: BAOCUN1.png (路径: image\IQOO\BAOCUN1.png)
[22:33:23] 找到图片 BAOCUN1.png，位置: (455, 808)
[22:33:24] 移动到图片上，等待0.5秒...
[22:33:24] 已点击图片: BAOCUN1.png
[22:33:24] 步骤9: 检测保存状态...
[22:33:24] 开始检测图片: BAOCUNZHONG.png (路径: image\IQOO\BAOCUNZHONG.png)
[22:33:24] 最多检测 80 次，每隔 0.2 秒检测一次
[22:33:24] 第 1/80 次检测...
[22:33:25] 第 2/80 次检测...
[22:33:25] ✅ 找到图片 BAOCUNZHONG.png！位置: (443, 507)
[22:33:25] ✅ 检测到保存中状态，等待保存完成...
[22:33:25] 等待5秒让图片完全保存到手机...
[22:33:30] 步骤10: 复制手机图片到商品 5 文件夹...
[22:33:30] 第 1/3 次尝试复制图片...
[22:33:30] 开始从MTP设备复制图片到商品 5 文件夹...
[22:33:30] 正在使用Windows Shell API查找MTP设备...
[22:33:30] 找到设备: iQOO Z1
[22:33:30] 进入文件夹: 内部存储设备
[22:33:30] 进入文件夹: DCIM
[22:33:30] 进入文件夹: Pindd
[22:33:30] 进入文件夹: goods
[22:33:30] 成功找到目标文件夹: 内部存储设备\DCIM\Pindd\goods
[22:33:30] 目标路径: F:\自动捕获数据\商品图片\5
[22:33:30] 复制文件: 1753626501373-1315797334.jpg
[22:33:30] ✅ 成功复制: 1753626501373-1315797334.jpg
[22:33:30] 复制文件: 1753626501484-1645306700.jpg
[22:33:30] ✅ 成功复制: 1753626501484-1645306700.jpg
[22:33:30] 复制文件: 1753626501256--945688330.jpg
[22:33:30] ✅ 成功复制: 1753626501256--945688330.jpg
[22:33:30] 复制文件: 1753626501297-483977703.jpg
[22:33:30] ✅ 成功复制: 1753626501297-483977703.jpg
[22:33:30] 复制文件: 1753626501401--1182398115.jpg
[22:33:30] ✅ 成功复制: 1753626501401--1182398115.jpg
[22:33:30] 复制文件: 1753626501042--850234244.jpg
[22:33:30] ✅ 成功复制: 1753626501042--850234244.jpg
[22:33:30] 复制文件: 1753626500939-199319035.jpg
[22:33:30] ✅ 成功复制: 1753626500939-199319035.jpg
[22:33:30] 复制文件: 1753626501073--278957689.jpg
[22:33:30] ✅ 成功复制: 1753626501073--278957689.jpg
[22:33:30] 复制文件: 1753626501332-1706905320.jpg
[22:33:31] ✅ 成功复制: 1753626501332-1706905320.jpg
[22:33:31] 复制文件: 1753626501176--1680485554.jpg
[22:33:31] ✅ 成功复制: 1753626501176--1680485554.jpg
[22:33:31] 复制文件: 1753626501138--1193025293.jpg
[22:33:31] ✅ 成功复制: 1753626501138--1193025293.jpg
[22:33:31] 复制文件: 1753626501207--778938302.jpg
[22:33:31] ✅ 成功复制: 1753626501207--778938302.jpg
[22:33:31] 复制文件: 1753626501435-1066972225.jpg
[22:33:31] ✅ 成功复制: 1753626501435-1066972225.jpg
[22:33:31] 复制文件: 1753626518152-660672411.jpg
[22:33:31] ✅ 成功复制: 1753626518152-660672411.jpg
[22:33:31] 复制文件: 1753626518186-2125388435.jpg
[22:33:31] ✅ 成功复制: 1753626518186-2125388435.jpg
[22:33:31] 复制文件: 1753626518219-1804250823.jpg
[22:33:31] ✅ 成功复制: 1753626518219-1804250823.jpg
[22:33:31] 复制文件: 1753626518249--1065563707.jpg
[22:33:31] ✅ 成功复制: 1753626518249--1065563707.jpg
[22:33:31] 复制文件: 1753626518295-152127411.jpg
[22:33:31] ✅ 成功复制: 1753626518295-152127411.jpg
[22:33:31] 复制文件: 1753626518357-1552964844.jpg
[22:33:31] ✅ 成功复制: 1753626518357-1552964844.jpg
[22:33:31] 复制文件: 1753626518406-1752797653.jpg
[22:33:31] ✅ 成功复制: 1753626518406-1752797653.jpg
[22:33:31] 复制文件: 1753626518441--1753262296.jpg
[22:33:31] ✅ 成功复制: 1753626518441--1753262296.jpg
[22:33:31] 复制文件: 1753626518489-842254062.jpg
[22:33:31] ✅ 成功复制: 1753626518489-842254062.jpg
[22:33:31] 复制文件: 1753626518530--255483989.jpg
[22:33:32] ✅ 成功复制: 1753626518530--255483989.jpg
[22:33:32] 复制文件: 1753626518571--1285728568.jpg
[22:33:32] ✅ 成功复制: 1753626518571--1285728568.jpg
[22:33:32] 复制文件: 1753626518627-396311468.jpg
[22:33:32] ✅ 成功复制: 1753626518627-396311468.jpg
[22:33:32] 复制文件: 1753626518662-1252897153.jpg
[22:33:32] ✅ 成功复制: 1753626518662-1252897153.jpg
[22:33:32] 复制文件: 1753626518696--1537897804.jpg
[22:33:32] ✅ 成功复制: 1753626518696--1537897804.jpg
[22:33:32] 复制文件: 1753626518746--1146868097.jpg
[22:33:32] ✅ 成功复制: 1753626518746--1146868097.jpg
[22:33:32] 复制文件: 1753626518780--1712757656.jpg
[22:33:32] ✅ 成功复制: 1753626518780--1712757656.jpg
[22:33:32] 复制文件: 1753626518827-184866696.jpg
[22:33:32] ✅ 成功复制: 1753626518827-184866696.jpg
[22:33:32] 复制文件: 1753626584506--174181552.jpg
[22:33:32] ✅ 成功复制: 1753626584506--174181552.jpg
[22:33:32] 复制文件: 1753626584568-649400088.jpg
[22:33:32] ✅ 成功复制: 1753626584568-649400088.jpg
[22:33:32] 复制文件: 1753626584616--75726748.jpg
[22:33:32] ✅ 成功复制: 1753626584616--75726748.jpg
[22:33:32] 复制文件: 1753626584693--1636531876.jpg
[22:33:32] ✅ 成功复制: 1753626584693--1636531876.jpg
[22:33:32] 复制文件: 1753626584727-1492996970.jpg
[22:33:32] ✅ 成功复制: 1753626584727-1492996970.jpg
[22:33:32] 复制文件: 1753626584762-2021427306.jpg
[22:33:32] ✅ 成功复制: 1753626584762-2021427306.jpg
[22:33:32] 复制文件: 1753626603375-1223281507.jpg
[22:33:33] ✅ 成功复制: 1753626603375-1223281507.jpg
[22:33:33] 复制文件: 1753626603420--59820335.jpg
[22:33:33] ✅ 成功复制: 1753626603420--59820335.jpg
[22:33:33] 复制文件: 1753626603446--1882576375.jpg
[22:33:33] ✅ 成功复制: 1753626603446--1882576375.jpg
[22:33:33] 复制文件: 1753626603487--2039576347.jpg
[22:33:33] ✅ 成功复制: 1753626603487--2039576347.jpg
[22:33:33] 复制文件: 1753626603536--320016844.jpg
[22:33:33] ✅ 成功复制: 1753626603536--320016844.jpg
[22:33:33] 复制文件: 1753626603577--1854090630.jpg
[22:33:33] ✅ 成功复制: 1753626603577--1854090630.jpg
[22:33:33] 复制文件: 1753626660614--1428446268.jpg
[22:33:33] ✅ 成功复制: 1753626660614--1428446268.jpg
[22:33:33] 复制文件: 1753626660666-1209359625.jpg
[22:33:33] ✅ 成功复制: 1753626660666-1209359625.jpg
[22:33:33] 复制文件: 1753626660709-1902243945.jpg
[22:33:33] ✅ 成功复制: 1753626660709-1902243945.jpg
[22:33:33] 复制文件: 1753626660740-333858737.jpg
[22:33:33] ✅ 成功复制: 1753626660740-333858737.jpg
[22:33:33] 复制文件: 1753626660786-1052787518.jpg
[22:33:33] ✅ 成功复制: 1753626660786-1052787518.jpg
[22:33:33] 复制文件: 1753626660849-877359325.jpg
[22:33:33] ✅ 成功复制: 1753626660849-877359325.jpg
[22:33:33] 复制文件: 1753626660881-1230956995.jpg
[22:33:33] ✅ 成功复制: 1753626660881-1230956995.jpg
[22:33:33] 复制文件: 1753626680297--251064384.jpg
[22:33:34] ✅ 成功复制: 1753626680297--251064384.jpg
[22:33:34] 复制文件: 1753626680360-1069102764.jpg
[22:33:34] ✅ 成功复制: 1753626680360-1069102764.jpg
[22:33:34] 复制文件: 1753626680401--482777265.jpg
[22:33:34] ✅ 成功复制: 1753626680401--482777265.jpg
[22:33:34] 复制文件: 1753626680449--1838772425.jpg
[22:33:34] ✅ 成功复制: 1753626680449--1838772425.jpg
[22:33:34] 复制文件: 1753626680498--618659008.jpg
[22:33:34] ✅ 成功复制: 1753626680498--618659008.jpg
[22:33:34] 复制文件: 1753626680546--1085139102.jpg
[22:33:34] ✅ 成功复制: 1753626680546--1085139102.jpg
[22:33:34] 复制文件: 1753626680588-1076043545.jpg
[22:33:34] ✅ 成功复制: 1753626680588-1076043545.jpg
[22:33:34] 复制文件: 1753626680636-552573400.jpg
[22:33:34] ✅ 成功复制: 1753626680636-552573400.jpg
[22:33:34] 复制文件: 1753626680677--1331103116.jpg
[22:33:34] ✅ 成功复制: 1753626680677--1331103116.jpg
[22:33:34] 复制文件: 1753626740875--1213573602.jpg
[22:33:34] ✅ 成功复制: 1753626740875--1213573602.jpg
[22:33:34] 复制文件: 1753626740918--268036214.jpg
[22:33:35] ✅ 成功复制: 1753626740918--268036214.jpg
[22:33:35] 复制文件: 1753626740959--1636983126.jpg
[22:33:35] ✅ 成功复制: 1753626740959--1636983126.jpg
[22:33:35] 复制文件: 1753626741004--597443972.jpg
[22:33:35] ✅ 成功复制: 1753626741004--597443972.jpg
[22:33:35] 复制文件: 1753626741049--805566952.jpg
[22:33:35] ✅ 成功复制: 1753626741049--805566952.jpg
[22:33:35] 复制文件: 1753626741112-715278496.jpg
[22:33:35] ✅ 成功复制: 1753626741112-715278496.jpg
[22:33:35] 复制文件: 1753626741153--106487342.jpg
[22:33:35] ✅ 成功复制: 1753626741153--106487342.jpg
[22:33:35] 复制文件: 1753626741194-1887506479.jpg
[22:33:35] ✅ 成功复制: 1753626741194-1887506479.jpg
[22:33:35] 复制文件: 1753626741230-639229492.jpg
[22:33:35] ✅ 成功复制: 1753626741230-639229492.jpg
[22:33:35] 复制文件: 1753626741263-1180235664.jpg
[22:33:35] ✅ 成功复制: 1753626741263-1180235664.jpg
[22:33:35] 复制文件: 1753626741298-1944811228.jpg
[22:33:35] ✅ 成功复制: 1753626741298-1944811228.jpg
[22:33:35] 复制文件: 1753626741339--1475076813.jpg
[22:33:35] ✅ 成功复制: 1753626741339--1475076813.jpg
[22:33:35] 复制文件: 1753626741374-913913680.jpg
[22:33:35] ✅ 成功复制: 1753626741374-913913680.jpg
[22:33:35] 复制文件: 1753626741409-1189978779.jpg
[22:33:35] ✅ 成功复制: 1753626741409-1189978779.jpg
[22:33:35] 复制文件: 1753626762896--238727143.jpg
[22:33:36] ✅ 成功复制: 1753626762896--238727143.jpg
[22:33:36] 复制文件: 1753626762954--655740314.jpg
[22:33:36] ✅ 成功复制: 1753626762954--655740314.jpg
[22:33:36] 复制文件: 1753626762988-953387863.jpg
[22:33:36] ✅ 成功复制: 1753626762988-953387863.jpg
[22:33:36] 复制文件: 1753626763036--744338388.jpg
[22:33:36] ✅ 成功复制: 1753626763036--744338388.jpg
[22:33:36] 复制文件: 1753626763085-730639924.jpg
[22:33:36] ✅ 成功复制: 1753626763085-730639924.jpg
[22:33:36] 复制文件: 1753626763126--112046204.jpg
[22:33:36] ✅ 成功复制: 1753626763126--112046204.jpg
[22:33:36] 复制文件: 1753626763160--1229046725.jpg
[22:33:36] ✅ 成功复制: 1753626763160--1229046725.jpg
[22:33:36] 复制文件: 1753626763195-203300966.jpg
[22:33:36] ✅ 成功复制: 1753626763195-203300966.jpg
[22:33:36] 复制文件: 1753626804293-1123631752.jpg
[22:33:36] ✅ 成功复制: 1753626804293-1123631752.jpg
[22:33:36] 复制文件: 1753626804337--1088434480.jpg
[22:33:36] ✅ 成功复制: 1753626804337--1088434480.jpg
[22:33:36] 复制文件: 1753626804364--106025177.jpg
[22:33:37] ✅ 成功复制: 1753626804364--106025177.jpg
[22:33:37] 复制文件: 1753626804413--1301755960.jpg
[22:33:37] ✅ 成功复制: 1753626804413--1301755960.jpg
[22:33:37] 复制文件: 1753626804462-1854031190.jpg
[22:33:37] ✅ 成功复制: 1753626804462-1854031190.jpg
[22:33:37] 复制文件: 1753626804516--580283861.jpg
[22:33:37] ✅ 成功复制: 1753626804516--580283861.jpg
[22:33:37] 复制文件: 1753626804551--876380769.jpg
[22:33:37] ✅ 成功复制: 1753626804551--876380769.jpg
[22:33:37] 成功复制 87 个文件
[22:33:37] ✅ 成功复制 87 个图片到商品 5 文件夹
[22:33:37] 复制的文件:
[22:33:37]   - 1753626501373-1315797334.jpg
[22:33:37]   - 1753626501484-1645306700.jpg
[22:33:37]   - 1753626501256--945688330.jpg
[22:33:37]   - 1753626501297-483977703.jpg
[22:33:37]   - 1753626501401--1182398115.jpg
[22:33:37]   - 1753626501042--850234244.jpg
[22:33:37]   - 1753626500939-199319035.jpg
[22:33:37]   - 1753626501073--278957689.jpg
[22:33:37]   - 1753626501332-1706905320.jpg
[22:33:37]   - 1753626501176--1680485554.jpg
[22:33:37]   - 1753626501138--1193025293.jpg
[22:33:37]   - 1753626501207--778938302.jpg
[22:33:37]   - 1753626501435-1066972225.jpg
[22:33:37]   - 1753626518152-660672411.jpg
[22:33:37]   - 1753626518186-2125388435.jpg
[22:33:37]   - 1753626518219-1804250823.jpg
[22:33:37]   - 1753626518249--1065563707.jpg
[22:33:37]   - 1753626518295-152127411.jpg
[22:33:37]   - 1753626518357-1552964844.jpg
[22:33:37]   - 1753626518406-1752797653.jpg
[22:33:37]   - 1753626518441--1753262296.jpg
[22:33:37]   - 1753626518489-842254062.jpg
[22:33:37]   - 1753626518530--255483989.jpg
[22:33:37]   - 1753626518571--1285728568.jpg
[22:33:37]   - 1753626518627-396311468.jpg
[22:33:37]   - 1753626518662-1252897153.jpg
[22:33:37]   - 1753626518696--1537897804.jpg
[22:33:37]   - 1753626518746--1146868097.jpg
[22:33:37]   - 1753626518780--1712757656.jpg
[22:33:37]   - 1753626518827-184866696.jpg
[22:33:37]   - 1753626584506--174181552.jpg
[22:33:37]   - 1753626584568-649400088.jpg
[22:33:37]   - 1753626584616--75726748.jpg
[22:33:37]   - 1753626584693--1636531876.jpg
[22:33:37]   - 1753626584727-1492996970.jpg
[22:33:37]   - 1753626584762-2021427306.jpg
[22:33:37]   - 1753626603375-1223281507.jpg
[22:33:37]   - 1753626603420--59820335.jpg
[22:33:37]   - 1753626603446--1882576375.jpg
[22:33:37]   - 1753626603487--2039576347.jpg
[22:33:37]   - 1753626603536--320016844.jpg
[22:33:37]   - 1753626603577--1854090630.jpg
[22:33:37]   - 1753626660614--1428446268.jpg
[22:33:37]   - 1753626660666-1209359625.jpg
[22:33:37]   - 1753626660709-1902243945.jpg
[22:33:37]   - 1753626660740-333858737.jpg
[22:33:37]   - 1753626660786-1052787518.jpg
[22:33:37]   - 1753626660849-877359325.jpg
[22:33:37]   - 1753626660881-1230956995.jpg
[22:33:37]   - 1753626680297--251064384.jpg
[22:33:37]   - 1753626680360-1069102764.jpg
[22:33:37]   - 1753626680401--482777265.jpg
[22:33:37]   - 1753626680449--1838772425.jpg
[22:33:37]   - 1753626680498--618659008.jpg
[22:33:37]   - 1753626680546--1085139102.jpg
[22:33:37]   - 1753626680588-1076043545.jpg
[22:33:37]   - 1753626680636-552573400.jpg
[22:33:37]   - 1753626680677--1331103116.jpg
[22:33:37]   - 1753626740875--1213573602.jpg
[22:33:37]   - 1753626740918--268036214.jpg
[22:33:37]   - 1753626740959--1636983126.jpg
[22:33:37]   - 1753626741004--597443972.jpg
[22:33:37]   - 1753626741049--805566952.jpg
[22:33:37]   - 1753626741112-715278496.jpg
[22:33:37]   - 1753626741153--106487342.jpg
[22:33:37]   - 1753626741194-1887506479.jpg
[22:33:37]   - 1753626741230-639229492.jpg
[22:33:37]   - 1753626741263-1180235664.jpg
[22:33:37]   - 1753626741298-1944811228.jpg
[22:33:37]   - 1753626741339--1475076813.jpg
[22:33:37]   - 1753626741374-913913680.jpg
[22:33:37]   - 1753626741409-1189978779.jpg
[22:33:37]   - 1753626762896--238727143.jpg
[22:33:37]   - 1753626762954--655740314.jpg
[22:33:37]   - 1753626762988-953387863.jpg
[22:33:37]   - 1753626763036--744338388.jpg
[22:33:37]   - 1753626763085-730639924.jpg
[22:33:37]   - 1753626763126--112046204.jpg
[22:33:37]   - 1753626763160--1229046725.jpg
[22:33:37]   - 1753626763195-203300966.jpg
[22:33:37]   - 1753626804293-1123631752.jpg
[22:33:37]   - 1753626804337--1088434480.jpg
[22:33:37]   - 1753626804364--106025177.jpg
[22:33:37]   - 1753626804413--1301755960.jpg
[22:33:37]   - 1753626804462-1854031190.jpg
[22:33:37]   - 1753626804516--580283861.jpg
[22:33:37]   - 1753626804551--876380769.jpg
[22:33:37] ⚠️  注意：由于MTP协议限制，无法自动删除手机上的原文件
[22:33:37] 请手动删除手机 DCIM/Pindd/goods 文件夹中的图片以释放空间
[22:33:37] ✅ 图片复制完成
[22:33:37] 步骤11: 删除手机上的原文件...
[22:33:37] 开始删除手机文件操作...
[22:33:37] 查找并点击GOODS.png...
[22:33:37] 正在查找图片: GOODS.png (路径: image\IQOO\GOODS.png)
[22:33:37] 查找或点击图片 GOODS.png 时出错: 
[22:33:37] ❌ 未找到GOODS.png图片
[22:33:37] ⚠️ 手机文件删除失败，请手动删除
[22:33:37] 步骤12: 开始处理主图...
[22:33:37] 步骤13: 开始第二轮操作（详情页）...
[22:33:37] 处理主图文件夹: 商品图片\5
[22:33:37] === 开始详情页图片捕获流程 ===
[22:33:37] 步骤13.1: 移动到(475,230)并点击右键...
[22:33:37] 找到 87 张图片
[22:33:37] 删除多余图片: 1753626501401--1182398115.jpg
[22:33:37] 删除多余图片: 1753626501042--850234244.jpg
[22:33:37] 删除多余图片: 1753626500939-199319035.jpg
[22:33:37] 删除多余图片: 1753626501073--278957689.jpg
[22:33:37] 删除多余图片: 1753626501332-1706905320.jpg
[22:33:37] 删除多余图片: 1753626501176--1680485554.jpg
[22:33:37] 删除多余图片: 1753626501138--1193025293.jpg
[22:33:37] 删除多余图片: 1753626501207--778938302.jpg
[22:33:37] 删除多余图片: 1753626501435-1066972225.jpg
[22:33:37] 删除多余图片: 1753626518152-660672411.jpg
[22:33:37] 删除多余图片: 1753626518186-2125388435.jpg
[22:33:37] 删除多余图片: 1753626518219-1804250823.jpg
[22:33:37] 删除多余图片: 1753626518249--1065563707.jpg
[22:33:37] 删除多余图片: 1753626518295-152127411.jpg
[22:33:37] 删除多余图片: 1753626518357-1552964844.jpg
[22:33:37] 删除多余图片: 1753626518406-1752797653.jpg
[22:33:37] 删除多余图片: 1753626518441--1753262296.jpg
[22:33:37] 删除多余图片: 1753626518489-842254062.jpg
[22:33:37] 删除多余图片: 1753626518530--255483989.jpg
[22:33:37] 删除多余图片: 1753626518571--1285728568.jpg
[22:33:37] 删除多余图片: 1753626518627-396311468.jpg
[22:33:37] 删除多余图片: 1753626518662-1252897153.jpg
[22:33:37] 删除多余图片: 1753626518696--1537897804.jpg
[22:33:37] 删除多余图片: 1753626518746--1146868097.jpg
[22:33:37] 删除多余图片: 1753626518780--1712757656.jpg
[22:33:37] 删除多余图片: 1753626518827-184866696.jpg
[22:33:37] 删除多余图片: 1753626584506--174181552.jpg
[22:33:37] 删除多余图片: 1753626584568-649400088.jpg
[22:33:37] 删除多余图片: 1753626584616--75726748.jpg
[22:33:37] 删除多余图片: 1753626584693--1636531876.jpg
[22:33:37] 删除多余图片: 1753626584727-1492996970.jpg
[22:33:37] 删除多余图片: 1753626584762-2021427306.jpg
[22:33:37] 删除多余图片: 1753626603375-1223281507.jpg
[22:33:37] 删除多余图片: 1753626603420--59820335.jpg
[22:33:37] 删除多余图片: 1753626603446--1882576375.jpg
[22:33:37] 删除多余图片: 1753626603487--2039576347.jpg
[22:33:37] 删除多余图片: 1753626603536--320016844.jpg
[22:33:37] 删除多余图片: 1753626603577--1854090630.jpg
[22:33:37] 删除多余图片: 1753626660614--1428446268.jpg
[22:33:37] 删除多余图片: 1753626660666-1209359625.jpg
[22:33:37] 删除多余图片: 1753626660709-1902243945.jpg
[22:33:37] 删除多余图片: 1753626660740-333858737.jpg
[22:33:37] 删除多余图片: 1753626660786-1052787518.jpg
[22:33:37] 删除多余图片: 1753626660849-877359325.jpg
[22:33:37] 删除多余图片: 1753626660881-1230956995.jpg
[22:33:37] 删除多余图片: 1753626680297--251064384.jpg
[22:33:37] 删除多余图片: 1753626680360-1069102764.jpg
[22:33:37] 删除多余图片: 1753626680401--482777265.jpg
[22:33:37] 删除多余图片: 1753626680449--1838772425.jpg
[22:33:37] 删除多余图片: 1753626680498--618659008.jpg
[22:33:37] 删除多余图片: 1753626680546--1085139102.jpg
[22:33:37] 删除多余图片: 1753626680588-1076043545.jpg
[22:33:37] 删除多余图片: 1753626680636-552573400.jpg
[22:33:37] 删除多余图片: 1753626680677--1331103116.jpg
[22:33:37] 删除多余图片: 1753626740875--1213573602.jpg
[22:33:37] 删除多余图片: 1753626740918--268036214.jpg
[22:33:37] 删除多余图片: 1753626740959--1636983126.jpg
[22:33:37] 删除多余图片: 1753626741004--597443972.jpg
[22:33:37] 删除多余图片: 1753626741049--805566952.jpg
[22:33:37] 删除多余图片: 1753626741112-715278496.jpg
[22:33:37] 删除多余图片: 1753626741153--106487342.jpg
[22:33:37] 删除多余图片: 1753626741194-1887506479.jpg
[22:33:37] 删除多余图片: 1753626741230-639229492.jpg
[22:33:37] 删除多余图片: 1753626741263-1180235664.jpg
[22:33:37] 删除多余图片: 1753626741298-1944811228.jpg
[22:33:37] 删除多余图片: 1753626741339--1475076813.jpg
[22:33:37] 删除多余图片: 1753626741374-913913680.jpg
[22:33:37] 删除多余图片: 1753626741409-1189978779.jpg
[22:33:37] 删除多余图片: 1753626762896--238727143.jpg
[22:33:37] 删除多余图片: 1753626762954--655740314.jpg
[22:33:37] 删除多余图片: 1753626762988-953387863.jpg
[22:33:37] 删除多余图片: 1753626763036--744338388.jpg
[22:33:37] 删除多余图片: 1753626763085-730639924.jpg
[22:33:37] 删除多余图片: 1753626763126--112046204.jpg
[22:33:37] 删除多余图片: 1753626763160--1229046725.jpg
[22:33:37] 删除多余图片: 1753626763195-203300966.jpg
[22:33:37] 删除多余图片: 1753626804293-1123631752.jpg
[22:33:37] 删除多余图片: 1753626804337--1088434480.jpg
[22:33:37] 删除多余图片: 1753626804364--106025177.jpg
[22:33:37] 删除多余图片: 1753626804413--1301755960.jpg
[22:33:37] 删除多余图片: 1753626804462-1854031190.jpg
[22:33:37] 删除多余图片: 1753626804516--580283861.jpg
[22:33:37] 删除多余图片: 1753626804551--876380769.jpg
[22:33:37] 重命名: 1753626501373-1315797334.jpg → 主图1.jpg
[22:33:37] 重命名: 1753626501484-1645306700.jpg → 主图2.jpg
[22:33:37] 重命名: 1753626501256--945688330.jpg → 主图3.jpg
[22:33:37] 重命名: 1753626501297-483977703.jpg → 主图4.jpg
[22:33:37] ✅ 主图处理完成
[22:33:39] 步骤13.2: 使用AutoHotkey向下滚动40次...
[22:33:39] 执行AutoHotkey脚本: scroll_down.ahk
[22:33:43] ✅ AutoHotkey脚本执行成功: scroll_down
[22:33:43] ✅ AutoHotkey滚动执行成功
[22:33:43] 步骤13.3: 移动到(477,300)并点击左键...
[22:33:44] ✅ 点击操作完成
[22:33:44] 步骤13.4: 移动到(480,495)并长按0.5秒...
[22:33:45] ✅ 长按操作完成
[22:33:46] 步骤13.5: 查找并点击保存按钮...
[22:33:46] 尝试查找图片 BAOCUN1.png (第1次/共3次)
[22:33:46] 正在查找图片: BAOCUN1.png (路径: image\IQOO\BAOCUN1.png)
[22:33:47] 找到图片 BAOCUN1.png，位置: (455, 808)
[22:33:47] 移动到图片上，等待0.5秒...
[22:33:47] 已点击图片: BAOCUN1.png
[22:33:47] 步骤13.6: 检测详情页保存状态...
[22:33:47] 开始检测图片: BAOCUNZHONG.png (路径: image\IQOO\BAOCUNZHONG.png)
[22:33:47] 最多检测 80 次，每隔 0.2 秒检测一次
[22:33:47] 第 1/80 次检测...
[22:33:48] 第 2/80 次检测...
[22:33:48] ✅ 找到图片 BAOCUNZHONG.png！位置: (443, 507)
[22:33:48] ✅ 检测到详情页保存中状态，等待保存完成...
[22:33:53] 步骤13.7: 复制详情页图片...
[22:33:53] 第 1/3 次尝试复制详情页图片...
[22:33:53] 开始从MTP设备复制图片到商品 5 文件夹...
[22:33:53] 正在使用Windows Shell API查找MTP设备...
[22:33:53] 找到设备: iQOO Z1
[22:33:53] 进入文件夹: 内部存储设备
[22:33:53] 进入文件夹: DCIM
[22:33:53] 进入文件夹: Pindd
[22:33:53] 进入文件夹: goods
[22:33:53] 成功找到目标文件夹: 内部存储设备\DCIM\Pindd\goods
[22:33:53] 目标路径: F:\自动捕获数据\商品图片\5
[22:33:53] 复制文件: 1753626501373-1315797334.jpg
[22:33:53] ✅ 成功复制: 1753626501373-1315797334.jpg
[22:33:53] 复制文件: 1753626501484-1645306700.jpg
[22:33:53] ✅ 成功复制: 1753626501484-1645306700.jpg
[22:33:53] 复制文件: 1753626501256--945688330.jpg
[22:33:53] ✅ 成功复制: 1753626501256--945688330.jpg
[22:33:53] 复制文件: 1753626501297-483977703.jpg
[22:33:53] ✅ 成功复制: 1753626501297-483977703.jpg
[22:33:53] 复制文件: 1753626501401--1182398115.jpg
[22:33:53] ✅ 成功复制: 1753626501401--1182398115.jpg
[22:33:53] 复制文件: 1753626501042--850234244.jpg
[22:33:53] ✅ 成功复制: 1753626501042--850234244.jpg
[22:33:53] 复制文件: 1753626500939-199319035.jpg
[22:33:54] ✅ 成功复制: 1753626500939-199319035.jpg
[22:33:54] 复制文件: 1753626501073--278957689.jpg
[22:33:54] ✅ 成功复制: 1753626501073--278957689.jpg
[22:33:54] 复制文件: 1753626501332-1706905320.jpg
[22:33:54] ✅ 成功复制: 1753626501332-1706905320.jpg
[22:33:54] 复制文件: 1753626501176--1680485554.jpg
[22:33:54] ✅ 成功复制: 1753626501176--1680485554.jpg
[22:33:54] 复制文件: 1753626501138--1193025293.jpg
[22:33:54] ✅ 成功复制: 1753626501138--1193025293.jpg
[22:33:54] 复制文件: 1753626501207--778938302.jpg
[22:33:54] ✅ 成功复制: 1753626501207--778938302.jpg
[22:33:54] 复制文件: 1753626501435-1066972225.jpg
[22:33:54] ✅ 成功复制: 1753626501435-1066972225.jpg
[22:33:54] 复制文件: 1753626518152-660672411.jpg
[22:33:54] ✅ 成功复制: 1753626518152-660672411.jpg
[22:33:54] 复制文件: 1753626518186-2125388435.jpg
[22:33:54] ✅ 成功复制: 1753626518186-2125388435.jpg
[22:33:54] 复制文件: 1753626518219-1804250823.jpg
[22:33:54] ✅ 成功复制: 1753626518219-1804250823.jpg
[22:33:54] 复制文件: 1753626518249--1065563707.jpg
[22:33:54] ✅ 成功复制: 1753626518249--1065563707.jpg
[22:33:54] 复制文件: 1753626518295-152127411.jpg
[22:33:54] ✅ 成功复制: 1753626518295-152127411.jpg
[22:33:54] 复制文件: 1753626518357-1552964844.jpg
[22:33:54] ✅ 成功复制: 1753626518357-1552964844.jpg
[22:33:54] 复制文件: 1753626518406-1752797653.jpg
[22:33:54] ✅ 成功复制: 1753626518406-1752797653.jpg
[22:33:54] 复制文件: 1753626518441--1753262296.jpg
[22:33:55] ✅ 成功复制: 1753626518441--1753262296.jpg
[22:33:55] 复制文件: 1753626518489-842254062.jpg
[22:33:55] ✅ 成功复制: 1753626518489-842254062.jpg
[22:33:55] 复制文件: 1753626518530--255483989.jpg
[22:33:55] ✅ 成功复制: 1753626518530--255483989.jpg
[22:33:55] 复制文件: 1753626518571--1285728568.jpg
[22:33:55] ✅ 成功复制: 1753626518571--1285728568.jpg
[22:33:55] 复制文件: 1753626518627-396311468.jpg
[22:33:55] ✅ 成功复制: 1753626518627-396311468.jpg
[22:33:55] 复制文件: 1753626518662-1252897153.jpg
[22:33:55] ✅ 成功复制: 1753626518662-1252897153.jpg
[22:33:55] 复制文件: 1753626518696--1537897804.jpg
[22:33:55] ✅ 成功复制: 1753626518696--1537897804.jpg
[22:33:55] 复制文件: 1753626518746--1146868097.jpg
[22:33:55] ✅ 成功复制: 1753626518746--1146868097.jpg
[22:33:55] 复制文件: 1753626518780--1712757656.jpg
[22:33:55] ✅ 成功复制: 1753626518780--1712757656.jpg
[22:33:55] 复制文件: 1753626518827-184866696.jpg
[22:33:55] ✅ 成功复制: 1753626518827-184866696.jpg
[22:33:55] 复制文件: 1753626584506--174181552.jpg
[22:33:55] ✅ 成功复制: 1753626584506--174181552.jpg
[22:33:55] 复制文件: 1753626584568-649400088.jpg
[22:33:55] ✅ 成功复制: 1753626584568-649400088.jpg
[22:33:55] 复制文件: 1753626584616--75726748.jpg
[22:33:55] ✅ 成功复制: 1753626584616--75726748.jpg
[22:33:55] 复制文件: 1753626584693--1636531876.jpg
[22:33:55] ✅ 成功复制: 1753626584693--1636531876.jpg
[22:33:55] 复制文件: 1753626584727-1492996970.jpg
[22:33:56] ✅ 成功复制: 1753626584727-1492996970.jpg
[22:33:56] 复制文件: 1753626584762-2021427306.jpg
[22:33:56] ✅ 成功复制: 1753626584762-2021427306.jpg
[22:33:56] 复制文件: 1753626603375-1223281507.jpg
[22:33:56] ✅ 成功复制: 1753626603375-1223281507.jpg
[22:33:56] 复制文件: 1753626603420--59820335.jpg
[22:33:56] ✅ 成功复制: 1753626603420--59820335.jpg
[22:33:56] 复制文件: 1753626603446--1882576375.jpg
[22:33:56] ✅ 成功复制: 1753626603446--1882576375.jpg
[22:33:56] 复制文件: 1753626603487--2039576347.jpg
[22:33:56] ✅ 成功复制: 1753626603487--2039576347.jpg
[22:33:56] 复制文件: 1753626603536--320016844.jpg
[22:33:56] ✅ 成功复制: 1753626603536--320016844.jpg
[22:33:56] 复制文件: 1753626603577--1854090630.jpg
[22:33:56] ✅ 成功复制: 1753626603577--1854090630.jpg
[22:33:56] 复制文件: 1753626660614--1428446268.jpg
[22:33:56] ✅ 成功复制: 1753626660614--1428446268.jpg
[22:33:56] 复制文件: 1753626660666-1209359625.jpg
[22:33:56] ✅ 成功复制: 1753626660666-1209359625.jpg
[22:33:56] 复制文件: 1753626660709-1902243945.jpg
[22:33:56] ✅ 成功复制: 1753626660709-1902243945.jpg
[22:33:56] 复制文件: 1753626660740-333858737.jpg
[22:33:56] ✅ 成功复制: 1753626660740-333858737.jpg
[22:33:56] 复制文件: 1753626660786-1052787518.jpg
[22:33:56] ✅ 成功复制: 1753626660786-1052787518.jpg
[22:33:56] 复制文件: 1753626660849-877359325.jpg
[22:33:57] ✅ 成功复制: 1753626660849-877359325.jpg
[22:33:57] 复制文件: 1753626660881-1230956995.jpg
[22:33:57] ✅ 成功复制: 1753626660881-1230956995.jpg
[22:33:57] 复制文件: 1753626680297--251064384.jpg
[22:33:57] ✅ 成功复制: 1753626680297--251064384.jpg
[22:33:57] 复制文件: 1753626680360-1069102764.jpg
[22:33:57] ✅ 成功复制: 1753626680360-1069102764.jpg
[22:33:57] 复制文件: 1753626680401--482777265.jpg
[22:33:57] ✅ 成功复制: 1753626680401--482777265.jpg
[22:33:57] 复制文件: 1753626680449--1838772425.jpg
[22:33:57] ✅ 成功复制: 1753626680449--1838772425.jpg
[22:33:57] 复制文件: 1753626680498--618659008.jpg
[22:33:57] ✅ 成功复制: 1753626680498--618659008.jpg
[22:33:57] 复制文件: 1753626680546--1085139102.jpg
[22:33:57] ✅ 成功复制: 1753626680546--1085139102.jpg
[22:33:57] 复制文件: 1753626680588-1076043545.jpg
[22:33:57] ✅ 成功复制: 1753626680588-1076043545.jpg
[22:33:57] 复制文件: 1753626680636-552573400.jpg
[22:33:57] ✅ 成功复制: 1753626680636-552573400.jpg
[22:33:57] 复制文件: 1753626680677--1331103116.jpg
[22:33:57] ✅ 成功复制: 1753626680677--1331103116.jpg
[22:33:57] 复制文件: 1753626740875--1213573602.jpg
[22:33:57] ✅ 成功复制: 1753626740875--1213573602.jpg
[22:33:57] 复制文件: 1753626740918--268036214.jpg
[22:33:58] ✅ 成功复制: 1753626740918--268036214.jpg
[22:33:58] 复制文件: 1753626740959--1636983126.jpg
[22:33:58] ✅ 成功复制: 1753626740959--1636983126.jpg
[22:33:58] 复制文件: 1753626741004--597443972.jpg
[22:33:58] ✅ 成功复制: 1753626741004--597443972.jpg
[22:33:58] 复制文件: 1753626741049--805566952.jpg
[22:33:58] ✅ 成功复制: 1753626741049--805566952.jpg
[22:33:58] 复制文件: 1753626741112-715278496.jpg
[22:33:58] ✅ 成功复制: 1753626741112-715278496.jpg
[22:33:58] 复制文件: 1753626741153--106487342.jpg
[22:33:58] ✅ 成功复制: 1753626741153--106487342.jpg
[22:33:58] 复制文件: 1753626741194-1887506479.jpg
[22:33:58] ✅ 成功复制: 1753626741194-1887506479.jpg
[22:33:58] 复制文件: 1753626741230-639229492.jpg
[22:33:58] ✅ 成功复制: 1753626741230-639229492.jpg
[22:33:58] 复制文件: 1753626741263-1180235664.jpg
[22:33:58] ✅ 成功复制: 1753626741263-1180235664.jpg
[22:33:58] 复制文件: 1753626741298-1944811228.jpg
[22:33:58] ✅ 成功复制: 1753626741298-1944811228.jpg
[22:33:58] 复制文件: 1753626741339--1475076813.jpg
[22:33:58] ✅ 成功复制: 1753626741339--1475076813.jpg
[22:33:58] 复制文件: 1753626741374-913913680.jpg
[22:33:58] ✅ 成功复制: 1753626741374-913913680.jpg
[22:33:58] 复制文件: 1753626741409-1189978779.jpg
[22:33:59] ✅ 成功复制: 1753626741409-1189978779.jpg
[22:33:59] 复制文件: 1753626762896--238727143.jpg
[22:33:59] ✅ 成功复制: 1753626762896--238727143.jpg
[22:33:59] 复制文件: 1753626762954--655740314.jpg
[22:33:59] ✅ 成功复制: 1753626762954--655740314.jpg
[22:33:59] 复制文件: 1753626762988-953387863.jpg
[22:33:59] ✅ 成功复制: 1753626762988-953387863.jpg
[22:33:59] 复制文件: 1753626763036--744338388.jpg
[22:33:59] ✅ 成功复制: 1753626763036--744338388.jpg
[22:33:59] 复制文件: 1753626763085-730639924.jpg
[22:33:59] ✅ 成功复制: 1753626763085-730639924.jpg
[22:33:59] 复制文件: 1753626763126--112046204.jpg
[22:33:59] ✅ 成功复制: 1753626763126--112046204.jpg
[22:33:59] 复制文件: 1753626763160--1229046725.jpg
[22:33:59] ✅ 成功复制: 1753626763160--1229046725.jpg
[22:33:59] 复制文件: 1753626763195-203300966.jpg
[22:33:59] ✅ 成功复制: 1753626763195-203300966.jpg
[22:33:59] 复制文件: 1753626804293-1123631752.jpg
[22:33:59] ✅ 成功复制: 1753626804293-1123631752.jpg
[22:33:59] 复制文件: 1753626804337--1088434480.jpg
[22:33:59] ✅ 成功复制: 1753626804337--1088434480.jpg
[22:33:59] 复制文件: 1753626804364--106025177.jpg
[22:34:00] ✅ 成功复制: 1753626804364--106025177.jpg
[22:34:00] 复制文件: 1753626804413--1301755960.jpg
[22:34:00] ✅ 成功复制: 1753626804413--1301755960.jpg
[22:34:00] 复制文件: 1753626804462-1854031190.jpg
[22:34:00] ✅ 成功复制: 1753626804462-1854031190.jpg
[22:34:00] 复制文件: 1753626804516--580283861.jpg
[22:34:00] ✅ 成功复制: 1753626804516--580283861.jpg
[22:34:00] 复制文件: 1753626804551--876380769.jpg
[22:34:00] ✅ 成功复制: 1753626804551--876380769.jpg
[22:34:00] 复制文件: 1753626827477-1940764765.jpg
[22:34:00] ✅ 成功复制: 1753626827477-1940764765.jpg
[22:34:00] 复制文件: 1753626827533--1943928095.jpg
[22:34:00] ✅ 成功复制: 1753626827533--1943928095.jpg
[22:34:00] 复制文件: 1753626827568--916045389.jpg
[22:34:00] ✅ 成功复制: 1753626827568--916045389.jpg
[22:34:00] 复制文件: 1753626827609-1921398309.jpg
[22:34:00] ✅ 成功复制: 1753626827609-1921398309.jpg
[22:34:00] 复制文件: 1753626827657-243413148.jpg
[22:34:01] ✅ 成功复制: 1753626827657-243413148.jpg
[22:34:01] 成功复制 92 个文件
[22:34:01] ✅ 成功复制 92 个图片到商品 5 文件夹
[22:34:01] 复制的文件:
[22:34:01]   - 1753626501373-1315797334.jpg
[22:34:01]   - 1753626501484-1645306700.jpg
[22:34:01]   - 1753626501256--945688330.jpg
[22:34:01]   - 1753626501297-483977703.jpg
[22:34:01]   - 1753626501401--1182398115.jpg
[22:34:01]   - 1753626501042--850234244.jpg
[22:34:01]   - 1753626500939-199319035.jpg
[22:34:01]   - 1753626501073--278957689.jpg
[22:34:01]   - 1753626501332-1706905320.jpg
[22:34:01]   - 1753626501176--1680485554.jpg
[22:34:01]   - 1753626501138--1193025293.jpg
[22:34:01]   - 1753626501207--778938302.jpg
[22:34:01]   - 1753626501435-1066972225.jpg
[22:34:01]   - 1753626518152-660672411.jpg
[22:34:01]   - 1753626518186-2125388435.jpg
[22:34:01]   - 1753626518219-1804250823.jpg
[22:34:01]   - 1753626518249--1065563707.jpg
[22:34:01]   - 1753626518295-152127411.jpg
[22:34:01]   - 1753626518357-1552964844.jpg
[22:34:01]   - 1753626518406-1752797653.jpg
[22:34:01]   - 1753626518441--1753262296.jpg
[22:34:01]   - 1753626518489-842254062.jpg
[22:34:01]   - 1753626518530--255483989.jpg
[22:34:01]   - 1753626518571--1285728568.jpg
[22:34:01]   - 1753626518627-396311468.jpg
[22:34:01]   - 1753626518662-1252897153.jpg
[22:34:01]   - 1753626518696--1537897804.jpg
[22:34:01]   - 1753626518746--1146868097.jpg
[22:34:01]   - 1753626518780--1712757656.jpg
[22:34:01]   - 1753626518827-184866696.jpg
[22:34:01]   - 1753626584506--174181552.jpg
[22:34:01]   - 1753626584568-649400088.jpg
[22:34:01]   - 1753626584616--75726748.jpg
[22:34:01]   - 1753626584693--1636531876.jpg
[22:34:01]   - 1753626584727-1492996970.jpg
[22:34:01]   - 1753626584762-2021427306.jpg
[22:34:01]   - 1753626603375-1223281507.jpg
[22:34:01]   - 1753626603420--59820335.jpg
[22:34:01]   - 1753626603446--1882576375.jpg
[22:34:01]   - 1753626603487--2039576347.jpg
[22:34:01]   - 1753626603536--320016844.jpg
[22:34:01]   - 1753626603577--1854090630.jpg
[22:34:01]   - 1753626660614--1428446268.jpg
[22:34:01]   - 1753626660666-1209359625.jpg
[22:34:01]   - 1753626660709-1902243945.jpg
[22:34:01]   - 1753626660740-333858737.jpg
[22:34:01]   - 1753626660786-1052787518.jpg
[22:34:01]   - 1753626660849-877359325.jpg
[22:34:01]   - 1753626660881-1230956995.jpg
[22:34:01]   - 1753626680297--251064384.jpg
[22:34:01]   - 1753626680360-1069102764.jpg
[22:34:01]   - 1753626680401--482777265.jpg
[22:34:01]   - 1753626680449--1838772425.jpg
[22:34:01]   - 1753626680498--618659008.jpg
[22:34:01]   - 1753626680546--1085139102.jpg
[22:34:01]   - 1753626680588-1076043545.jpg
[22:34:01]   - 1753626680636-552573400.jpg
[22:34:01]   - 1753626680677--1331103116.jpg
[22:34:01]   - 1753626740875--1213573602.jpg
[22:34:01]   - 1753626740918--268036214.jpg
[22:34:01]   - 1753626740959--1636983126.jpg
[22:34:01]   - 1753626741004--597443972.jpg
[22:34:01]   - 1753626741049--805566952.jpg
[22:34:01]   - 1753626741112-715278496.jpg
[22:34:01]   - 1753626741153--106487342.jpg
[22:34:01]   - 1753626741194-1887506479.jpg
[22:34:01]   - 1753626741230-639229492.jpg
[22:34:01]   - 1753626741263-1180235664.jpg
[22:34:01]   - 1753626741298-1944811228.jpg
[22:34:01]   - 1753626741339--1475076813.jpg
[22:34:01]   - 1753626741374-913913680.jpg
[22:34:01]   - 1753626741409-1189978779.jpg
[22:34:01]   - 1753626762896--238727143.jpg
[22:34:01]   - 1753626762954--655740314.jpg
[22:34:01]   - 1753626762988-953387863.jpg
[22:34:01]   - 1753626763036--744338388.jpg
[22:34:01]   - 1753626763085-730639924.jpg
[22:34:01]   - 1753626763126--112046204.jpg
[22:34:01]   - 1753626763160--1229046725.jpg
[22:34:01]   - 1753626763195-203300966.jpg
[22:34:01]   - 1753626804293-1123631752.jpg
[22:34:01]   - 1753626804337--1088434480.jpg
[22:34:01]   - 1753626804364--106025177.jpg
[22:34:01]   - 1753626804413--1301755960.jpg
[22:34:01]   - 1753626804462-1854031190.jpg
[22:34:01]   - 1753626804516--580283861.jpg
[22:34:01]   - 1753626804551--876380769.jpg
[22:34:01]   - 1753626827477-1940764765.jpg
[22:34:01]   - 1753626827533--1943928095.jpg
[22:34:01]   - 1753626827568--916045389.jpg
[22:34:01]   - 1753626827609-1921398309.jpg
[22:34:01]   - 1753626827657-243413148.jpg
[22:34:01] ⚠️  注意：由于MTP协议限制，无法自动删除手机上的原文件
[22:34:01] 请手动删除手机 DCIM/Pindd/goods 文件夹中的图片以释放空间
[22:34:01] ✅ 详情页图片复制完成
[22:34:01] 步骤13.8: 删除手机上的详情页图片...
[22:34:01] 开始删除手机文件操作...
[22:34:01] 查找并点击GOODS.png...
[22:34:01] 正在查找图片: GOODS.png (路径: image\IQOO\GOODS.png)
[22:34:01] 查找或点击图片 GOODS.png 时出错: 
[22:34:01] ❌ 未找到GOODS.png图片
[22:34:01] ⚠️ 手机详情页文件删除失败，请手动删除
[22:34:01] 步骤13.8: 处理详情页图片...
[22:34:01] 处理详情页图片文件夹: 商品图片\5
[22:34:01] 跳过已处理的主图: 主图1.jpg
[22:34:01] 跳过已处理的主图: 主图2.jpg
[22:34:01] 跳过已处理的主图: 主图3.jpg
[22:34:01] 跳过已处理的主图: 主图4.jpg
[22:34:01] 找到 92 张详情页图片
[22:34:01] 删除多余详情页图片: 1753626584506--174181552.jpg
[22:34:01] 删除多余详情页图片: 1753626584568-649400088.jpg
[22:34:01] 删除多余详情页图片: 1753626584616--75726748.jpg
[22:34:01] 删除多余详情页图片: 1753626584693--1636531876.jpg
[22:34:01] 删除多余详情页图片: 1753626584727-1492996970.jpg
[22:34:01] 删除多余详情页图片: 1753626584762-2021427306.jpg
[22:34:01] 删除多余详情页图片: 1753626603375-1223281507.jpg
[22:34:01] 删除多余详情页图片: 1753626603420--59820335.jpg
[22:34:01] 删除多余详情页图片: 1753626603446--1882576375.jpg
[22:34:01] 删除多余详情页图片: 1753626603487--2039576347.jpg
[22:34:01] 删除多余详情页图片: 1753626603536--320016844.jpg
[22:34:01] 删除多余详情页图片: 1753626603577--1854090630.jpg
[22:34:01] 删除多余详情页图片: 1753626660614--1428446268.jpg
[22:34:01] 删除多余详情页图片: 1753626660666-1209359625.jpg
[22:34:01] 删除多余详情页图片: 1753626660709-1902243945.jpg
[22:34:01] 删除多余详情页图片: 1753626660740-333858737.jpg
[22:34:01] 删除多余详情页图片: 1753626660786-1052787518.jpg
[22:34:01] 删除多余详情页图片: 1753626660849-877359325.jpg
[22:34:01] 删除多余详情页图片: 1753626660881-1230956995.jpg
[22:34:01] 删除多余详情页图片: 1753626680297--251064384.jpg
[22:34:01] 删除多余详情页图片: 1753626680360-1069102764.jpg
[22:34:01] 删除多余详情页图片: 1753626680401--482777265.jpg
[22:34:01] 删除多余详情页图片: 1753626680449--1838772425.jpg
[22:34:01] 删除多余详情页图片: 1753626680498--618659008.jpg
[22:34:01] 删除多余详情页图片: 1753626680546--1085139102.jpg
[22:34:01] 删除多余详情页图片: 1753626680588-1076043545.jpg
[22:34:01] 删除多余详情页图片: 1753626680636-552573400.jpg
[22:34:01] 删除多余详情页图片: 1753626680677--1331103116.jpg
[22:34:01] 删除多余详情页图片: 1753626740875--1213573602.jpg
[22:34:01] 删除多余详情页图片: 1753626740918--268036214.jpg
[22:34:01] 删除多余详情页图片: 1753626740959--1636983126.jpg
[22:34:01] 删除多余详情页图片: 1753626741004--597443972.jpg
[22:34:01] 删除多余详情页图片: 1753626741049--805566952.jpg
[22:34:01] 删除多余详情页图片: 1753626741112-715278496.jpg
[22:34:01] 删除多余详情页图片: 1753626741153--106487342.jpg
[22:34:01] 删除多余详情页图片: 1753626741194-1887506479.jpg
[22:34:01] 删除多余详情页图片: 1753626741230-639229492.jpg
[22:34:01] 删除多余详情页图片: 1753626741263-1180235664.jpg
[22:34:01] 删除多余详情页图片: 1753626741298-1944811228.jpg
[22:34:01] 删除多余详情页图片: 1753626741339--1475076813.jpg
[22:34:01] 删除多余详情页图片: 1753626741374-913913680.jpg
[22:34:01] 删除多余详情页图片: 1753626741409-1189978779.jpg
[22:34:01] 删除多余详情页图片: 1753626762896--238727143.jpg
[22:34:01] 删除多余详情页图片: 1753626762954--655740314.jpg
[22:34:01] 删除多余详情页图片: 1753626762988-953387863.jpg
[22:34:01] 删除多余详情页图片: 1753626763036--744338388.jpg
[22:34:01] 删除多余详情页图片: 1753626763085-730639924.jpg
[22:34:01] 删除多余详情页图片: 1753626763126--112046204.jpg
[22:34:01] 删除多余详情页图片: 1753626763160--1229046725.jpg
[22:34:01] 删除多余详情页图片: 1753626763195-203300966.jpg
[22:34:01] 删除多余详情页图片: 1753626804293-1123631752.jpg
[22:34:01] 删除多余详情页图片: 1753626804337--1088434480.jpg
[22:34:01] 删除多余详情页图片: 1753626804364--106025177.jpg
[22:34:01] 删除多余详情页图片: 1753626804413--1301755960.jpg
[22:34:01] 删除多余详情页图片: 1753626804462-1854031190.jpg
[22:34:01] 删除多余详情页图片: 1753626804516--580283861.jpg
[22:34:01] 删除多余详情页图片: 1753626804551--876380769.jpg
[22:34:01] 删除多余详情页图片: 1753626827477-1940764765.jpg
[22:34:01] 删除多余详情页图片: 1753626827533--1943928095.jpg
[22:34:01] 删除多余详情页图片: 1753626827568--916045389.jpg
[22:34:01] 删除多余详情页图片: 1753626827609-1921398309.jpg
[22:34:01] 删除多余详情页图片: 1753626827657-243413148.jpg
[22:34:01] 重命名详情页图片: 1753626501373-1315797334.jpg → 1.jpg
[22:34:01] 重命名详情页图片: 1753626501484-1645306700.jpg → 2.jpg
[22:34:01] 重命名详情页图片: 1753626501256--945688330.jpg → 3.jpg
[22:34:01] 重命名详情页图片: 1753626501297-483977703.jpg → 4.jpg
[22:34:01] 重命名详情页图片: 1753626501401--1182398115.jpg → 5.jpg
[22:34:01] 重命名详情页图片: 1753626501042--850234244.jpg → 6.jpg
[22:34:01] 重命名详情页图片: 1753626500939-199319035.jpg → 7.jpg
[22:34:01] 重命名详情页图片: 1753626501073--278957689.jpg → 8.jpg
[22:34:01] 重命名详情页图片: 1753626501332-1706905320.jpg → 9.jpg
[22:34:01] 重命名详情页图片: 1753626501176--1680485554.jpg → 10.jpg
[22:34:01] 重命名详情页图片: 1753626501138--1193025293.jpg → 11.jpg
[22:34:01] 重命名详情页图片: 1753626501207--778938302.jpg → 12.jpg
[22:34:01] 重命名详情页图片: 1753626501435-1066972225.jpg → 13.jpg
[22:34:01] 重命名详情页图片: 1753626518152-660672411.jpg → 14.jpg
[22:34:01] 重命名详情页图片: 1753626518186-2125388435.jpg → 15.jpg
[22:34:01] 重命名详情页图片: 1753626518219-1804250823.jpg → 16.jpg
[22:34:01] 重命名详情页图片: 1753626518249--1065563707.jpg → 17.jpg
[22:34:01] 重命名详情页图片: 1753626518295-152127411.jpg → 18.jpg
[22:34:01] 重命名详情页图片: 1753626518357-1552964844.jpg → 19.jpg
[22:34:01] 重命名详情页图片: 1753626518406-1752797653.jpg → 20.jpg
[22:34:01] 重命名详情页图片: 1753626518441--1753262296.jpg → 21.jpg
[22:34:01] 重命名详情页图片: 1753626518489-842254062.jpg → 22.jpg
[22:34:01] 重命名详情页图片: 1753626518530--255483989.jpg → 23.jpg
[22:34:01] 重命名详情页图片: 1753626518571--1285728568.jpg → 24.jpg
[22:34:01] 重命名详情页图片: 1753626518627-396311468.jpg → 25.jpg
[22:34:01] 重命名详情页图片: 1753626518662-1252897153.jpg → 26.jpg
[22:34:01] 重命名详情页图片: 1753626518696--1537897804.jpg → 27.jpg
[22:34:01] 重命名详情页图片: 1753626518746--1146868097.jpg → 28.jpg
[22:34:01] 重命名详情页图片: 1753626518780--1712757656.jpg → 29.jpg
[22:34:01] 重命名详情页图片: 1753626518827-184866696.jpg → 30.jpg
[22:34:01] ✅ 详情页图片处理完成
[22:34:01] ✅ 主图保持'主图X'格式，详情页图片重命名为1-30
[22:34:01] ✅ 详情页图片处理完成
[22:34:01] 步骤13.9: 执行最后的鼠标操作...
[22:34:01] 开始执行最后的鼠标操作序列...
[22:34:01] 移动到位置(475,125)...
[22:34:02] 点击右键...
[22:34:02] 等待1秒...
[22:34:03] 移动到位置(670,940)...
[22:34:03] 点击左键...
[22:34:04] 等待2.5秒让页面加载完成...
[22:34:06] ✅ 最后的鼠标操作序列完成
[22:34:06] 步骤13.10: 执行智能OCR分析...
[22:34:06] ==================================================
[22:34:06] 🧠 智能OCR分析开始
[22:34:06] ==================================================
[22:34:06] 🔍 开始智能OCR分析屏幕区域: (270, 230) 到 (691, 883)
[22:34:06] ✅ OCR截图已保存: 商品图片\5.jpg
[22:34:06] 正在执行OCR识别...
[22:34:08] OCR原始结果类型: <class 'list'>
[22:34:08] OCR原始结果长度: 1
[22:34:08] 第一页结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[22:34:08] OCRResult对象属性: ['_save_funcs', '_json_writer', '_rand_fn', '_img_writer']
[22:34:08]   _save_funcs: <class 'list'> - [<bound method JsonMixin.save_to_json of {'input_path': None, 'page_index': None, 'doc_preprocessor_...
[22:34:08]   _json_writer: <class 'paddlex.inference.utils.io.writers.JsonWriter'> - <paddlex.inference.utils.io.writers.JsonWriter object at 0x00000200E6E47BC0>
[22:34:08]   _rand_fn: <class 'NoneType'> - None
[22:34:08]   _img_writer: <class 'paddlex.inference.utils.io.writers.ImageWriter'> - <paddlex.inference.utils.io.writers.ImageWriter object at 0x00000200E7AF3C50>
[22:34:08] 方式2成功：识别到 19 个文本
[22:34:08] 文本 0: '￥28.1- 48.3', 置信度: 0.8872
[22:34:08] OCR识别: ￥28.1- 48.3 (置信度: 0.8872)
[22:34:08] 文本 1: '1', 置信度: 0.9973
[22:34:08] OCR识别: 1 (置信度: 0.9973)
[22:34:08] 文本 2: '请选择：颜色分类参考分类', 置信度: 0.9971
[22:34:08] OCR识别: 请选择：颜色分类参考分类 (置信度: 0.9971)
[22:34:08] 文本 3: '优惠', 置信度: 0.9997
[22:34:08] OCR识别: 优惠 (置信度: 0.9997)
[22:34:08] 文本 4: '选择款式后查看优惠', 置信度: 0.9984
[22:34:08] OCR识别: 选择款式后查看优惠 (置信度: 0.9984)
[22:34:08] 文本 5: '颜色分类', 置信度: 0.9998
[22:34:08] OCR识别: 颜色分类 (置信度: 0.9998)
[22:34:08] 文本 6: '衬衫', 置信度: 0.9999
[22:34:08] OCR识别: 衬衫 (置信度: 0.9999)
[22:34:08] 文本 7: '裤子', 置信度: 0.9999
[22:34:08] OCR识别: 裤子 (置信度: 0.9999)
[22:34:08] 文本 8: '套装', 置信度: 0.9995
[22:34:08] OCR识别: 套装 (置信度: 0.9995)
[22:34:08] 文本 9: '参考分类', 置信度: 0.9999
[22:34:08] OCR识别: 参考分类 (置信度: 0.9999)
[22:34:08] 文本 10: '90', 置信度: 0.9993
[22:34:08] OCR识别: 90 (置信度: 0.9993)
[22:34:08] 文本 11: '100', 置信度: 0.9999
[22:34:08] OCR识别: 100 (置信度: 0.9999)
[22:34:08] 文本 12: '110', 置信度: 0.9999
[22:34:08] OCR识别: 110 (置信度: 0.9999)
[22:34:08] 文本 13: '120', 置信度: 0.9999
[22:34:08] OCR识别: 120 (置信度: 0.9999)
[22:34:08] 文本 14: '130', 置信度: 0.9997
[22:34:08] OCR识别: 130 (置信度: 0.9997)
[22:34:08] 文本 15: '140', 置信度: 0.9998
[22:34:08] OCR识别: 140 (置信度: 0.9998)
[22:34:08] 文本 16: '免费服务', 置信度: 0.9999
[22:34:08] OCR识别: 免费服务 (置信度: 0.9999)
[22:34:08] 文本 17: '退货包运费(商家赠送）', 置信度: 0.9157
[22:34:08] OCR识别: 退货包运费(商家赠送） (置信度: 0.9157)
[22:34:08] 文本 18: '一次选多款，不满意可退货包运费>', 置信度: 0.9610
[22:34:08] OCR识别: 一次选多款，不满意可退货包运费> (置信度: 0.9610)
[22:34:08] ✅ OCR识别完成，共识别到 19 个文本
[22:34:08] 💾 保存OCR结果到文件...
[22:34:08] ✅ OCR结果已保存: OCR\商品5_OCR结果.txt
[22:34:08] 🧠 开始智能分析商品信息...
[22:34:08] 🔧 开始智能拼接被分割的文本...
[22:34:09] 🎯 找到上方紧贴文本:
[22:34:09]    上方文本: '免费服务' Y范围: 420.0-446.0, X范围: 2.0-73.0
[22:34:09]    当前文本: '退货包运费(商家赠送）' Y范围: 458.0-480.0, X起始: 27.0
[22:34:09]    垂直间距: 12.0px, X重叠: True
[22:34:09] ✅ 向上拼接: '免费服务' + '退货包运费(商家赠送）' = '免费服务退货包运费(商家赠送）'
[22:34:09] 🔗 完成向上拼接:
[22:34:09]    结果: '免费服务退货包运费(商家赠送）'
[22:34:09] 📊 拼接结果: 原始19个文本 → 拼接后18个文本
[22:34:09] 📝 拼接后的文本列表:
[22:34:09]    1. '￥28.1- 48.3'
[22:34:09]    2. '1'
[22:34:09]    3. '请选择：颜色分类参考分类'
[22:34:09]    4. '优惠'
[22:34:09]    5. '选择款式后查看优惠'
[22:34:09]    6. '颜色分类'
[22:34:09]    7. '衬衫'
[22:34:09]    8. '裤子'
[22:34:09]    9. '套装'
[22:34:09]    10. '参考分类'
[22:34:09]    11. '90'
[22:34:09]    12. '100'
[22:34:09]    13. '110'
[22:34:09]    14. '120'
[22:34:09]    15. '130'
[22:34:09]    16. '140'
[22:34:09]    17. '免费服务退货包运费(商家赠送）'
[22:34:09]    18. '一次选多款，不满意可退货包运费>'
[22:34:09] 🎯 找到尺码区域开始位置: 第3行 '请选择：颜色分类参考分类'
[22:34:09] 📝 尺码区域文本无数字: '优惠'
[22:34:09] 📝 尺码区域文本无数字: '选择款式后查看优惠'
[22:34:09] 📝 尺码区域文本无数字: '颜色分类'
[22:34:09] 📝 尺码区域文本无数字: '衬衫'
[22:34:09] 📝 尺码区域文本无数字: '裤子'
[22:34:09] 📝 尺码区域文本无数字: '套装'
[22:34:09] 📝 尺码区域文本无数字: '参考分类'
[22:34:09] ✅ 提取尺码: 90 (来源: '90')
[22:34:09] 🔍 处理bbox: [5, 367, 39, 398] (长度: 4)
[22:34:09] ✅ 计算坐标成功: (22, 382)
[22:34:09] 📍 记录尺码坐标: 90 -> (22, 382)
[22:34:09] ✅ 提取尺码: 100 (来源: '100')
[22:34:09] 🔍 处理bbox: [50, 368, 91, 397] (长度: 4)
[22:34:09] ✅ 计算坐标成功: (70, 382)
[22:34:09] 📍 记录尺码坐标: 100 -> (70, 382)
[22:34:09] ✅ 提取尺码: 110 (来源: '110')
[22:34:09] 🔍 处理bbox: [101, 369, 139, 395] (长度: 4)
[22:34:09] ✅ 计算坐标成功: (120, 382)
[22:34:09] 📍 记录尺码坐标: 110 -> (120, 382)
[22:34:09] ✅ 提取尺码: 120 (来源: '120')
[22:34:09] 🔍 处理bbox: [148, 368, 190, 397] (长度: 4)
[22:34:09] ✅ 计算坐标成功: (169, 382)
[22:34:09] 📍 记录尺码坐标: 120 -> (169, 382)
[22:34:09] ✅ 提取尺码: 130 (来源: '130')
[22:34:09] 🔍 处理bbox: [195, 366, 243, 399] (长度: 4)
[22:34:09] ✅ 计算坐标成功: (219, 382)
[22:34:09] 📍 记录尺码坐标: 130 -> (219, 382)
[22:34:09] ✅ 提取尺码: 140 (来源: '140')
[22:34:09] 🔍 处理bbox: [248, 366, 292, 399] (长度: 4)
[22:34:09] ✅ 计算坐标成功: (270, 382)
[22:34:09] 📍 记录尺码坐标: 140 -> (270, 382)
[22:34:09] 🛑 遇到停止关键词，结束尺码提取: '免费服务退货包运费(商家赠送）'
[22:34:09] 📊 尺码提取结果: 数字=[90, 100, 110, 120, 130, 140], 范围=90-140, 原始文本数量=6
[22:34:09] 尺码信息: {'optimized_range': '90-140', 'original_texts': ['90', '100', '110', '120', '130', '140'], 'size_numbers': [90, 100, 110, 120, 130, 140]}
[22:34:09] 📍 已记录尺码坐标: {100: (70, 382), 110: (120, 382), 120: (169, 382), 130: (219, 382), 140: (270, 382), 150: (90, 455), 90: (22, 382), 80: (37, 372)}
[22:34:09] 🔍 发现颜色分类嵌入格式: '请选择：颜色分类参考分类' → 提取: '参考分类'
[22:34:09] 🎯 找到颜色分类开始位置: 第2行 '请选择：颜色分类参考分类'
[22:34:09] 🔍 发现颜色嵌入格式: '颜色分类' → 提取: '分类'
[22:34:09] 🎯 发现嵌入颜色分类: '分类' (来源: '颜色分类')
[22:34:09] 🎯 找到颜色分类开始位置: 第5行 '颜色分类'
[22:34:09] 🎯 找到颜色分类结束位置: 第9行 '参考分类'
[22:34:09] 🔍 开始提取颜色分类: 从第6行到第8行
[22:34:09] ✅ 保留有效文本: '衬衫'
[22:34:09] ✅ 保留有效文本: '裤子'
[22:34:09] ✅ 保留有效文本: '套装'
[22:34:09] ✅ 添加嵌入颜色分类: '分类'
[22:34:09] 🔍 检查颜色文本: '衬衫' (长度: 2)
[22:34:09] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [ 52 ... 306]
[22:34:09] 🔍 价格检测结果: False
[22:34:09] 🔍 处理bbox: [52, 282, 89, 306] (长度: 4)
[22:34:09] ✅ 计算坐标成功: (70, 294)
[22:34:09] 📍 记录颜色坐标: 衬衫 -> (70, 294)
[22:34:09] ✅ 提取到无价格颜色: 衬衫
[22:34:09] 🔍 检查颜色文本: '裤子' (长度: 2)
[22:34:09] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [189 ... 306]
[22:34:09] 🔍 价格检测结果: False
[22:34:09] 🔍 处理bbox: [189, 282, 228, 306] (长度: 4)
[22:34:09] ✅ 计算坐标成功: (208, 294)
[22:34:09] 📍 记录颜色坐标: 裤子 -> (208, 294)
[22:34:09] ✅ 提取到无价格颜色: 裤子
[22:34:09] 🔍 检查颜色文本: '套装' (长度: 2)
[22:34:09] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [334 ... 306]
[22:34:09] 🔍 价格检测结果: False
[22:34:09] 🔍 处理bbox: [334, 283, 373, 306] (长度: 4)
[22:34:09] ✅ 计算坐标成功: (353, 294)
[22:34:09] 📍 记录颜色坐标: 套装 -> (353, 294)
[22:34:09] ✅ 提取到无价格颜色: 套装
[22:34:09] 🔍 检查颜色文本: '分类' (长度: 2)
[22:34:09] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [  3 ... 152]
[22:34:09] 🔍 价格检测结果: False
[22:34:09] 🔍 处理bbox: [3, 130, 73, 152] (长度: 4)
[22:34:09] ✅ 计算坐标成功: (38, 141)
[22:34:09] 📍 记录颜色坐标: 分类 -> (38, 141)
[22:34:09] ✅ 提取到无价格颜色: 分类
[22:34:09] 🎨 颜色分类提取完成: 共提取到 4 个颜色
[22:34:09] 📍 坐标记录完成: 共记录 4 个坐标
[22:34:09] 提取到颜色分类: [{'pure_name': '衬衫', 'original_text': '衬衫', 'has_direct_price': False, 'direct_price': None, 'bbox': array([ 52, ..., 306], dtype=int16)}, {'pure_name': '裤子', 'original_text': '裤子', 'has_direct_price': False, 'direct_price': None, 'bbox': array([189, ..., 306], dtype=int16)}, {'pure_name': '套装', 'original_text': '套装', 'has_direct_price': False, 'direct_price': None, 'bbox': array([334, ..., 306], dtype=int16)}, {'pure_name': '分类', 'original_text': '分类', 'has_direct_price': False, 'direct_price': None, 'bbox': array([  3, ..., 152], dtype=int16)}]
[22:34:09] 颜色分类数量: 4
[22:34:09] 📍 已记录颜色坐标: {'衬衫': (70, 294), '裤子': (208, 294), '套装': (353, 294), '分类': (38, 141)}
[22:34:09] 🎯 从页面提取价格信息
[22:34:09] ✅ 页面通用价格: 28.1 (来源: ￥28.1- 48.3)
[22:34:09] 🔧 不分配页面价格给颜色，使用交互式方案获取准确价格
[22:34:09] 🔍 商品类型分析:
[22:34:09]    颜色数量: 4
[22:34:09]    颜色直接带价格: False
[22:34:09]    页面有券前/券后价格: False
[22:34:09]    尺码带价格: False
[22:34:09] 📊 分析类型: type3_multiple_colors_no_prices
[22:34:09] 🔧 处理方案: interactive
[22:34:09] 📊 智能分析结果:
[22:34:09]   优化尺码范围: 90-140
[22:34:09]   原始尺码文本: ['90', '100', '110', '120', '130', '140']
[22:34:09]   颜色分类: ['衬衫', '裤子', '套装', '分类']
[22:34:09]   颜色价格: {}
[22:34:09]   分析类型: type3_multiple_colors_no_prices
[22:34:09]   处理方案: interactive
[22:34:09] 🔄 切换到交互式价格获取方案
[22:34:09] 🚀 开始交互式价格获取...
[22:34:09] 需要交互获取价格的颜色: ['衬衫', '裤子', '套装', '分类']
[22:34:09] 📏 尺码选择策略: 从6个尺码中选择中间值 120
[22:34:09]    完整尺码列表: [90, 100, 110, 120, 130, 140]
[22:34:09]    避免最小码: 90，避免最大码: 140
[22:34:09] 选择中间尺码: 120
[22:34:09] 🔍 检查尺码坐标记录: {100: (70, 382), 110: (120, 382), 120: (169, 382), 130: (219, 382), 140: (270, 382), 150: (90, 455), 90: (22, 382), 80: (37, 372)}
[22:34:09] 🔍 查找尺码: 120
[22:34:09] 📍 使用记录的尺码坐标: 120
[22:34:09]    相对坐标: (169, 382)
[22:34:09]    绝对坐标: (439, 612)
[22:34:09] 🎯 移动到尺码 120 坐标: (439, 612)
[22:34:10] 🎯 点击尺码 120
[22:34:12] 🎯 检测到多颜色商品，需要依次点击 4 个颜色
[22:34:12] 🎨 处理颜色 1/4: 衬衫
[22:34:12] 📍 使用记录的坐标: 衬衫
[22:34:12]    相对坐标: (70, 294)
[22:34:12]    绝对坐标: (340, 524)
[22:34:12] 🎯 移动到颜色 衬衫 坐标: (340, 524)
[22:34:13] 🎯 点击颜色 衬衫
[22:34:15] 📸 截取颜色 衬衫 更新后的页面...
[22:34:15] ✅ 价格OCR截图已保存: 商品图片\5-1.jpg
[22:34:17] 价格OCR结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[22:34:17] 字典格式价格OCR识别到 19 个文本
[22:34:17] 价格OCR识别: 包邮价￥36.5￥38.9 (置信度: 0.948)
[22:34:17] 价格OCR识别: 1 (置信度: 0.998)
[22:34:17] 价格OCR识别: 已选：衬衫120 (置信度: 0.989)
[22:34:17] 价格OCR识别: 优惠 (置信度: 1.000)
[22:34:17] 价格OCR识别: 已享9.4折·共优惠2.4元> (置信度: 0.972)
[22:34:17] 价格OCR识别: 颜色分类 (置信度: 0.997)
[22:34:17] 价格OCR识别: 衬衫 (置信度: 1.000)
[22:34:17] 价格OCR识别: 裤子 (置信度: 1.000)
[22:34:17] 价格OCR识别: 套装 (置信度: 1.000)
[22:34:17] 价格OCR识别: 参考分类 (置信度: 1.000)
[22:34:17] 价格OCR识别: 90 (置信度: 1.000)
[22:34:17] 价格OCR识别: 100 (置信度: 1.000)
[22:34:17] 价格OCR识别: 110 (置信度: 1.000)
[22:34:17] 价格OCR识别: 120 (置信度: 1.000)
[22:34:17] 价格OCR识别: 130 (置信度: 1.000)
[22:34:17] 价格OCR识别: 140 (置信度: 1.000)
[22:34:17] 价格OCR识别: 免费服务 (置信度: 1.000)
[22:34:17] 价格OCR识别: 退货包运费(商家赠送） (置信度: 0.912)
[22:34:17] 价格OCR识别: 一次选多款，不满意可退货包运费> (置信度: 0.963)
[22:34:17] ✅ 价格OCR识别完成，共识别到 19 个文本
[22:34:17] ✅ 提取到通用价格: 36.5 (来源: 包邮价￥36.5￥38.9)
[22:34:17] ✅ 获取到颜色 衬衫 的价格: 36.5
[22:34:17] 🔍 开始坐标校验，重新进行完整OCR识别...
[22:34:17] 🔍 执行坐标校验OCR识别...
[22:34:17] 🔍 开始智能OCR分析屏幕区域: (270, 230) 到 (691, 883)
[22:34:18] ✅ OCR截图已保存: 商品图片\coordinate_verify.jpg
[22:34:18] 正在执行OCR识别...
[22:34:20] OCR原始结果类型: <class 'list'>
[22:34:20] OCR原始结果长度: 1
[22:34:20] 第一页结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[22:34:20] OCRResult对象属性: ['_save_funcs', '_json_writer', '_rand_fn', '_img_writer']
[22:34:20]   _save_funcs: <class 'list'> - [<bound method JsonMixin.save_to_json of {'input_path': None, 'page_index': None, 'doc_preprocessor_...
[22:34:20]   _json_writer: <class 'paddlex.inference.utils.io.writers.JsonWriter'> - <paddlex.inference.utils.io.writers.JsonWriter object at 0x00000200E6E80740>
[22:34:20]   _rand_fn: <class 'NoneType'> - None
[22:34:20]   _img_writer: <class 'paddlex.inference.utils.io.writers.ImageWriter'> - <paddlex.inference.utils.io.writers.ImageWriter object at 0x00000200E7AF0EF0>
[22:34:20] 方式2成功：识别到 19 个文本
[22:34:20] 文本 0: '包邮价￥36.5￥38.9', 置信度: 0.9482
[22:34:20] OCR识别: 包邮价￥36.5￥38.9 (置信度: 0.9482)
[22:34:20] 文本 1: '1', 置信度: 0.9977
[22:34:20] OCR识别: 1 (置信度: 0.9977)
[22:34:20] 文本 2: '已选：衬衫120', 置信度: 0.9889
[22:34:20] OCR识别: 已选：衬衫120 (置信度: 0.9889)
[22:34:20] 文本 3: '优惠', 置信度: 0.9996
[22:34:20] OCR识别: 优惠 (置信度: 0.9996)
[22:34:20] 文本 4: '已享9.4折·共优惠2.4元>', 置信度: 0.9716
[22:34:20] OCR识别: 已享9.4折·共优惠2.4元> (置信度: 0.9716)
[22:34:20] 文本 5: '颜色分类', 置信度: 0.9969
[22:34:20] OCR识别: 颜色分类 (置信度: 0.9969)
[22:34:20] 文本 6: '衬衫', 置信度: 0.9999
[22:34:20] OCR识别: 衬衫 (置信度: 0.9999)
[22:34:20] 文本 7: '裤子', 置信度: 0.9999
[22:34:20] OCR识别: 裤子 (置信度: 0.9999)
[22:34:20] 文本 8: '套装', 置信度: 0.9996
[22:34:20] OCR识别: 套装 (置信度: 0.9996)
[22:34:20] 文本 9: '参考分类', 置信度: 0.9999
[22:34:20] OCR识别: 参考分类 (置信度: 0.9999)
[22:34:20] 文本 10: '90', 置信度: 0.9999
[22:34:20] OCR识别: 90 (置信度: 0.9999)
[22:34:20] 文本 11: '100', 置信度: 0.9999
[22:34:20] OCR识别: 100 (置信度: 0.9999)
[22:34:20] 文本 12: '110', 置信度: 0.9998
[22:34:20] OCR识别: 110 (置信度: 0.9998)
[22:34:20] 文本 13: '120', 置信度: 0.9995
[22:34:20] OCR识别: 120 (置信度: 0.9995)
[22:34:20] 文本 14: '130', 置信度: 0.9998
[22:34:20] OCR识别: 130 (置信度: 0.9998)
[22:34:20] 文本 15: '140', 置信度: 0.9998
[22:34:20] OCR识别: 140 (置信度: 0.9998)
[22:34:20] 文本 16: '免费服务', 置信度: 0.9999
[22:34:20] OCR识别: 免费服务 (置信度: 0.9999)
[22:34:20] 文本 17: '退货包运费(商家赠送）', 置信度: 0.9119
[22:34:20] OCR识别: 退货包运费(商家赠送） (置信度: 0.9119)
[22:34:20] 文本 18: '一次选多款，不满意可退货包运费>', 置信度: 0.9625
[22:34:20] OCR识别: 一次选多款，不满意可退货包运费> (置信度: 0.9625)
[22:34:20] ✅ OCR识别完成，共识别到 19 个文本
[22:34:20] 🧠 开始智能分析商品信息...
[22:34:20] 🔧 开始智能拼接被分割的文本...
[22:34:20] 🎯 找到上方紧贴文本:
[22:34:20]    上方文本: '包邮价￥36.5￥38.9' Y范围: 12.0-37.0, X范围: 4.0-160.0
[22:34:20]    当前文本: '已选：衬衫120' Y范围: 46.0-65.0, X起始: 4.0
[22:34:20]    垂直间距: 9.0px, X重叠: True
[22:34:20] ✅ 向上拼接: '包邮价￥36.5￥38.9' + '已选：衬衫120' = '包邮价￥36.5￥38.9已选：衬衫120'
[22:34:20] 🔗 完成向上拼接:
[22:34:20]    结果: '包邮价￥36.5￥38.9已选：衬衫120'
[22:34:20] 🎯 找到上方紧贴文本:
[22:34:20]    上方文本: '免费服务' Y范围: 420.0-446.0, X范围: 2.0-73.0
[22:34:20]    当前文本: '退货包运费(商家赠送）' Y范围: 458.0-480.0, X起始: 27.0
[22:34:20]    垂直间距: 12.0px, X重叠: True
[22:34:20] ✅ 向上拼接: '免费服务' + '退货包运费(商家赠送）' = '免费服务退货包运费(商家赠送）'
[22:34:20] 🔗 完成向上拼接:
[22:34:20]    结果: '免费服务退货包运费(商家赠送）'
[22:34:20] 📊 拼接结果: 原始19个文本 → 拼接后17个文本
[22:34:20] 📝 拼接后的文本列表:
[22:34:20]    1. '1'
[22:34:20]    2. '包邮价￥36.5￥38.9已选：衬衫120'
[22:34:20]    3. '优惠'
[22:34:20]    4. '已享9.4折·共优惠2.4元>'
[22:34:20]    5. '颜色分类'
[22:34:20]    6. '衬衫'
[22:34:20]    7. '裤子'
[22:34:20]    8. '套装'
[22:34:20]    9. '参考分类'
[22:34:20]    10. '90'
[22:34:20]    11. '100'
[22:34:20]    12. '110'
[22:34:20]    13. '120'
[22:34:20]    14. '130'
[22:34:20]    15. '140'
[22:34:20]    16. '免费服务退货包运费(商家赠送）'
[22:34:20]    17. '一次选多款，不满意可退货包运费>'
[22:34:20] 🎯 找到尺码区域开始位置: 第9行 '参考分类'
[22:34:20] ✅ 提取尺码: 90 (来源: '90')
[22:34:20] 🔍 处理bbox: [7, 369, 38, 396] (长度: 4)
[22:34:20] ✅ 计算坐标成功: (22, 382)
[22:34:20] 📍 记录尺码坐标: 90 -> (22, 382)
[22:34:20] ✅ 提取尺码: 100 (来源: '100')
[22:34:20] 🔍 处理bbox: [50, 368, 91, 397] (长度: 4)
[22:34:20] ✅ 计算坐标成功: (70, 382)
[22:34:20] 📍 记录尺码坐标: 100 -> (70, 382)
[22:34:20] ✅ 提取尺码: 110 (来源: '110')
[22:34:20] 🔍 处理bbox: [101, 369, 138, 395] (长度: 4)
[22:34:20] ✅ 计算坐标成功: (119, 382)
[22:34:20] 📍 记录尺码坐标: 110 -> (119, 382)
[22:34:20] ✅ 提取尺码: 120 (来源: '120')
[22:34:20] 🔍 处理bbox: [150, 368, 190, 396] (长度: 4)
[22:34:20] ✅ 计算坐标成功: (170, 382)
[22:34:20] 📍 记录尺码坐标: 120 -> (170, 382)
[22:34:20] ✅ 提取尺码: 130 (来源: '130')
[22:34:20] 🔍 处理bbox: [197, 366, 243, 399] (长度: 4)
[22:34:20] ✅ 计算坐标成功: (220, 382)
[22:34:20] 📍 记录尺码坐标: 130 -> (220, 382)
[22:34:20] ✅ 提取尺码: 140 (来源: '140')
[22:34:20] 🔍 处理bbox: [248, 366, 292, 399] (长度: 4)
[22:34:20] ✅ 计算坐标成功: (270, 382)
[22:34:20] 📍 记录尺码坐标: 140 -> (270, 382)
[22:34:20] 🛑 遇到停止关键词，结束尺码提取: '免费服务退货包运费(商家赠送）'
[22:34:20] 📊 尺码提取结果: 数字=[90, 100, 110, 120, 130, 140], 范围=90-140, 原始文本数量=6
[22:34:20] 尺码信息: {'optimized_range': '90-140', 'original_texts': ['90', '100', '110', '120', '130', '140'], 'size_numbers': [90, 100, 110, 120, 130, 140]}
[22:34:20] 📍 已记录尺码坐标: {100: (70, 382), 110: (119, 382), 120: (170, 382), 130: (220, 382), 140: (270, 382), 150: (90, 455), 90: (22, 382), 80: (37, 372)}
[22:34:20] 🔍 发现颜色嵌入格式: '颜色分类' → 提取: '分类'
[22:34:20] 🎯 发现嵌入颜色分类: '分类' (来源: '颜色分类')
[22:34:20] 🎯 找到颜色分类开始位置: 第4行 '颜色分类'
[22:34:20] 🎯 找到颜色分类结束位置: 第8行 '参考分类'
[22:34:20] 🔍 开始提取颜色分类: 从第5行到第7行
[22:34:20] ✅ 保留有效文本: '衬衫'
[22:34:20] ✅ 保留有效文本: '裤子'
[22:34:20] ✅ 保留有效文本: '套装'
[22:34:20] ✅ 添加嵌入颜色分类: '分类'
[22:34:20] 🔍 检查颜色文本: '衬衫' (长度: 2)
[22:34:20] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [ 52 ... 306]
[22:34:20] 🔍 价格检测结果: False
[22:34:20] 🔍 处理bbox: [52, 282, 89, 306] (长度: 4)
[22:34:20] ✅ 计算坐标成功: (70, 294)
[22:34:20] 📍 记录颜色坐标: 衬衫 -> (70, 294)
[22:34:20] ✅ 提取到无价格颜色: 衬衫
[22:34:20] 🔍 检查颜色文本: '裤子' (长度: 2)
[22:34:20] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [190 ... 306]
[22:34:20] 🔍 价格检测结果: False
[22:34:20] 🔍 处理bbox: [190, 282, 228, 306] (长度: 4)
[22:34:20] ✅ 计算坐标成功: (209, 294)
[22:34:20] 📍 记录颜色坐标: 裤子 -> (209, 294)
[22:34:20] ✅ 提取到无价格颜色: 裤子
[22:34:20] 🔍 检查颜色文本: '套装' (长度: 2)
[22:34:20] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [335 ... 306]
[22:34:20] 🔍 价格检测结果: False
[22:34:20] 🔍 处理bbox: [335, 283, 373, 306] (长度: 4)
[22:34:20] ✅ 计算坐标成功: (354, 294)
[22:34:20] 📍 记录颜色坐标: 套装 -> (354, 294)
[22:34:20] ✅ 提取到无价格颜色: 套装
[22:34:20] 🔍 检查颜色文本: '分类' (长度: 2)
[22:34:20] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [  3 ... 152]
[22:34:20] 🔍 价格检测结果: False
[22:34:20] 🔍 处理bbox: [3, 130, 73, 152] (长度: 4)
[22:34:20] ✅ 计算坐标成功: (38, 141)
[22:34:20] 📍 记录颜色坐标: 分类 -> (38, 141)
[22:34:20] ✅ 提取到无价格颜色: 分类
[22:34:20] 🎨 颜色分类提取完成: 共提取到 4 个颜色
[22:34:20] 📍 坐标记录完成: 共记录 4 个坐标
[22:34:20] 提取到颜色分类: [{'pure_name': '衬衫', 'original_text': '衬衫', 'has_direct_price': False, 'direct_price': None, 'bbox': array([ 52, ..., 306], dtype=int16)}, {'pure_name': '裤子', 'original_text': '裤子', 'has_direct_price': False, 'direct_price': None, 'bbox': array([190, ..., 306], dtype=int16)}, {'pure_name': '套装', 'original_text': '套装', 'has_direct_price': False, 'direct_price': None, 'bbox': array([335, ..., 306], dtype=int16)}, {'pure_name': '分类', 'original_text': '分类', 'has_direct_price': False, 'direct_price': None, 'bbox': array([  3, ..., 152], dtype=int16)}]
[22:34:20] 颜色分类数量: 4
[22:34:20] 📍 已记录颜色坐标: {'衬衫': (70, 294), '裤子': (209, 294), '套装': (354, 294), '分类': (38, 141)}
[22:34:20] 🎯 从页面提取价格信息
[22:34:20] ✅ 页面通用价格: 36.5 (来源: 包邮价￥36.5￥38.9已选：衬衫120)
[22:34:20] 🔧 不分配页面价格给颜色，使用交互式方案获取准确价格
[22:34:20] 🔍 商品类型分析:
[22:34:20]    颜色数量: 4
[22:34:20]    颜色直接带价格: False
[22:34:20]    页面有券前/券后价格: False
[22:34:20]    尺码带价格: False
[22:34:20] 📊 分析类型: type3_multiple_colors_no_prices
[22:34:20] 🔧 处理方案: interactive
[22:34:20] 📊 坐标对比结果（仅对比原有颜色）:
[22:34:20]    衬衫: (70, 294) → (70, 294) (差异: X±0, Y±0)
[22:34:20]    ✅ 衬衫 坐标无显著变化
[22:34:20]    裤子: (208, 294) → (209, 294) (差异: X±1, Y±0)
[22:34:20]    ✅ 裤子 坐标无显著变化
[22:34:20]    套装: (353, 294) → (354, 294) (差异: X±1, Y±0)
[22:34:20]    ✅ 套装 坐标无显著变化
[22:34:20]    分类: (38, 141) → (38, 141) (差异: X±0, Y±0)
[22:34:20]    ✅ 分类 坐标无显著变化
[22:34:20] ✅ 所有颜色坐标无显著变化，继续使用原始坐标
[22:34:20] ✅ 坐标无变化，继续使用原始坐标
[22:34:20] 🎨 处理颜色 2/4: 裤子
[22:34:20] 📍 使用记录的坐标: 裤子
[22:34:20]    相对坐标: (208, 294)
[22:34:20]    绝对坐标: (478, 524)
[22:34:20] 🎯 移动到颜色 裤子 坐标: (478, 524)
[22:34:21] 🎯 点击颜色 裤子
[22:34:23] 📸 截取颜色 裤子 更新后的页面...
[22:34:23] ✅ 价格OCR截图已保存: 商品图片\5-2.jpg
[22:34:25] 价格OCR结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[22:34:25] 字典格式价格OCR识别到 16 个文本
[22:34:25] 价格OCR识别: 包邮价¥29.6￥29.9 (置信度: 0.916)
[22:34:25] 价格OCR识别: 已选：裤子120 (置信度: 0.993)
[22:34:25] 价格OCR识别: 颜色分类 (置信度: 1.000)
[22:34:25] 价格OCR识别: 衬衫 (置信度: 1.000)
[22:34:25] 价格OCR识别: 裤子 (置信度: 1.000)
[22:34:25] 价格OCR识别: 套装 (置信度: 1.000)
[22:34:25] 价格OCR识别: 参考分类 (置信度: 1.000)
[22:34:25] 价格OCR识别: 90 (置信度: 1.000)
[22:34:25] 价格OCR识别: 100 (置信度: 1.000)
[22:34:25] 价格OCR识别: 110 (置信度: 1.000)
[22:34:25] 价格OCR识别: 120 (置信度: 1.000)
[22:34:25] 价格OCR识别: 130 (置信度: 1.000)
[22:34:25] 价格OCR识别: 140 (置信度: 1.000)
[22:34:25] 价格OCR识别: 免费服务 (置信度: 1.000)
[22:34:25] 价格OCR识别: 退货包运费(商家赠送) (置信度: 0.921)
[22:34:25] 价格OCR识别: 一次选多款，不满意可退货包运费> (置信度: 0.980)
[22:34:25] ✅ 价格OCR识别完成，共识别到 16 个文本
[22:34:25] ✅ 提取到通用价格: 29.9 (来源: 包邮价¥29.6￥29.9)
[22:34:25] ✅ 获取到颜色 裤子 的价格: 29.9
[22:34:25] 🎨 处理颜色 3/4: 套装
[22:34:25] 📍 使用记录的坐标: 套装
[22:34:25]    相对坐标: (353, 294)
[22:34:25]    绝对坐标: (623, 524)
[22:34:25] 🎯 移动到颜色 套装 坐标: (623, 524)
[22:34:26] 🎯 点击颜色 套装
[22:34:28] 📸 截取颜色 套装 更新后的页面...
[22:34:28] ✅ 价格OCR截图已保存: 商品图片\5-3.jpg
[22:34:31] 价格OCR结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[22:34:31] 字典格式价格OCR识别到 19 个文本
[22:34:31] 价格OCR识别: 包邮价￥48.3￥58.9 (置信度: 0.933)
[22:34:31] 价格OCR识别: 1 (置信度: 0.997)
[22:34:31] 价格OCR识别: 已选：套装120 (置信度: 0.997)
[22:34:31] 价格OCR识别: 优惠 (置信度: 1.000)
[22:34:31] 价格OCR识别: 已享8.3折·共优惠10.6元> (置信度: 0.984)
[22:34:31] 价格OCR识别: 颜色分类 (置信度: 1.000)
[22:34:31] 价格OCR识别: 衬衫 (置信度: 1.000)
[22:34:31] 价格OCR识别: 裤子 (置信度: 1.000)
[22:34:31] 价格OCR识别: 套装 (置信度: 0.999)
[22:34:31] 价格OCR识别: 参考分类 (置信度: 1.000)
[22:34:31] 价格OCR识别: 90 (置信度: 1.000)
[22:34:31] 价格OCR识别: 100 (置信度: 1.000)
[22:34:31] 价格OCR识别: 110 (置信度: 1.000)
[22:34:31] 价格OCR识别: 120 (置信度: 1.000)
[22:34:31] 价格OCR识别: 130 (置信度: 1.000)
[22:34:31] 价格OCR识别: 140 (置信度: 1.000)
[22:34:31] 价格OCR识别: 免费服务 (置信度: 1.000)
[22:34:31] 价格OCR识别: 退货包运费(商家赠送） (置信度: 0.931)
[22:34:31] 价格OCR识别: 一次选多款，不满意可退货包运费> (置信度: 0.963)
[22:34:31] ✅ 价格OCR识别完成，共识别到 19 个文本
[22:34:31] ✅ 提取到通用价格: 48.3 (来源: 包邮价￥48.3￥58.9)
[22:34:31] ✅ 获取到颜色 套装 的价格: 48.3
[22:34:31] 🎨 处理颜色 4/4: 分类
[22:34:31] 📍 使用记录的坐标: 分类
[22:34:31]    相对坐标: (38, 141)
[22:34:31]    绝对坐标: (308, 371)
[22:34:31] 🎯 移动到颜色 分类 坐标: (308, 371)
[22:34:32] 🎯 点击颜色 分类
[22:34:34] 📸 截取颜色 分类 更新后的页面...
[22:34:34] ✅ 价格OCR截图已保存: 商品图片\5-4.jpg
[22:34:36] 价格OCR结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[22:34:36] 字典格式价格OCR识别到 19 个文本
[22:34:36] 价格OCR识别: 包邮价￥48.3￥58.9 (置信度: 0.934)
[22:34:36] 价格OCR识别: 1 (置信度: 0.997)
[22:34:36] 价格OCR识别: 已选：套装120 (置信度: 0.997)
[22:34:36] 价格OCR识别: 优惠 (置信度: 1.000)
[22:34:36] 价格OCR识别: 已享8.3折·共优惠10.6元> (置信度: 0.984)
[22:34:36] 价格OCR识别: 颜色分类 (置信度: 1.000)
[22:34:36] 价格OCR识别: 衬衫 (置信度: 1.000)
[22:34:36] 价格OCR识别: 裤子 (置信度: 1.000)
[22:34:36] 价格OCR识别: 套装 (置信度: 0.999)
[22:34:36] 价格OCR识别: 参考分类 (置信度: 1.000)
[22:34:36] 价格OCR识别: 90 (置信度: 1.000)
[22:34:36] 价格OCR识别: 100 (置信度: 1.000)
[22:34:36] 价格OCR识别: 110 (置信度: 1.000)
[22:34:36] 价格OCR识别: 120 (置信度: 1.000)
[22:34:36] 价格OCR识别: 130 (置信度: 1.000)
[22:34:36] 价格OCR识别: 140 (置信度: 1.000)
[22:34:36] 价格OCR识别: 免费服务 (置信度: 1.000)
[22:34:36] 价格OCR识别: 退货包运费(商家赠送） (置信度: 0.916)
[22:34:36] 价格OCR识别: 一次选多款，不满意可退货包运费> (置信度: 0.963)
[22:34:36] ✅ 价格OCR识别完成，共识别到 19 个文本
[22:34:36] ✅ 提取到通用价格: 48.3 (来源: 包邮价￥48.3￥58.9)
[22:34:36] ✅ 获取到颜色 分类 的价格: 48.3
[22:34:36] 💾 保存分析结果到Excel...
[22:34:36] 找到目标商品链接在第 73 行
[22:34:36] ✅ 保存有价格颜色: 衬衫 -> 36.5
[22:34:36] ✅ 保存有价格颜色: 裤子 -> 29.9
[22:34:36] ✅ 保存有价格颜色: 套装 -> 48.3
[22:34:36] ✅ 保存有价格颜色: 分类 -> 48.3
[22:34:36] 找到下一个商品链接在第 84 行
[22:34:36] 清空第 74 行到第 83 行中的 0 行有内容数据，保留空行
[22:34:36] ✅ 分析结果已保存到商品5下方: 商品图片\商品SKU信息.xlsx
[22:34:36]    插入了 6 行新数据
[22:34:36] 🎉 交互式价格获取完成
[22:34:36] ==================================================
[22:34:36] ✅ 智能OCR分析完成
[22:34:36] ==================================================
[22:34:36] ✅ 详情页图片捕获完成
[22:34:36] ✅ OCR分析和Excel保存已在详情页处理中完成
[22:34:36] ✅ 第 5 个商品处理完成
[22:34:36] 🔄 准备处理下一个商品...
[22:34:36] 🔄 开始返回搜索页面...
[22:34:36] 移动到位置(470,590)...
[22:34:37] 点击鼠标右键...
[22:34:38] 再次点击鼠标右键...
[22:34:39] ✅ 返回搜索页面操作完成
[22:34:39] 
============================================================
[22:34:39] 🎯 开始处理第 6 个商品
[22:34:39] 商品链接: https://mobile.yangkeduo.com/goods1.html?ps=sz68QHembv
[22:34:39] ============================================================
[22:34:39] 步骤1: 设置剪贴板内容...
[22:34:39] 已复制商品链接到剪贴板: https://mobile.yangkeduo.com/goods1.html?ps=sz68QHembv
[22:34:40] ✅ 剪贴板内容验证成功
[22:34:40] 步骤2: 点击搜索框并输入链接...
[22:34:40] 移动到位置(480,96)并点击...
[22:34:40] 按下Ctrl+A全选...
[22:34:40] 执行AutoHotkey脚本: ctrl_a.ahk
[22:34:42] ✅ AutoHotkey脚本执行成功: ctrl_a
[22:34:42] 粘贴商品链接...
[22:34:42] 使用外部脚本软件执行真正的Ctrl+V...
[22:34:42] Python的键盘模拟无法被escrcpy识别，尝试其他方案
[22:34:42] 方法1: 尝试AutoHotkey...
[22:34:42] 找到脚本文件: paste_v2.ahk
[22:34:42] 检查路径: AutoHotkey.exe
[22:34:42] 检查路径: C:\Program Files\AutoHotkey\AutoHotkey.exe
[22:34:42] 检查路径: C:\Program Files (x86)\AutoHotkey\AutoHotkey.exe
[22:34:42] 检查路径: C:\Program Files\AutoHotkey\v2\AutoHotkey.exe
[22:34:42] 找到AutoHotkey: C:\Program Files\AutoHotkey\v2\AutoHotkey.exe
[22:34:42] 执行AutoHotkey脚本: paste_v2.ahk
[22:34:44] AutoHotkey返回码: 0
[22:34:44] ✅ AutoHotkey执行成功
[22:34:44] ✅ Ctrl+V操作执行成功
[22:34:47] 步骤3: 点击搜索按钮...
[22:34:47] 正在查找图片: SOUSUO2.png (路径: image\IQOO\SOUSUO2.png)
[22:34:47] 查找或点击图片 SOUSUO2.png 时出错: 
[22:34:47] 错误：未找到搜索按钮图片 SOUSUO2.png
[22:34:47] ❌ 第 6 个商品处理失败
[22:34:47] ❌ 自动化操作失败，请检查日志

