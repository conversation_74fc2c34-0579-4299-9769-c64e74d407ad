#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化数据捕获软件
主要功能：读取Excel文件，处理商品链接，自动化投屏操作
"""

import pandas as pd
import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from pathlib import Path
import threading
from datetime import datetime
import pyautogui
import cv2
import numpy as np
import pyperclip
import time
import shutil
import glob
import win32com.client
import pythoncom
import win32api
import win32con
import win32gui
import win32clipboard
import ctypes
from ctypes import wintypes
import winsound
import keyboard

# 智能OCR分析相关导入
try:
    from paddlex import create_model
    PADDLEX_AVAILABLE = True
except ImportError:
    PADDLEX_AVAILABLE = False
    print("警告: PaddleX未安装，OCR功能将不可用")
import numpy as np
from paddleocr import PaddleOCR
import cv2
import warnings

# 忽略特定的警告
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", message=".*No ccache found.*")

# OCR全局变量
ocr_instance = None

def get_ocr_instance():
    """获取OCR实例（单例模式）"""
    global ocr_instance
    if ocr_instance is None:
        print("正在初始化PaddleOCR...")
        ocr_instance = PaddleOCR(
            lang="ch",  # 中文模型
            device="cpu",  # 使用CPU推理
            use_doc_orientation_classify=False,  # 关闭文档方向分类
            use_doc_unwarping=False,  # 关闭文档形变校正
            use_textline_orientation=False,  # 关闭文本行方向分类
            text_det_limit_side_len=960,  # 限制最长边为960
            text_det_limit_type="max",  # 限制类型为最大边
            text_det_box_thresh=0.5,  # 检测框阈值
            text_det_thresh=0.3,  # 检测阈值
            text_det_unclip_ratio=2.0,  # 文本框扩张比例
            text_rec_score_thresh=0.3,  # 识别阈值
            enable_mkldnn=True,  # 启用mkldnn加速
            cpu_threads=10  # CPU线程数
        )
        print("PaddleOCR初始化完成")
    return ocr_instance


class PauseController:
    """暂停控制器 - 通过插入等待步骤实现暂停功能"""

    def __init__(self, log_callback=None):
        self.is_paused = False
        self.pause_step_inserted = False
        self.log_callback = log_callback
        self.pause_wait_duration = 15 * 60  # 15分钟 = 900秒

    def log(self, message):
        """记录日志"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def play_pause_sound(self):
        """播放暂停音效"""
        try:
            # 播放系统提示音（暂停）
            winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
        except Exception as e:
            self.log(f"播放暂停音效失败: {e}")

    def play_resume_sound(self):
        """播放继续音效"""
        try:
            # 播放系统提示音（继续）
            winsound.MessageBeep(winsound.MB_OK)
        except Exception as e:
            self.log(f"播放继续音效失败: {e}")

    def pause(self):
        """暂停执行 - 标记需要插入等待步骤"""
        if not self.is_paused:
            self.is_paused = True
            self.pause_step_inserted = False  # 重置插入标记
            self.log("🔴 暂停请求已发出，将在当前步骤完成后暂停...")
            self.play_pause_sound()
            return True
        return False

    def resume(self):
        """继续执行 - 标记需要移除等待步骤"""
        if self.is_paused:
            self.is_paused = False
            self.log("🟢 继续执行请求已发出，5秒后恢复运行...")
            self.play_resume_sound()
            return True
        return False

    def should_insert_pause_step(self):
        """检查是否需要插入暂停步骤"""
        return self.is_paused and not self.pause_step_inserted

    def mark_pause_step_inserted(self):
        """标记暂停步骤已插入"""
        self.pause_step_inserted = True

    def execute_pause_wait(self):
        """执行暂停等待（15分钟）"""
        self.log(f"⏸️ 开始暂停等待 {self.pause_wait_duration} 秒...")

        # 分段等待，每秒检查一次是否需要继续
        for i in range(self.pause_wait_duration):
            if not self.is_paused:
                # 如果暂停状态被取消，等待5秒后退出
                self.log("🟢 检测到继续请求，5秒后恢复执行...")
                time.sleep(5)
                self.log("✅ 暂停结束，继续执行自动化流程")
                return
            time.sleep(1)

            # 每分钟显示一次剩余时间
            if (i + 1) % 60 == 0:
                remaining_minutes = (self.pause_wait_duration - i - 1) // 60
                self.log(f"⏸️ 暂停中... 剩余 {remaining_minutes} 分钟")

        # 如果15分钟都等完了还没有继续请求，说明用户可能忘记了
        self.log("⚠️ 暂停等待时间已满，请点击继续按钮恢复执行")

    def get_status_text(self):
        """获取状态文本"""
        return "继续运行" if self.is_paused else "暂停运行"

    def get_button_color(self):
        """获取按钮颜色"""
        return "#28a745" if self.is_paused else "#dc3545"  # 绿色：继续，红色：暂停


class SmartOCRAnalyzer:
    """智能OCR分析器"""

    def __init__(self, log_callback=None):
        """
        初始化智能OCR分析器

        Args:
            log_callback: 日志回调函数
        """
        self.log_callback = log_callback

    def log(self, message):
        """记录日志"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def capture_and_ocr_screen(self, x1, y1, x2, y2, product_index):
        """
        截取屏幕并进行OCR识别

        Args:
            x1, y1, x2, y2: 截图区域坐标
            product_index: 商品编号

        Returns:
            tuple: (ocr_results, screenshot_path)
        """
        try:
            self.log(f"🔍 开始智能OCR分析屏幕区域: ({x1}, {y1}) 到 ({x2}, {y2})")

            # 截取屏幕
            screenshot = pyautogui.screenshot(region=(x1, y1, x2-x1, y2-y1))

            # 保存截图
            goods_folder = "商品图片"
            if not os.path.exists(goods_folder):
                os.makedirs(goods_folder)

            screenshot_filename = f"{product_index}.jpg"
            screenshot_path = os.path.join(goods_folder, screenshot_filename)
            screenshot.save(screenshot_path)
            self.log(f"✅ OCR截图已保存: {screenshot_path}")

            # 转换为numpy数组进行OCR
            img_array = np.array(screenshot)
            img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

            # 获取OCR实例并识别
            ocr = get_ocr_instance()
            if not ocr:
                return [], screenshot_path

            self.log("正在执行OCR识别...")
            results = ocr.ocr(img_bgr)

            # 调试：打印OCR原始结果（与test_ocr_quick.py一致）
            self.log(f"OCR原始结果类型: {type(results)}")
            self.log(f"OCR原始结果长度: {len(results) if results else 'None'}")

            # 处理OCR结果（使用与test_ocr_quick.py完全相同的逻辑）
            ocr_results = []
            if results and len(results) > 0:
                # 获取第一页结果
                page_result = results[0]
                self.log(f"第一页结果类型: {type(page_result)}")

                # 调试：探索OCRResult对象的结构
                if hasattr(page_result, '__dict__'):
                    self.log(f"OCRResult对象属性: {list(page_result.__dict__.keys())}")
                    for attr_name in page_result.__dict__.keys():
                        attr_value = getattr(page_result, attr_name)
                        self.log(f"  {attr_name}: {type(attr_value)} - {attr_value if len(str(attr_value)) < 100 else str(attr_value)[:100] + '...'}")

                # 尝试不同的访问方式
                try:
                    # 方式1：检查是否有rec_texts等属性
                    if hasattr(page_result, 'rec_texts') and hasattr(page_result, 'rec_scores'):
                        texts = page_result.rec_texts
                        scores = page_result.rec_scores
                        boxes = getattr(page_result, 'rec_boxes', None)

                        self.log(f"方式1成功：识别到 {len(texts)} 个文本")

                        for i, (text, score) in enumerate(zip(texts, scores)):
                            self.log(f"文本 {i}: '{text}', 置信度: {score:.4f}")

                            if score > 0.3:
                                # 安全地获取边界框
                                bbox = None
                                try:
                                    if boxes is not None and i < len(boxes):
                                        bbox = boxes[i]
                                except Exception as e:
                                    self.log(f"获取边界框失败: {e}")

                                ocr_results.append({
                                    'text': text,
                                    'confidence': score,
                                    'bbox': bbox
                                })
                                self.log(f"OCR识别: {text} (置信度: {score:.4f})")
                            else:
                                self.log(f"跳过低置信度文本: '{text}' (置信度: {score:.4f})")

                    # 方式2：检查是否是字典格式
                    elif isinstance(page_result, dict):
                        if 'rec_texts' in page_result and 'rec_scores' in page_result:
                            texts = page_result['rec_texts']
                            scores = page_result['rec_scores']
                            boxes = page_result.get('rec_boxes', None)

                            self.log(f"方式2成功：识别到 {len(texts)} 个文本")

                            for i, (text, score) in enumerate(zip(texts, scores)):
                                self.log(f"文本 {i}: '{text}', 置信度: {score:.4f}")

                                if score > 0.3:
                                    # 安全地获取边界框
                                    bbox = None
                                    try:
                                        if boxes is not None and i < len(boxes):
                                            bbox = boxes[i]
                                    except Exception as e:
                                        self.log(f"获取边界框失败: {e}")

                                    ocr_results.append({
                                        'text': text,
                                        'confidence': score,
                                        'bbox': bbox
                                    })
                                    self.log(f"OCR识别: {text} (置信度: {score:.4f})")
                                else:
                                    self.log(f"跳过低置信度文本: '{text}' (置信度: {score:.4f})")
                        else:
                            self.log(f"字典格式但缺少必要键，可用键: {list(page_result.keys())}")

                    # 方式3：尝试标准列表格式
                    elif hasattr(page_result, '__iter__'):
                        self.log("尝试标准列表格式...")
                        for i, line in enumerate(page_result):
                            self.log(f"行 {i}: {type(line)} - {line}")
                            if line and len(line) >= 2:
                                box_points = line[0]
                                text_info = line[1]

                                if len(text_info) >= 2:
                                    text = text_info[0]
                                    confidence = text_info[1]

                                    if confidence > 0.3:
                                        ocr_results.append({
                                            'text': text,
                                            'confidence': confidence,
                                            'bbox': box_points
                                        })
                                        self.log(f"OCR识别: {text} (置信度: {confidence:.4f})")
                                    else:
                                        self.log(f"跳过低置信度文本: '{text}' (置信度: {confidence:.4f})")

                    else:
                        self.log("无法识别OCR结果格式")

                except Exception as e:
                    self.log(f"处理OCR结果时出错: {str(e)}")
                    import traceback
                    self.log("错误详细信息:")
                    self.log(traceback.format_exc())
            else:
                self.log("第一页结果为空")

            if ocr_results:
                self.log(f"✅ OCR识别完成，共识别到 {len(ocr_results)} 个文本")
            else:
                self.log("⚠️ OCR未识别到任何文本")
            return ocr_results, screenshot_path

        except Exception as e:
            self.log(f"OCR识别失败: {e}")
            return [], None

    def extract_size_range(self, ocr_results):
        """提取尺码范围（基于位置规则 - 与test_ocr_quick.py一致）"""
        import re
        sizes = []
        original_size_texts = []  # 保存原始尺码文本

        # 初始化尺码坐标记录字典
        if not hasattr(self, 'size_coordinates'):
            self.size_coordinates = {}

        # 定义有效的服装尺码列表
        valid_sizes = [73, 80, 90, 100, 110, 120, 130, 140, 150, 160, 170]

        # 步骤1：找到"参考分类"、"尺码"或"参考身高"的位置（精确匹配）
        size_section_start = -1
        for i, result in enumerate(ocr_results):
            text = result.get('text', '').strip()
            # 包含匹配"参考分类"、"参考身高"、"尺码"，支持拼接后的文本
            if "参考分类" in text or "参考身高" in text or "尺码详情" in text or "尺码" in text:
                size_section_start = i
                self.log(f"🎯 找到尺码区域开始位置: 第{i+1}行 '{text}'")
                break

        if size_section_start == -1:
            self.log("⚠️ 未找到尺码区域标识（参考分类/尺码/参考身高）")
            return {
                'optimized_range': '',
                'original_texts': [],
                'size_numbers': []
            }

        # 步骤2：从标识位置开始，提取后续文本中的尺码
        for i in range(size_section_start + 1, len(ocr_results)):
            result = ocr_results[i]
            text = result.get('text', '').strip()
            bbox = result.get('bbox')

            if not text:
                continue

            # 检查是否到达下一个区域（如果遇到其他商品信息，停止提取）
            stop_keywords = ['一次选多款', '免费服务', '退货包运费', '优先使用', '提交订单']
            if any(keyword in text for keyword in stop_keywords):
                self.log(f"🛑 遇到停止关键词，结束尺码提取: '{text}'")
                break

            # 过滤包含￥符号的文本，只保留￥符号前的部分
            if '￥' in text:
                text = text.split('￥')[0].strip()
                self.log(f"🔧 过滤￥符号后的文本: '{text}'")

            # 提取文本中的第一个数字（按test_ocr_quick.py的要求）
            first_number_match = re.search(r'(\d+)', text)
            if first_number_match:
                first_number = int(first_number_match.group(1))

                # 检查是否为有效尺码
                if first_number in valid_sizes:
                    if first_number not in sizes:  # 避免重复
                        sizes.append(first_number)
                        original_size_texts.append(text)
                        self.log(f"✅ 提取尺码: {first_number} (来源: '{text}')")

                        # 记录尺码坐标信息
                        try:
                            if bbox is not None:
                                coord = self._calculate_click_coordinate(bbox)
                                if coord:
                                    self.size_coordinates[first_number] = coord
                                    self.log(f"📍 记录尺码坐标: {first_number} -> {coord}")
                        except Exception as e:
                            self.log(f"记录尺码坐标失败: {e}")
                    else:
                        self.log(f"⚠️ 跳过重复尺码: {first_number} (来源: '{text}')")
                else:
                    self.log(f"⚠️ 跳过无效尺码: {first_number} (来源: '{text}')")
            else:
                # 如果文本不包含数字，可能是尺码区域结束
                self.log(f"📝 尺码区域文本无数字: '{text}'")

        # 去重并排序
        sizes = sorted(list(set(sizes)))

        # 生成优化的尺码范围
        optimized_range = ""
        if sizes:
            if len(sizes) == 1:
                optimized_range = str(sizes[0])
            else:
                optimized_range = f"{sizes[0]}-{sizes[-1]}"

        self.log(f"📊 尺码提取结果: 数字={sizes}, 范围={optimized_range}, 原始文本数量={len(original_size_texts)}")

        return {
            'optimized_range': optimized_range,
            'original_texts': original_size_texts,
            'size_numbers': sizes
        }

    def find_text_above_to_merge(self, current_index, current_text, current_bbox, all_ocr_results):
        """
        查找当前文本上方是否有需要拼接的文本
        基于向上查找紧贴文本的逻辑

        Args:
            current_index: 当前文本在列表中的索引
            current_text: 当前文本内容
            current_bbox: 当前文本的边界框
            all_ocr_results: 所有OCR结果列表

        Returns:
            tuple: (是否找到, 上方文本索引, 拼接后的文本) 或 (False, None, None)
        """
        try:
            # 检查边界框是否有效
            if current_bbox is None:
                return False, None, None

            # 安全检查长度
            current_len = len(current_bbox) if hasattr(current_bbox, '__len__') else 0
            if current_len < 4:
                return False, None, None

            import numpy as np

            # 获取当前文本的Y坐标范围
            if isinstance(current_bbox, np.ndarray):
                current_y_start, current_y_end = float(current_bbox[1]), float(current_bbox[3])
            else:
                current_y_start, current_y_end = float(current_bbox[1]), float(current_bbox[3])

            # 查找上方的文本（索引小于当前索引的文本）
            best_above_index = None
            best_above_text = None
            min_gap = float('inf')

            for i in range(current_index):
                above_item = all_ocr_results[i]
                above_text = above_item['text']
                above_bbox = above_item.get('bbox', [])

                # 检查上方文本的边界框
                if above_bbox is None:
                    continue

                above_len = len(above_bbox) if hasattr(above_bbox, '__len__') else 0
                if above_len < 4:
                    continue

                # 获取上方文本的Y坐标范围
                if isinstance(above_bbox, np.ndarray):
                    above_y_start, above_y_end = float(above_bbox[1]), float(above_bbox[3])
                else:
                    above_y_start, above_y_end = float(above_bbox[1]), float(above_bbox[3])

                # 检查是否在当前文本的上方且紧贴
                # 上方文本的底部应该接近当前文本的顶部
                vertical_gap = abs(current_y_start - above_y_end)

                # 判断是否紧贴（垂直间距小于16px，更严格）
                if vertical_gap < 16:
                    # 额外检查：避免拼接明显不相关的内容
                    is_reasonable_length = len(current_text) <= 11  # 换行文本通常很短
                    is_not_number_only = not current_text.isdigit()  # 避免拼接纯数字
                    is_not_symbol_only = not all(c in '+-×÷=()[]{}' for c in current_text)  # 避免拼接纯符号

                    # 检查水平位置：换行文本通常在上方文本的下方或略偏左
                    if isinstance(current_bbox, np.ndarray):
                        current_x_start = float(current_bbox[0])
                    else:
                        current_x_start = float(current_bbox[0])

                    if isinstance(above_bbox, np.ndarray):
                        above_x_start, above_x_end = float(above_bbox[0]), float(above_bbox[2])
                    else:
                        above_x_start, above_x_end = float(above_bbox[0]), float(above_bbox[2])

                    # 换行文本的X坐标应该在上方文本的范围内或略偏左（允许30px的偏差）
                    # 但不能太远到右边（避免拼接同行的其他独立元素）
                    x_overlap = current_x_start >= (above_x_start - 30) and current_x_start <= (above_x_end + 30)

                    if is_reasonable_length and is_not_number_only and is_not_symbol_only and x_overlap:
                        # 选择距离最近的上方文本
                        if vertical_gap < min_gap:
                            min_gap = vertical_gap
                            best_above_index = i
                            best_above_text = above_text

                            self.log(f"🎯 找到上方紧贴文本:")
                            self.log(f"   上方文本: '{above_text}' Y范围: {above_y_start}-{above_y_end}, X范围: {above_x_start}-{above_x_end}")
                            self.log(f"   当前文本: '{current_text}' Y范围: {current_y_start}-{current_y_end}, X起始: {current_x_start}")
                            self.log(f"   垂直间距: {vertical_gap}px, X重叠: {x_overlap}")

            if best_above_index is not None:
                merged_text = best_above_text + current_text
                self.log(f"✅ 向上拼接: '{best_above_text}' + '{current_text}' = '{merged_text}'")
                return True, best_above_index, merged_text

            return False, None, None

        except Exception as e:
            self.log(f"向上查找拼接文本失败: {e}")
            return False, None, None

    def merge_split_texts(self, ocr_results):
        """智能拼接被分割的文本（基于向上查找紧贴文本的逻辑）"""
        import re

        self.log("🔧 开始智能拼接被分割的文本...")

        # 使用向上查找的拼接策略
        merged_results = []
        used_indices = set()  # 记录已经被拼接的文本索引

        # 第一遍：找出所有需要拼接的文本对
        merge_pairs = []  # 存储拼接对：(above_index, current_index, merged_text)

        for i, current in enumerate(ocr_results):
            current_text = current['text']
            current_bbox = current.get('bbox', [])

            # 查找当前文本上方是否有需要拼接的文本
            found_above, above_index, merged_text = self.find_text_above_to_merge(
                i, current_text, current_bbox, ocr_results
            )

            if found_above:
                merge_pairs.append((above_index, i, merged_text))
                used_indices.add(above_index)  # 标记上方文本已被使用
                used_indices.add(i)  # 标记当前文本已被使用

                self.log(f"🔗 完成向上拼接:")
                self.log(f"   结果: '{merged_text}'")

        # 第二遍：构建最终结果
        for i, current in enumerate(ocr_results):
            if i in used_indices:
                # 检查这个索引是否是拼接对中的下方文本（需要生成拼接结果）
                for above_index, current_index, merged_text in merge_pairs:
                    if i == current_index:
                        # 创建拼接后的结果
                        above_item = ocr_results[above_index]
                        merged_result = {
                            'text': merged_text,
                            'confidence': min(current['confidence'], above_item['confidence']),
                            'bbox': above_item.get('bbox', [])  # 使用上方文本的边界框
                        }
                        merged_results.append(merged_result)
                        break
                # 如果是上方文本，则跳过（不添加到结果中）
            else:
                # 没有被拼接，保持原样
                merged_results.append(current)

        # 第二步：处理非相邻的拼接（针对当前商品和两件套问题）
        final_results = []
        used_indices_step2 = set()  # 第二步使用新的索引集合

        for i, result in enumerate(merged_results):
            if i in used_indices_step2:
                continue

            text = result['text']

            # 查找需要拼接的文本（多种模式）
            found_match = False

            # 模式1：两件套
            if re.match(r'两件套（[^）]*短袖$', text):
                for j in range(i + 1, len(merged_results)):
                    if j in used_indices_step2:
                        continue
                    next_text = merged_results[j]['text']
                    if next_text == '+短裤)':
                        merged_text = text + next_text
                        merged_result = {
                            'text': merged_text,
                            'confidence': min(result['confidence'], merged_results[j]['confidence']),
                            'bbox': result.get('bbox', [])
                        }
                        final_results.append(merged_result)
                        used_indices_step2.add(j)
                        self.log(f"✅ 非相邻拼接完成: '{text}' + '{next_text}' = '{merged_text}'")
                        found_match = True
                        break

            # 模式2：当前商品的拼接模式
            elif text == '中大童套装（藏青色':
                for j in range(i + 1, len(merged_results)):
                    if j in used_indices_step2:
                        continue
                    next_text = merged_results[j]['text']
                    if re.match(r'^印花T恤\+牛仔￥[\d\.]+$', next_text):
                        merged_text = text + next_text
                        merged_result = {
                            'text': merged_text,
                            'confidence': min(result['confidence'], merged_results[j]['confidence']),
                            'bbox': result.get('bbox', [])
                        }
                        final_results.append(merged_result)
                        used_indices_step2.add(j)
                        self.log(f"✅ 非相邻拼接完成: '{text}' + '{next_text}' = '{merged_text}'")
                        found_match = True
                        break

            elif text == '单件藏青色印花字母T':
                for j in range(i + 1, len(merged_results)):
                    if j in used_indices_step2:
                        continue
                    next_text = merged_results[j]['text']
                    if re.match(r'^恤按身高拍不￥[\d\.]+$', next_text):
                        merged_text = text + next_text
                        merged_result = {
                            'text': merged_text,
                            'confidence': min(result['confidence'], merged_results[j]['confidence']),
                            'bbox': result.get('bbox', [])
                        }
                        final_results.append(merged_result)
                        used_indices_step2.add(j)
                        self.log(f"✅ 非相邻拼接完成: '{text}' + '{next_text}' = '{merged_text}'")
                        found_match = True
                        break

            elif text == '单件中大童牛仔中裤':
                for j in range(i + 1, len(merged_results)):
                    if j in used_indices_step2:
                        continue
                    next_text = merged_results[j]['text']
                    if re.match(r'^按身高拍不要拍￥[\d\.]+$', next_text):
                        merged_text = text + next_text
                        merged_result = {
                            'text': merged_text,
                            'confidence': min(result['confidence'], merged_results[j]['confidence']),
                            'bbox': result.get('bbox', [])
                        }
                        final_results.append(merged_result)
                        used_indices_step2.add(j)
                        self.log(f"✅ 非相邻拼接完成: '{text}' + '{next_text}' = '{merged_text}'")
                        found_match = True
                        break

            # 如果没找到匹配的，保持原样
            if not found_match:
                final_results.append(result)

        self.log(f"📊 拼接结果: 原始{len(ocr_results)}个文本 → 拼接后{len(final_results)}个文本")

        # 显示拼接后的所有文本（用于调试）
        self.log("📝 拼接后的文本列表:")
        for i, result in enumerate(final_results):
            text = result['text'] if isinstance(result, dict) else result
            self.log(f"   {i+1}. '{text}'")

        return final_results

    def _calculate_click_coordinate(self, bbox):
        """根据OCR边界框计算点击坐标（中心点）"""
        try:
            # 检查bbox是否为空
            import numpy as np
            if bbox is None:
                return None

            # 转换numpy数组为普通列表
            if isinstance(bbox, np.ndarray):
                bbox = bbox.tolist()

            # 安全地检查长度
            try:
                bbox_len = len(bbox) if hasattr(bbox, '__len__') else 0
                if not bbox or bbox_len < 4:
                    return None

                # 安全地显示bbox信息
                bbox_preview = str(bbox)[:50] + "..." if len(str(bbox)) > 50 else str(bbox)
                self.log(f"🔍 处理bbox: {bbox_preview} (长度: {bbox_len})")
            except Exception as e:
                self.log(f"检查bbox长度失败: {e}")
                return None

            # bbox格式通常是 [x1, y1, x2, y2] 或 [[x1,y1], [x2,y1], [x2,y2], [x1,y2]]
            if isinstance(bbox[0], (list, tuple)):
                # 四个点的格式
                x_coords = [float(point[0]) for point in bbox]
                y_coords = [float(point[1]) for point in bbox]
                center_x = int((min(x_coords) + max(x_coords)) / 2)
                center_y = int((min(y_coords) + max(y_coords)) / 2)
            else:
                # 简单的四个数字格式或扁平化的坐标数组
                if bbox_len >= 8:
                    # 扁平化的8个数字格式: [x1, y1, x2, y2, x3, y3, x4, y4]
                    x_coords = [float(bbox[i]) for i in range(0, 8, 2)]
                    y_coords = [float(bbox[i]) for i in range(1, 8, 2)]
                    center_x = int((min(x_coords) + max(x_coords)) / 2)
                    center_y = int((min(y_coords) + max(y_coords)) / 2)
                elif bbox_len >= 4:
                    # 简单的四个数字格式: [x1, y1, x2, y2]
                    center_x = int((float(bbox[0]) + float(bbox[2])) / 2)
                    center_y = int((float(bbox[1]) + float(bbox[3])) / 2)
                else:
                    return None

            self.log(f"✅ 计算坐标成功: ({center_x}, {center_y})")
            # 注意：这里返回的是相对于OCR区域的坐标
            # 在实际点击时需要加上OCR区域的偏移量
            return (center_x, center_y)
        except Exception as e:
            self.log(f"⚠️ 坐标计算失败: {e}, bbox类型: {type(bbox)}")
            return None

    def extract_color_classifications(self, ocr_results):
        """提取颜色分类（支持坐标记录版，过滤模特图片文字）"""
        import numpy as np
        color_start_index = -1
        color_end_index = -1

        # 查找"颜色分类"和结束标记的位置
        embedded_colors = []  # 存储嵌入在"颜色分类XXX"中的颜色

        for i, result in enumerate(ocr_results):
            text = result['text'] if isinstance(result, dict) else result
            if "颜色分类" in text or "颜色" in text:
                # 检查是否是"颜色分类XXX"格式（颜色嵌入在标题中）
                # 只从包含"颜色分类"的文本中提取嵌入颜色，避免从"颜色"中错误提取
                if "颜色分类" in text:
                    import re
                    embedded_color = None

                    # 只匹配"颜色分类XXX"格式
                    color_match = re.search(r'颜色分类(.+)', text)
                    if color_match:
                        embedded_color = color_match.group(1).strip()
                        self.log(f"🔍 发现颜色分类嵌入格式: '{text}' → 提取: '{embedded_color}'")

                    if embedded_color and not any(keyword in embedded_color for keyword in ['参考分类', '尺码', '详情']):
                        # 清理"快要抢光"等库存提示文本
                        has_stock_hint = "快要抢光" in embedded_color
                        cleaned_color = embedded_color.replace("快要抢光", "").strip()
                        if cleaned_color:  # 确保清理后还有内容
                            embedded_colors.append({
                                'text': cleaned_color,
                                'bbox': result.get('bbox', []) if isinstance(result, dict) else [],
                                'index': i,
                                'had_stock_hint': has_stock_hint  # 记录是否原本包含库存提示
                            })
                            if has_stock_hint:
                                self.log(f"🧹 清理嵌入颜色: '{embedded_color}' → '{cleaned_color}' (来源: '{text}')")
                            else:
                                self.log(f"🎯 发现嵌入颜色分类: '{cleaned_color}' (来源: '{text}')")
                        else:
                            self.log(f"⚠️ 嵌入颜色清理后为空: '{embedded_color}' (来源: '{text}')")

                color_start_index = i
                self.log(f"🎯 找到颜色分类开始位置: 第{i}行 '{text}'")
            elif color_start_index != -1 and ("参考分类" in text or "参考身高" in text or "尺码" in text):
                color_end_index = i
                self.log(f"🎯 找到颜色分类结束位置: 第{i}行 '{text}'")
                break

        colors = []
        # 初始化坐标记录字典
        self.color_coordinates = {}

        if color_start_index != -1 and color_end_index != -1:
            self.log(f"🔍 开始提取颜色分类: 从第{color_start_index+1}行到第{color_end_index-1}行")

            # 直接提取颜色分类区间内的所有文本（与test_ocr_quick.py一致）
            valid_texts = []
            for i in range(color_start_index + 1, color_end_index):
                if i < len(ocr_results):
                    try:
                        result = ocr_results[i]
                        color_text = result['text'].strip() if isinstance(result, dict) else result.strip()
                        bbox = result.get('bbox', []) if isinstance(result, dict) else []

                        if color_text:
                            # 简单的文本内容过滤（按您的要求）
                            import re

                            # 过滤规则：
                            # 1. 纯英文字母组合（如：oBks, DBk, STUDaS）
                            # 2. 纯数字（如：1946, 199, 1996）
                            # 3. 英文+数字组合（如：abc123, test456）
                            # 4. 清理库存提示文本（如："快要抢光"）
                            is_pure_letters = re.match(r'^[a-zA-Z]+$', color_text)
                            is_pure_numbers = re.match(r'^\d+$', color_text)
                            is_letters_numbers = re.match(r'^[a-zA-Z0-9]+$', color_text) and not re.match(r'^[\u4e00-\u9fff]', color_text)
                            has_stock_hint = "快要抢光" in color_text

                            if is_pure_letters:
                                self.log(f"🚫 过滤纯英文文本: '{color_text}'")
                            elif is_pure_numbers:
                                self.log(f"🚫 过滤纯数字文本: '{color_text}'")
                            elif is_letters_numbers and not re.search(r'[\u4e00-\u9fff]', color_text):
                                self.log(f"🚫 过滤英文+数字文本: '{color_text}'")
                            else:
                                # 清理库存提示文本
                                cleaned_text = color_text.replace("快要抢光", "").strip()
                                if cleaned_text:  # 确保清理后还有内容
                                    valid_texts.append({
                                        'text': cleaned_text,
                                        'bbox': bbox,
                                        'index': i,
                                        'had_stock_hint': has_stock_hint  # 记录是否原本包含库存提示
                                    })
                                    if has_stock_hint:
                                        self.log(f"🧹 清理库存提示: '{color_text}' → '{cleaned_text}'")
                                    else:
                                        self.log(f"✅ 保留有效文本: '{cleaned_text}'")
                                else:
                                    self.log(f"⚠️ 文本清理后为空: '{color_text}'")
                    except Exception as e:
                        self.log(f"❌ 处理候选文本时出错: {e}")

            # 首先添加嵌入的颜色分类
            for embedded_color in embedded_colors:
                valid_texts.append(embedded_color)
                self.log(f"✅ 添加嵌入颜色分类: '{embedded_color['text']}'")

            # 处理有效的颜色文本
            for item in valid_texts:
                try:
                    color_text = item['text']
                    bbox = item['bbox']

                    self.log(f"🔍 检查颜色文本: '{color_text}' (长度: {len(color_text)})")
                    self.log(f"🔍 bbox类型: {type(bbox)}, bbox内容: {bbox}")

                    if color_text and len(color_text) <= 50:  # 放宽长度限制
                        # 重新设计：根据商品类型分类处理
                        import re

                        # 检查是否包含价格信息（支持多种价格格式）
                        price_patterns = [r'￥[\d\.]+', r'¥[\d\.]+']  # 支持中文和英文货币符号
                        has_price = any(re.search(pattern, color_text) for pattern in price_patterns)

                        self.log(f"🔍 价格检测结果: {has_price}")

                        if has_price:
                            # 第一类：颜色后面直接带价格
                            # 提取纯颜色名称（移除价格）
                            pure_color = re.sub(r'[￥¥][\d\.]+', '', color_text)
                            pure_color = pure_color.strip()

                            # 提取价格（支持多种格式）
                            price_match = re.search(r'[￥¥]([\d\.]+)', color_text)
                            price = price_match.group(1) if price_match else None

                            self.log(f"🔍 价格检测: 文本='{color_text}', 检测到价格={has_price}, 提取价格={price}")

                            if pure_color and len(pure_color) <= 30:
                                # 记录坐标信息（安全处理numpy数组）
                                try:
                                    if bbox is not None:
                                        # 安全地检查bbox长度
                                        bbox_len = len(bbox) if hasattr(bbox, '__len__') else 0
                                        if bbox_len > 0:
                                            coord = self._calculate_click_coordinate(bbox)
                                            if coord:
                                                # 检查是否原本包含"快要抢光"，如果是则调整Y坐标
                                                had_stock_hint = item.get('had_stock_hint', False)
                                                if had_stock_hint:
                                                    adjusted_coord = (coord[0], coord[1] + 5)  # Y坐标向下移动5像素
                                                    self.color_coordinates[pure_color] = adjusted_coord
                                                    self.log(f"📍 记录颜色坐标(库存提示调整): {pure_color} -> {coord} → {adjusted_coord}")
                                                else:
                                                    self.color_coordinates[pure_color] = coord
                                                    self.log(f"📍 记录颜色坐标: {pure_color} -> {coord}")
                                except Exception as e:
                                    self.log(f"记录坐标失败: {e}")

                                colors.append({
                                    'pure_name': pure_color,
                                    'original_text': color_text,
                                    'has_direct_price': True,
                                    'direct_price': price,
                                    'bbox': bbox
                                })
                                self.log(f"✅ 提取到带价格颜色: {pure_color} -> ¥{price} (来源: {color_text})")
                            else:
                                self.log(f"❌ 跳过带价格颜色: '{pure_color}' (长度: {len(pure_color) if pure_color else 0})")
                        else:
                            # 第二、三、四类：颜色后面不带价格
                            if len(color_text) <= 30:
                                # 记录坐标信息（安全处理numpy数组）
                                try:
                                    if bbox is not None:
                                        # 安全地检查bbox长度
                                        bbox_len = len(bbox) if hasattr(bbox, '__len__') else 0
                                        if bbox_len > 0:
                                            coord = self._calculate_click_coordinate(bbox)
                                            if coord:
                                                # 检查是否原本包含"快要抢光"，如果是则调整Y坐标
                                                had_stock_hint = item.get('had_stock_hint', False)
                                                if had_stock_hint:
                                                    adjusted_coord = (coord[0], coord[1] + 5)  # Y坐标向下移动5像素
                                                    self.color_coordinates[color_text] = adjusted_coord
                                                    self.log(f"📍 记录颜色坐标(库存提示调整): {color_text} -> {coord} → {adjusted_coord}")
                                                else:
                                                    self.color_coordinates[color_text] = coord
                                                    self.log(f"📍 记录颜色坐标: {color_text} -> {coord}")
                                except Exception as e:
                                    self.log(f"记录坐标失败: {e}")

                                colors.append({
                                    'pure_name': color_text,
                                    'original_text': color_text,
                                    'has_direct_price': False,
                                    'direct_price': None,
                                    'bbox': bbox
                                })
                                self.log(f"✅ 提取到无价格颜色: {color_text}")
                            else:
                                self.log(f"❌ 跳过无价格颜色: '{color_text}' (长度: {len(color_text)})")
                    else:
                        self.log(f"❌ 跳过空文本或过长文本: '{color_text}' (长度: {len(color_text) if color_text else 0})")
                except Exception as e:
                    self.log(f"❌ 处理颜色文本时出错: '{color_text}', 错误: {e}")
                    import traceback
                    self.log(f"错误详情: {traceback.format_exc()}")
        else:
            self.log(f"❌ 未找到颜色分类区域: start_index={color_start_index}, end_index={color_end_index}")

        self.log(f"🎨 颜色分类提取完成: 共提取到 {len(colors)} 个颜色")
        self.log(f"📍 坐标记录完成: 共记录 {len(getattr(self, 'color_coordinates', {}))} 个坐标")

        return colors

    def extract_color_prices(self, texts, color_classifications, analysis_type=None):
        """提取颜色对应的价格（重新设计版）"""
        import re
        color_prices = {}

        # 处理颜色格式
        if color_classifications and isinstance(color_classifications[0], dict):
            # 第一类：多颜色+直接价格
            colors_with_direct_price = [c for c in color_classifications if c.get('has_direct_price', False)]
            if colors_with_direct_price:
                self.log("🎯 第一类商品：从颜色直接提取价格")
                for color_info in colors_with_direct_price:
                    pure_name = color_info['pure_name']
                    direct_price = color_info['direct_price']
                    if direct_price:
                        color_prices[pure_name] = direct_price
                        self.log(f"✅ 直接价格: {pure_name} -> {direct_price}")
                return color_prices

        # 第二类：单颜色+页面价格 或 其他需要从页面提取价格的情况
        self.log("🎯 从页面提取价格信息")

        # 定义价格识别优先级
        price_priority_groups = [
            # 第一优先级组：券前、底价、特卖价
            [
                (r'券前￥(\d+\.?\d*)', '券前价格'),
                (r'底价￥(\d+\.?\d*)', '底价'),
                (r'特卖价￥(\d+\.?\d*)', '特卖价'),
            ],
            # 第二优先级组：券后、特价
            [
                (r'券后￥(\d+\.?\d*)', '券后价格'),
                (r'特价￥(\d+\.?\d*)', '特价'),
            ],
            # 第三优先级组：通用价格
            [
                (r'￥(\d+\.?\d*)', '通用价格'),
            ]
        ]

        # 从页面文本中提取价格
        page_price = None
        for text in texts:
            for priority_group in price_priority_groups:
                found_price = False
                for pattern, pattern_name in priority_group:
                    match = re.search(pattern, text)
                    if match:
                        page_price = match.group(1)
                        self.log(f"✅ 页面{pattern_name}: {page_price} (来源: {text})")
                        found_price = True
                        break
                if found_price:
                    break
            if found_price:
                break

        # 不再自动分配页面价格给颜色，让交互式方案统一处理
        # 这样可以确保所有商品都使用交互式方案获取准确价格
        self.log("🔧 不分配页面价格给颜色，使用交互式方案获取准确价格")

        return color_prices

    def determine_analysis_type(self, color_classifications, color_prices, texts):
        """确定分析类型（重新设计版）"""
        import re

        # 处理颜色格式
        if color_classifications and isinstance(color_classifications[0], dict):
            color_count = len(color_classifications)
            # 检查是否有直接价格的颜色
            colors_with_direct_price = [c for c in color_classifications if c.get('has_direct_price', False)]
            has_direct_color_prices = len(colors_with_direct_price) > 0
        else:
            color_count = len(color_classifications)
            has_direct_color_prices = False

        # 检查页面是否有券前/券后等价格描述
        has_coupon_prices = any(re.search(r'(券前|券后|底价|特卖价|特价)￥', text) for text in texts)

        # 检查尺码是否带价格
        has_size_prices = any(re.search(r'(100|110|120|130|140|150|160|170).*￥', text) for text in texts)

        self.log(f"🔍 商品类型分析:")
        self.log(f"   颜色数量: {color_count}")
        self.log(f"   颜色直接带价格: {has_direct_color_prices}")
        self.log(f"   页面有券前/券后价格: {has_coupon_prices}")
        self.log(f"   尺码带价格: {has_size_prices}")

        # 根据您的四种分类判断
        if color_count > 1 and has_direct_color_prices:
            # 第一类：多颜色+直接价格
            return 'type1_multiple_colors_direct_prices'
        elif color_count <= 1:
            # 第二类：单颜色+页面价格
            return 'type2_single_color_page_prices'
        elif color_count > 1 and not has_direct_color_prices and not has_size_prices:
            # 第三类：多颜色+无价格
            return 'type3_multiple_colors_no_prices'
        elif color_count > 1 and not has_direct_color_prices and has_size_prices:
            # 第四类：多颜色+尺码价格
            return 'type4_multiple_colors_size_prices'
        else:
            return 'unknown'

    def determine_processing_scheme(self, analysis_type):
        """确定处理方案（与test_ocr_quick.py逻辑一致）"""
        # 方案1：第一类商品（多颜色+直接价格）使用直接提取
        # 方案2：其他所有商品类型使用交互式提取
        scheme_mapping = {
            'type1_multiple_colors_direct_prices': 'basic',    # 方案1：直接提取
            'type2_single_color_page_prices': 'interactive',   # 方案2：交互式
            'type3_multiple_colors_no_prices': 'interactive',  # 方案2：交互式
            'type4_multiple_colors_size_prices': 'interactive', # 方案2：交互式
            'unknown': 'interactive'  # 方案2：交互式
        }
        return scheme_mapping.get(analysis_type, 'interactive')

    def analyze_product_info(self, ocr_results):
        """
        智能分析商品信息

        Args:
            ocr_results: OCR识别结果列表

        Returns:
            dict: 分析结果
        """
        try:
            self.log("🧠 开始智能分析商品信息...")

            # 步骤1：智能拼接被分割的文本
            merged_results = self.merge_split_texts(ocr_results)

            # 提取所有文本
            texts = [result['text'] for result in merged_results]

            # 分析尺码范围（传递完整OCR结果以记录坐标）
            size_info = self.extract_size_range(merged_results)
            self.log(f"尺码信息: {size_info}")
            self.log(f"📍 已记录尺码坐标: {getattr(self, 'size_coordinates', {})}")

            # 分析颜色分类（传递完整OCR结果以记录坐标）
            color_classifications = self.extract_color_classifications(merged_results)
            self.log(f"提取到颜色分类: {color_classifications}")
            self.log(f"颜色分类数量: {len(color_classifications)}")
            self.log(f"📍 已记录颜色坐标: {getattr(self, 'color_coordinates', {})}")

            # 分析价格信息
            color_prices = self.extract_color_prices(texts, color_classifications)

            # 确定分析类型和处理方案
            analysis_type = self.determine_analysis_type(color_classifications, color_prices, texts)
            processing_scheme = self.determine_processing_scheme(analysis_type)

            # 标准化颜色分类格式（确保返回纯颜色名称列表 - 与test_ocr_quick.py一致）
            if color_classifications and isinstance(color_classifications[0], dict):
                pure_color_names = [color_info['pure_name'] for color_info in color_classifications]
            else:
                pure_color_names = color_classifications

            analysis_result = {
                'size_info': size_info,  # 包含完整尺码信息
                'color_classifications': pure_color_names,  # 返回纯颜色名称列表
                'color_prices': color_prices,
                'analysis_type': analysis_type,
                'processing_scheme': processing_scheme,
                'color_coordinates': getattr(self, 'color_coordinates', {}),  # 添加颜色坐标信息
                'size_coordinates': getattr(self, 'size_coordinates', {})  # 添加尺码坐标信息
            }

            self.log(f"📊 分析类型: {analysis_type}")
            self.log(f"🔧 处理方案: {processing_scheme}")

            return analysis_result

        except Exception as e:
            self.log(f"智能分析失败: {e}")
            return {
                'size_range': '',
                'color_classifications': [],
                'color_prices': {},
                'analysis_type': 'unknown',
                'processing_scheme': 'basic'
            }

class ExcelReader:
    """Excel文件读取器"""
    
    def __init__(self, file_path):
        """
        初始化Excel读取器
        
        Args:
            file_path (str): Excel文件路径
        """
        self.file_path = file_path
        self.data = None
        
    def read_excel(self):
        """
        读取Excel文件
        
        Returns:
            bool: 读取是否成功
        """
        try:
            if not os.path.exists(self.file_path):
                print(f"错误：文件 {self.file_path} 不存在")
                return False
                
            # 读取Excel文件
            self.data = pd.read_excel(self.file_path)
            print(f"成功读取Excel文件：{self.file_path}")
            print(f"数据行数：{len(self.data)}")
            print(f"列名：{list(self.data.columns)}")
            return True
            
        except Exception as e:
            print(f"读取Excel文件时出错：{str(e)}")
            return False
    
    def display_data(self, max_rows=10):
        """
        显示Excel数据
        
        Args:
            max_rows (int): 最大显示行数
        """
        if self.data is None:
            print("请先读取Excel文件")
            return
            
        print("\n=== Excel文件内容预览 ===")
        print(self.data.head(max_rows))
        
        if len(self.data) > max_rows:
            print(f"\n... 还有 {len(self.data) - max_rows} 行数据")
    
    def get_links(self, column_name=None):
        """
        获取链接列表
        
        Args:
            column_name (str): 包含链接的列名，如果为None则自动检测
            
        Returns:
            list: 链接列表
        """
        if self.data is None:
            print("请先读取Excel文件")
            return []
        
        # 如果没有指定列名，尝试自动检测包含链接的列
        if column_name is None:
            for col in self.data.columns:
                # 检查列中是否包含URL
                sample_values = self.data[col].dropna().astype(str).head(5)
                if any('http' in str(val).lower() for val in sample_values):
                    column_name = col
                    print(f"自动检测到链接列：{column_name}")
                    break
        
        if column_name is None:
            print("未找到包含链接的列")
            return []
        
        if column_name not in self.data.columns:
            print(f"错误：列 '{column_name}' 不存在")
            print(f"可用列：{list(self.data.columns)}")
            return []
        
        # 获取链接并过滤空值
        links = self.data[column_name].dropna().tolist()
        print(f"从列 '{column_name}' 中找到 {len(links)} 个链接")
        
        return links

    def create_product_folders(self, count):
        """
        创建商品文件夹

        Args:
            count (int): 商品数量

        Returns:
            list: 创建的文件夹路径列表
        """
        try:
            base_folder = "商品图片"

            # 确保基础文件夹存在
            if not os.path.exists(base_folder):
                os.makedirs(base_folder)
                print(f"创建基础文件夹: {base_folder}")

            created_folders = []

            for i in range(1, count + 1):
                folder_path = os.path.join(base_folder, str(i))

                if not os.path.exists(folder_path):
                    os.makedirs(folder_path)
                    print(f"创建商品文件夹: {folder_path}")
                else:
                    print(f"商品文件夹已存在: {folder_path}")

                created_folders.append(folder_path)

            print(f"成功创建/确认 {count} 个商品文件夹")
            return created_folders

        except Exception as e:
            print(f"创建商品文件夹时出错: {str(e)}")
            return []

class MTPFileManager:
    """MTP设备文件管理器"""

    def __init__(self, log_callback=None):
        self.log_callback = log_callback

    def log(self, message):
        """记录日志"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def find_mtp_device_folder(self, device_name="Redmi K60 Ultra", target_path="DCIM\\Pindd\\goods"):
        """
        查找MTP设备中的目标文件夹

        Args:
            device_name (str): 设备名称
            target_path (str): 目标路径

        Returns:
            object or None: 找到的文件夹对象
        """
        try:
            self.log("正在使用Windows Shell API查找MTP设备...")

            # 初始化COM
            pythoncom.CoInitialize()

            # 创建Shell应用对象
            shell = win32com.client.Dispatch("Shell.Application")

            # 获取"此电脑"文件夹
            computer = shell.NameSpace(17)  # 17 = ssfDRIVES (此电脑)

            # 查找设备
            device_folder = None
            for item in computer.Items():
                if device_name in item.Name:
                    self.log(f"找到设备: {item.Name}")
                    device_folder = item.GetFolder
                    break

            if device_folder is None:
                self.log(f"未找到设备: {device_name}")
                return None

            # 导航到目标路径
            current_folder = device_folder
            path_parts = target_path.split("\\")

            for part in path_parts:
                found = False
                for item in current_folder.Items():
                    if item.Name == part and item.IsFolder:
                        current_folder = item.GetFolder
                        found = True
                        self.log(f"进入文件夹: {part}")
                        break

                if not found:
                    self.log(f"未找到文件夹: {part}")
                    return None

            self.log(f"成功找到目标文件夹: {target_path}")
            return current_folder

        except Exception as e:
            self.log(f"查找MTP设备文件夹时出错: {str(e)}")
            return None
        finally:
            try:
                pythoncom.CoUninitialize()
            except:
                pass

    def detect_connected_mtp_device(self):
        """
        检测当前连接的MTP设备（包含DCIM\\Pindd\\goods路径的设备）

        Returns:
            str or None: 检测到的设备名称，如果没有找到返回None
        """
        try:
            self.log("正在检测连接的MTP设备...")

            # 初始化COM
            pythoncom.CoInitialize()

            # 创建Shell应用对象
            shell = win32com.client.Dispatch("Shell.Application")

            # 获取"此电脑"文件夹
            computer = shell.NameSpace(17)  # 17 = ssfDRIVES (此电脑)

            # 遍历所有设备
            for item in computer.Items():
                device_name = item.Name
                self.log(f"检查设备: {device_name}")

                try:
                    # 检查是否是MTP设备（通常包含手机品牌名）
                    if any(brand in device_name.lower() for brand in ['redmi', 'xiaomi', 'samsung', 'huawei', 'oppo', 'vivo', 'oneplus', 'realme', 'honor', 'meizu', 'iqoo']):
                        self.log(f"发现可能的手机设备: {device_name}")

                        # 尝试查找目标路径
                        device_folder = item.GetFolder
                        target_found = self.check_target_path_in_device(device_folder, "DCIM\\Pindd\\goods")

                        if target_found:
                            self.log(f"✅ 在设备 '{device_name}' 中找到目标路径")
                            return device_name
                        else:
                            # 尝试其他可能的路径结构
                            alt_paths = [
                                "内部存储设备\\DCIM\\Pindd\\goods",
                                "内部存储\\DCIM\\Pindd\\goods",
                                "Internal storage\\DCIM\\Pindd\\goods"
                            ]
                            for alt_path in alt_paths:
                                if self.check_target_path_in_device(device_folder, alt_path):
                                    self.log(f"✅ 在设备 '{device_name}' 中找到目标路径: {alt_path}")
                                    return device_name

                            self.log(f"⚠️ 设备 '{device_name}' 中未找到目标路径")

                except Exception as e:
                    self.log(f"检查设备 '{device_name}' 时出错: {str(e)}")
                    continue

            self.log("❌ 未找到包含目标路径的MTP设备")
            return None

        except Exception as e:
            self.log(f"检测MTP设备时出错: {str(e)}")
            return None
        finally:
            try:
                pythoncom.CoUninitialize()
            except:
                pass

    def check_target_path_in_device(self, device_folder, target_path):
        """
        检查设备中是否存在目标路径

        Args:
            device_folder: 设备文件夹对象
            target_path (str): 目标路径

        Returns:
            bool: 是否找到目标路径
        """
        try:
            current_folder = device_folder
            path_parts = target_path.split("\\")

            for part in path_parts:
                found = False
                for item in current_folder.Items():
                    if item.Name == part and item.IsFolder:
                        current_folder = item.GetFolder
                        found = True
                        break

                if not found:
                    return False

            return True

        except Exception as e:
            self.log(f"检查路径时出错: {str(e)}")
            return False

    def copy_files_from_mtp(self, source_folder, destination_path):
        """
        从MTP文件夹复制文件到本地

        Args:
            source_folder: MTP源文件夹对象
            destination_path (str): 本地目标路径

        Returns:
            list: 复制的文件列表
        """
        try:
            if source_folder is None:
                return []

            # 确保目标文件夹存在
            if not os.path.exists(destination_path):
                os.makedirs(destination_path)

            copied_files = []
            image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.webp']

            # 创建Shell应用对象用于文件操作
            shell = win32com.client.Dispatch("Shell.Application")

            # 确保路径格式正确
            abs_dest_path = os.path.abspath(destination_path)
            self.log(f"目标路径: {abs_dest_path}")

            dest_folder = shell.NameSpace(abs_dest_path)

            if dest_folder is None:
                self.log(f"无法创建目标文件夹对象: {abs_dest_path}")
                # 尝试使用标准文件复制方法
                return self.copy_files_standard_method(source_folder, abs_dest_path, image_extensions)

            for item in source_folder.Items():
                if not item.IsFolder:
                    # 检查是否是图片文件
                    file_ext = os.path.splitext(item.Name)[1].lower()
                    if file_ext in image_extensions:
                        self.log(f"复制文件: {item.Name}")

                        try:
                            # 使用Shell复制文件
                            dest_folder.CopyHere(item, 4)  # 4 = 不显示进度对话框
                            copied_files.append(item.Name)
                            self.log(f"✅ 成功复制: {item.Name}")
                        except Exception as copy_error:
                            self.log(f"复制文件失败 {item.Name}: {str(copy_error)}")
                            # 尝试标准方法复制单个文件
                            if self.copy_single_file_standard(item, abs_dest_path):
                                copied_files.append(item.Name)

            self.log(f"成功复制 {len(copied_files)} 个文件")
            return copied_files

        except Exception as e:
            self.log(f"从MTP复制文件时出错: {str(e)}")
            return []

    def copy_files_standard_method(self, source_folder, dest_path, image_extensions):
        """
        使用标准方法复制文件（备用方案）
        当Shell.NameSpace失败时使用
        """
        try:
            self.log("使用备用方法复制文件...")
            copied_files = []

            # 尝试直接遍历并复制文件
            for item in source_folder.Items():
                if not item.IsFolder:
                    file_ext = os.path.splitext(item.Name)[1].lower()
                    if file_ext in image_extensions:
                        self.log(f"尝试复制文件: {item.Name}")

                        # 构建目标文件路径
                        dest_file_path = os.path.join(dest_path, item.Name)

                        try:
                            # 尝试使用item的方法直接复制
                            # 这是一个简化的方法，可能需要更复杂的MTP操作
                            self.log(f"备用方法暂不支持直接MTP复制: {item.Name}")
                            # 在这里我们可以尝试其他方法，比如使用adb pull

                        except Exception as item_error:
                            self.log(f"备用复制失败 {item.Name}: {str(item_error)}")

            return copied_files

        except Exception as e:
            self.log(f"备用方法失败: {str(e)}")
            return []

    def copy_single_file_standard(self, item, dest_path):
        """
        复制单个文件的标准方法
        """
        try:
            self.log(f"尝试单文件复制: {item.Name}")
            # 这里可以实现更复杂的单文件复制逻辑
            return False

        except Exception as e:
            self.log(f"单文件复制失败: {str(e)}")
            return False

class AutomationController:
    """自动化控制器"""

    def __init__(self, log_callback=None):
        """
        初始化自动化控制器

        Args:
            log_callback: 日志回调函数
        """
        self.log_callback = log_callback
        self.image_folder = "image"
        self.goods_folder = "商品图片"
        self.current_product_index = 1  # 当前处理的商品索引

        # 设备品牌检测相关
        self.current_device_name = None
        self.current_device_brand = None

        # 创建MTP文件管理器
        self.mtp_manager = MTPFileManager(log_callback=log_callback)

        # 创建智能OCR分析器
        self.ocr_analyzer = SmartOCRAnalyzer(log_callback=log_callback)

        # ADB相关
        self.adb_cmd = None
        self.adb_ready = False

        # 设置pyautogui参数
        pyautogui.FAILSAFE = True  # 鼠标移到左上角停止
        pyautogui.PAUSE = 0.2  # 每次操作间隔0.2秒

        # 暂停控制器引用（由外部设置）
        self.pause_controller = None

    def log(self, message):
        """记录日志"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def detect_device_brand(self):
        """
        检测连接设备的品牌并更新图片文件夹路径

        Returns:
            str: 检测到的设备品牌，如果检测失败返回None
        """
        try:
            self.log("🔍 开始检测连接设备品牌...")

            # 使用MTP管理器检测设备
            device_name = self.mtp_manager.detect_connected_mtp_device()

            if device_name:
                self.current_device_name = device_name
                self.log(f"检测到设备: {device_name}")

                # 检测设备品牌
                device_name_lower = device_name.lower()

                if 'iqoo' in device_name_lower:
                    self.current_device_brand = 'iqoo'
                    self.image_folder = os.path.join("image", "IQOO")
                    self.log(f"✅ 检测到IQOO品牌设备，使用图片文件夹: {self.image_folder}")
                else:
                    # 其他品牌使用默认图片文件夹
                    self.current_device_brand = 'other'
                    self.image_folder = "image"
                    self.log(f"✅ 检测到其他品牌设备，使用默认图片文件夹: {self.image_folder}")

                # 检查图片文件夹是否存在
                if not os.path.exists(self.image_folder):
                    self.log(f"⚠️ 警告：图片文件夹 {self.image_folder} 不存在")
                    return None

                return self.current_device_brand
            else:
                self.log("❌ 未检测到连接的MTP设备")
                return None

        except Exception as e:
            self.log(f"❌ 检测设备品牌时出错: {str(e)}")
            return None

    def get_image_path(self, image_name):
        """
        根据当前设备品牌获取图片文件的完整路径

        Args:
            image_name (str): 图片文件名

        Returns:
            str: 图片文件的完整路径
        """
        return os.path.join(self.image_folder, image_name)

    def find_and_click_image(self, image_name, click_position="center", confidence=0.8):
        """
        查找图片并点击

        Args:
            image_name (str): 图片文件名
            click_position (str): 点击位置 ("center", "left", "right", "top", "bottom")
            confidence (float): 匹配置信度

        Returns:
            bool: 是否找到并点击成功
        """
        try:
            image_path = self.get_image_path(image_name)

            if not os.path.exists(image_path):
                self.log(f"错误：图片文件 {image_path} 不存在")
                return False

            self.log(f"正在查找图片: {image_name} (路径: {image_path})")

            # 查找图片
            location = pyautogui.locateOnScreen(image_path, confidence=confidence)

            if location is None:
                self.log(f"未找到图片: {image_name}")
                return False

            # 计算点击位置
            if click_position == "center":
                click_x, click_y = pyautogui.center(location)
            elif click_position == "left":
                click_x = location.left
                click_y = location.top + location.height // 2
            elif click_position == "right":
                click_x = location.left + location.width
                click_y = location.top + location.height // 2
            elif click_position == "top":
                click_x = location.left + location.width // 2
                click_y = location.top
            elif click_position == "bottom":
                click_x = location.left + location.width // 2
                click_y = location.top + location.height
            else:
                click_x, click_y = pyautogui.center(location)

            self.log(f"找到图片 {image_name}，位置: ({location.left}, {location.top})")
            self.log(f"点击位置: ({click_x}, {click_y})")

            # 点击
            pyautogui.click(click_x, click_y)
            self.log(f"已点击图片: {image_name}")

            return True

        except Exception as e:
            self.log(f"查找或点击图片 {image_name} 时出错: {str(e)}")
            return False

    def find_and_long_press_image(self, image_name, click_position="center", hold_duration=0.5, confidence=0.8):
        """
        查找图片并长按

        Args:
            image_name (str): 图片文件名
            click_position (str): 点击位置 ("center", "left", "right", "top", "bottom")
            hold_duration (float): 长按持续时间（秒）
            confidence (float): 匹配置信度

        Returns:
            bool: 是否找到并长按成功
        """
        try:
            image_path = self.get_image_path(image_name)

            if not os.path.exists(image_path):
                self.log(f"错误：图片文件 {image_path} 不存在")
                return False

            self.log(f"正在查找图片: {image_name} (路径: {image_path})")

            # 查找图片
            location = pyautogui.locateOnScreen(image_path, confidence=confidence)

            if location is None:
                self.log(f"未找到图片: {image_name}")
                return False

            # 计算点击位置
            if click_position == "center":
                click_x, click_y = pyautogui.center(location)
            elif click_position == "left":
                click_x = location.left
                click_y = location.top + location.height // 2
            elif click_position == "right":
                click_x = location.left + location.width
                click_y = location.top + location.height // 2
            elif click_position == "top":
                click_x = location.left + location.width // 2
                click_y = location.top
            elif click_position == "bottom":
                click_x = location.left + location.width // 2
                click_y = location.top + location.height
            else:
                click_x, click_y = pyautogui.center(location)

            self.log(f"找到图片 {image_name}，位置: ({location.left}, {location.top})")
            self.log(f"长按位置: ({click_x}, {click_y})，持续时间: {hold_duration}秒")

            # 长按操作
            pyautogui.mouseDown(click_x, click_y)
            time.sleep(hold_duration)
            pyautogui.mouseUp()

            self.log(f"已长按图片: {image_name}")

            return True

        except Exception as e:
            self.log(f"查找或长按图片 {image_name} 时出错: {str(e)}")
            return False

    def paste_text(self, text):
        """
        粘贴文本

        Args:
            text (str): 要粘贴的文本
        """
        try:
            # 将文本复制到剪贴板
            pyperclip.copy(text)
            self.log(f"已复制文本到剪贴板: {text[:50]}...")

            # 等待剪贴板操作完成
            time.sleep(0.8)

            # 粘贴
            pyautogui.hotkey('ctrl', 'v')
            self.log("已粘贴文本")

            # 等待粘贴操作完成
            time.sleep(0.5)

        except Exception as e:
            self.log(f"粘贴文本时出错: {str(e)}")

    def input_text_for_escrcpy(self, text):
        """
        专门用于escrcpy投屏的文本输入方法
        针对escrcpy的特殊处理

        Args:
            text (str): 要输入的文本
        """
        try:
            self.log(f"开始为escrcpy输入文本: {text[:50]}...")

            # 方法1：使用剪贴板 + MOD+v (escrcpy特有的粘贴方式)
            self.log("尝试escrcpy剪贴板同步方法...")

            # 复制到剪贴板
            pyperclip.copy(text)
            time.sleep(0.5)

            # 先清空输入框
            self.log("清空输入框...")
            pyautogui.hotkey('ctrl', 'a')  # 全选
            time.sleep(0.3)

            # 使用标准Ctrl+V粘贴（escrcpy应该支持剪贴板同步）
            self.log("执行Ctrl+V粘贴...")
            pyautogui.hotkey('ctrl', 'v')
            time.sleep(1.5)

            # 验证是否成功，如果失败则使用备用方法
            self.log("粘贴完成，如果失败将使用备用方法")

        except Exception as e:
            self.log(f"escrcpy剪贴板方法失败: {str(e)}")

            # 备用方法1：使用ADB直接输入（如果可用）
            try:
                self.log("尝试ADB直接输入方法...")
                self.input_text_via_adb(text)
            except Exception as e2:
                self.log(f"ADB方法也失败: {str(e2)}")

                # 备用方法2：模拟物理键盘输入
                try:
                    self.log("尝试模拟物理键盘输入...")
                    self.simulate_physical_keyboard_input(text)
                except Exception as e3:
                    self.log(f"所有输入方法都失败: {str(e3)}")

    def input_text_via_adb(self, text):
        """
        使用ADB直接输入文本到Android设备

        Args:
            text (str): 要输入的文本
        """
        try:
            import subprocess

            # 转义特殊字符
            escaped_text = text.replace('"', '\\"').replace('\\', '\\\\')

            # 使用adb shell input text命令
            cmd = f'adb shell input text "{escaped_text}"'
            self.log(f"执行ADB命令: {cmd}")

            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                self.log("ADB文本输入成功")
                time.sleep(1.0)
            else:
                self.log(f"ADB命令失败: {result.stderr}")
                raise Exception("ADB命令执行失败")

        except Exception as e:
            self.log(f"ADB输入失败: {str(e)}")
            raise

    def simulate_physical_keyboard_input(self, text):
        """
        模拟物理键盘输入（针对escrcpy的UHID模式）

        Args:
            text (str): 要输入的文本
        """
        try:
            self.log("模拟物理键盘输入...")

            # 确保输入框获得焦点
            time.sleep(0.5)

            # 清空现有内容
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.2)
            pyautogui.press('delete')
            time.sleep(0.5)

            # 逐字符输入，使用更长的间隔
            for char in text:
                pyautogui.write(char)
                time.sleep(0.05)  # 每个字符间隔50ms

            self.log("物理键盘模拟输入完成")
            time.sleep(1.0)

        except Exception as e:
            self.log(f"物理键盘模拟失败: {str(e)}")
            raise

    def send_real_keyboard_input(self, text):
        """
        使用Win32 API发送真实的键盘输入
        完全模拟物理键盘按键

        Args:
            text (str): 要输入的文本
        """
        try:
            self.log("使用Win32 API模拟真实键盘输入...")

            # 先复制文本到剪贴板
            pyperclip.copy(text)
            self.log(f"已复制到剪贴板: {text[:50]}...")
            time.sleep(0.8)

            # 使用Win32 API发送Ctrl+A (全选)
            self.log("发送Ctrl+A全选...")
            self.send_key_combination([win32con.VK_CONTROL, ord('A')])
            time.sleep(0.5)

            # 使用Win32 API发送Ctrl+V (粘贴)
            self.log("发送Ctrl+V粘贴...")
            self.send_key_combination([win32con.VK_CONTROL, ord('V')])
            time.sleep(1.0)

            self.log("Win32 API键盘输入完成")

        except Exception as e:
            self.log(f"Win32 API键盘输入失败: {str(e)}")
            raise

    def send_key_combination(self, keys):
        """
        发送组合键

        Args:
            keys (list): 按键列表，如 [VK_CONTROL, ord('V')]
        """
        try:
            # 按下所有键
            for key in keys:
                win32api.keybd_event(key, 0, 0, 0)
                time.sleep(0.01)

            # 释放所有键（逆序）
            for key in reversed(keys):
                win32api.keybd_event(key, 0, win32con.KEYEVENTF_KEYUP, 0)
                time.sleep(0.01)

        except Exception as e:
            self.log(f"发送组合键失败: {str(e)}")
            raise

    def send_single_key(self, key_code):
        """
        发送单个按键

        Args:
            key_code: 按键代码
        """
        try:
            win32api.keybd_event(key_code, 0, 0, 0)  # 按下
            time.sleep(0.01)
            win32api.keybd_event(key_code, 0, win32con.KEYEVENTF_KEYUP, 0)  # 释放
        except Exception as e:
            self.log(f"发送单个按键失败: {str(e)}")
            raise

    def send_input_api_keyboard(self, text):
        """
        使用Windows SendInput API发送最底层的键盘输入
        这是Windows最底层的输入方式

        Args:
            text (str): 要输入的文本
        """
        try:
            self.log("使用SendInput API发送底层键盘输入...")

            # 定义Windows API结构
            user32 = ctypes.windll.user32
            kernel32 = ctypes.windll.kernel32

            # 先复制文本到剪贴板
            pyperclip.copy(text)
            self.log(f"已复制到剪贴板: {text[:50]}...")
            time.sleep(1.0)

            # 发送Ctrl+A (全选)
            self.log("发送Ctrl+A全选...")
            self.send_input_key_combination([0x11, 0x41])  # VK_CONTROL, VK_A
            time.sleep(0.8)

            # 发送Ctrl+V (粘贴)
            self.log("发送Ctrl+V粘贴...")
            self.send_input_key_combination([0x11, 0x56])  # VK_CONTROL, VK_V
            time.sleep(1.5)

            self.log("SendInput API键盘输入完成")

        except Exception as e:
            self.log(f"SendInput API键盘输入失败: {str(e)}")
            # 备用方法：直接逐字符输入
            try:
                self.log("尝试备用方法：逐字符SendInput输入...")
                self.send_input_text_direct(text)
            except Exception as e2:
                self.log(f"所有SendInput方法都失败: {str(e2)}")
                raise

    def send_input_key_combination(self, keys):
        """
        使用SendInput API发送组合键

        Args:
            keys (list): 虚拟键码列表
        """
        try:
            # 简化的方法：使用ctypes直接调用
            user32 = ctypes.windll.user32

            # 按下所有键
            for key in keys:
                user32.keybd_event(key, 0, 0, 0)
                time.sleep(0.01)

            # 释放所有键
            for key in reversed(keys):
                user32.keybd_event(key, 0, 2, 0)  # KEYEVENTF_KEYUP = 2
                time.sleep(0.01)

            self.log(f"成功发送组合键: {keys}")

        except Exception as e:
            self.log(f"SendInput组合键失败: {str(e)}")
            raise

    def send_input_text_direct(self, text):
        """
        使用SendInput API直接输入文本（逐字符）

        Args:
            text (str): 要输入的文本
        """
        try:
            self.log("使用SendInput API逐字符输入...")

            # 先清空输入框
            self.send_input_key_combination([0x11, 0x41])  # Ctrl+A
            time.sleep(0.3)
            self.send_input_single_key(0x2E)  # Delete
            time.sleep(0.5)

            # 逐字符输入
            for i, char in enumerate(text):
                if i % 10 == 0:  # 每10个字符显示一次进度
                    self.log(f"输入进度: {i}/{len(text)}")

                # 获取字符的虚拟键码
                vk_code = ctypes.windll.user32.VkKeyScanW(ord(char))
                if vk_code != -1:
                    self.send_input_single_key(vk_code & 0xFF)
                    time.sleep(0.02)  # 每个字符间隔20ms

            self.log("逐字符输入完成")

        except Exception as e:
            self.log(f"SendInput逐字符输入失败: {str(e)}")
            raise

    def send_input_single_key(self, key_code):
        """
        发送单个按键

        Args:
            key_code: 虚拟键码
        """
        try:
            user32 = ctypes.windll.user32

            # 按下
            user32.keybd_event(key_code, 0, 0, 0)
            time.sleep(0.01)

            # 释放
            user32.keybd_event(key_code, 0, 2, 0)  # KEYEVENTF_KEYUP = 2
            time.sleep(0.01)

        except Exception as e:
            self.log(f"发送单个按键失败: {str(e)}")
            raise

    def input_text_via_window_message(self, text):
        """
        使用Windows消息直接向投屏软件窗口发送文本
        这种方法可能更有效，因为它直接与窗口通信

        Args:
            text (str): 要输入的文本

        Returns:
            bool: 输入是否成功
        """
        try:
            self.log("使用Windows消息向投屏软件发送文本...")

            # 查找投屏软件窗口
            screen_window = self.find_screen_mirroring_window()
            if not screen_window:
                self.log("未找到投屏软件窗口")
                return False

            # 将文本复制到剪贴板
            self.set_clipboard_text(text)

            # 发送Ctrl+A (全选)
            self.send_key_to_window(screen_window, win32con.VK_CONTROL, ord('A'))
            time.sleep(0.5)

            # 发送Ctrl+V (粘贴)
            self.send_key_to_window(screen_window, win32con.VK_CONTROL, ord('V'))
            time.sleep(1.0)

            self.log("✅ 窗口消息发送完成")
            return True

        except Exception as e:
            self.log(f"窗口消息发送失败: {str(e)}")
            return False

    def find_screen_mirroring_window(self):
        """
        查找投屏软件窗口

        Returns:
            int: 窗口句柄，如果未找到返回None
        """
        try:
            # 常见的投屏软件窗口标题关键词
            screen_apps = [
                'escrcpy',
                'scrcpy',
                'Escrcpy',
                'Screen Mirror',
                'Android',
            ]

            # 如果检测到了设备名称，也加入关键词列表
            if self.current_device_name:
                screen_mirror_keywords.append(self.current_device_name)

            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_title = win32gui.GetWindowText(hwnd)
                    if window_title:
                        windows.append((hwnd, window_title))
                return True

            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)

            # 查找匹配的窗口
            for hwnd, title in windows:
                for app_name in screen_apps:
                    if app_name.lower() in title.lower():
                        self.log(f"找到投屏软件窗口: {title}")
                        return hwnd

            # 如果没找到特定的，尝试找当前活动窗口
            active_window = win32gui.GetForegroundWindow()
            if active_window:
                title = win32gui.GetWindowText(active_window)
                self.log(f"使用当前活动窗口: {title}")
                return active_window

            return None

        except Exception as e:
            self.log(f"查找窗口失败: {str(e)}")
            return None

    def set_clipboard_text(self, text):
        """
        设置剪贴板文本

        Args:
            text (str): 要设置的文本
        """
        try:
            win32clipboard.OpenClipboard()
            win32clipboard.EmptyClipboard()
            win32clipboard.SetClipboardText(text)
            win32clipboard.CloseClipboard()
            self.log(f"已设置剪贴板: {text[:50]}...")
        except Exception as e:
            self.log(f"设置剪贴板失败: {str(e)}")

    def send_key_to_window(self, hwnd, modifier_key, key):
        """
        向指定窗口发送组合键

        Args:
            hwnd: 窗口句柄
            modifier_key: 修饰键 (如VK_CONTROL)
            key: 主键
        """
        try:
            # 确保窗口获得焦点
            win32gui.SetForegroundWindow(hwnd)
            time.sleep(0.1)

            # 发送按键消息
            WM_KEYDOWN = 0x0100
            WM_KEYUP = 0x0101

            # 按下修饰键
            win32gui.SendMessage(hwnd, WM_KEYDOWN, modifier_key, 0)
            time.sleep(0.05)

            # 按下主键
            win32gui.SendMessage(hwnd, WM_KEYDOWN, key, 0)
            time.sleep(0.05)

            # 释放主键
            win32gui.SendMessage(hwnd, WM_KEYUP, key, 0)
            time.sleep(0.05)

            # 释放修饰键
            win32gui.SendMessage(hwnd, WM_KEYUP, modifier_key, 0)

        except Exception as e:
            self.log(f"发送按键到窗口失败: {str(e)}")

    def simulate_real_ctrl_v(self):
        """
        使用外部脚本软件执行真正的Ctrl+V

        Returns:
            bool: 操作是否成功
        """
        try:
            self.log("使用外部脚本软件执行真正的Ctrl+V...")
            self.log("Python的键盘模拟无法被escrcpy识别，尝试其他方案")

            import subprocess

            # 方法1: AutoHotkey (最推荐)
            self.log("方法1: 尝试AutoHotkey...")
            try:
                # 检查AutoHotkey脚本文件是否存在
                script_files = ["paste_v2.ahk", "paste_text.ahk"]
                script_file = None

                for file in script_files:
                    if os.path.exists(file):
                        script_file = file
                        self.log(f"找到脚本文件: {file}")
                        break

                if not script_file:
                    self.log("AutoHotkey脚本文件不存在")
                else:
                    # 检查AutoHotkey是否可用
                    ahk_paths = [
                        "AutoHotkey.exe",
                        "C:\\Program Files\\AutoHotkey\\AutoHotkey.exe",
                        "C:\\Program Files (x86)\\AutoHotkey\\AutoHotkey.exe",
                        "C:\\Program Files\\AutoHotkey\\v2\\AutoHotkey.exe",
                        "C:\\Program Files (x86)\\AutoHotkey\\v2\\AutoHotkey.exe"
                    ]

                    ahk_exe = None
                    for path in ahk_paths:
                        self.log(f"检查路径: {path}")
                        if os.path.exists(path):
                            ahk_exe = path
                            self.log(f"找到AutoHotkey: {ahk_exe}")
                            break

                    if ahk_exe:
                        self.log(f"执行AutoHotkey脚本: {script_file}")
                        try:
                            result = subprocess.run([ahk_exe, script_file],
                                                  capture_output=True, text=True, timeout=3)

                            self.log(f"AutoHotkey返回码: {result.returncode}")
                            if result.stdout:
                                self.log(f"AutoHotkey输出: {result.stdout}")
                            if result.stderr:
                                self.log(f"AutoHotkey错误: {result.stderr}")

                            if result.returncode == 0:
                                self.log("✅ AutoHotkey执行成功")
                                return True
                            else:
                                self.log("AutoHotkey执行失败")

                        except subprocess.TimeoutExpired:
                            self.log("AutoHotkey脚本执行超时，可能是版本兼容问题")
                            self.log("尝试直接执行AutoHotkey命令...")

                            # 尝试直接执行命令而不是脚本文件
                            try:
                                result = subprocess.run([
                                    ahk_exe,
                                    "-c",
                                    "Sleep 500\nSend ^v\nSleep 1000\nExitApp"
                                ], capture_output=True, text=True, timeout=3)

                                if result.returncode == 0:
                                    self.log("✅ AutoHotkey直接命令执行成功")
                                    return True
                                else:
                                    self.log("AutoHotkey直接命令也失败")

                            except Exception as e2:
                                self.log(f"AutoHotkey直接命令失败: {str(e2)}")
                    else:
                        self.log("未找到AutoHotkey可执行文件")
                        self.log("请确保AutoHotkey已正确安装")

            except Exception as e:
                self.log(f"AutoHotkey方法失败: {str(e)}")

            # 方法2: 最后的备用方案 - 提示用户手动操作
            self.log("AutoHotkey不可用")
            self.log("请手动按下Ctrl+V来粘贴链接")
            self.log("程序将等待5秒供您手动操作...")

            for i in range(5, 0, -1):
                self.log(f"请在 {i} 秒内手动按下Ctrl+V...")
                time.sleep(1)

            self.log("手动操作时间结束，继续执行...")
            return True

        except Exception as e:
            self.log(f"执行Ctrl+V失败: {str(e)}")
            return False

    def execute_autohotkey_script(self, script_name):
        """
        执行指定的AutoHotkey脚本

        Args:
            script_name (str): 脚本名称（不含.ahk扩展名）

        Returns:
            bool: 执行是否成功
        """
        try:
            import subprocess

            script_file = f"{script_name}.ahk"

            # 检查脚本文件是否存在
            if not os.path.exists(script_file):
                self.log(f"AutoHotkey脚本文件不存在: {script_file}")
                return False

            # 查找AutoHotkey可执行文件
            ahk_paths = [
                "C:\\Program Files\\AutoHotkey\\v2\\AutoHotkey.exe",
                "C:\\Program Files\\AutoHotkey\\AutoHotkey.exe",
                "C:\\Program Files (x86)\\AutoHotkey\\AutoHotkey.exe",
                "AutoHotkey.exe"
            ]

            ahk_exe = None
            for path in ahk_paths:
                if os.path.exists(path):
                    ahk_exe = path
                    break

            if not ahk_exe:
                self.log("未找到AutoHotkey可执行文件")
                return False

            # 执行脚本
            self.log(f"执行AutoHotkey脚本: {script_file}")
            result = subprocess.run([ahk_exe, script_file],
                                  capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                self.log(f"✅ AutoHotkey脚本执行成功: {script_name}")
                return True
            else:
                self.log(f"❌ AutoHotkey脚本执行失败: {result.stderr}")
                return False

        except Exception as e:
            self.log(f"执行AutoHotkey脚本失败: {str(e)}")
            return False

    def input_text_via_scrcpy_command(self, text):
        """
        使用scrcpy命令行直接输入文本
        这是最直接的方法，不会干扰投屏连接

        Args:
            text (str): 要输入的文本

        Returns:
            bool: 输入是否成功
        """
        try:
            import subprocess

            self.log("使用scrcpy命令行输入文本...")

            # scrcpy路径
            scrcpy_path = r"F:\Escrcpy-1.16.3\resources\extra\win\scrcpy\scrcpy.exe"

            # 检查scrcpy是否存在
            if not os.path.exists(scrcpy_path):
                self.log(f"scrcpy不存在: {scrcpy_path}")
                return False

            # 方法1: 使用scrcpy的--push-target参数（如果支持）
            # 先尝试清空输入框
            self.log("清空输入框...")

            # 全选
            clear_cmd = [scrcpy_path, "--no-display", "--turn-screen-off",
                        "--stay-awake", "--no-audio", "--no-video",
                        "--keyboard=uhid", "--mouse=uhid"]

            # 实际上，让我们使用更简单的方法：直接调用adb但通过scrcpy的adb
            adb_path = r"F:\Escrcpy-1.16.3\resources\extra\win\scrcpy\adb.exe"

            if os.path.exists(adb_path):
                self.log(f"使用scrcpy自带的adb: {adb_path}")

                # 清空输入框
                subprocess.run([adb_path, 'shell', 'input', 'keyevent', 'KEYCODE_CTRL_A'],
                             capture_output=True, timeout=5)
                time.sleep(0.3)

                subprocess.run([adb_path, 'shell', 'input', 'keyevent', 'KEYCODE_DEL'],
                             capture_output=True, timeout=5)
                time.sleep(0.5)

                # 输入文本
                self.log(f"输入文本: {text[:50]}...")
                result = subprocess.run([adb_path, 'shell', 'input', 'text', text],
                                      capture_output=True, text=True, timeout=10)

                if result.returncode == 0:
                    self.log("✅ scrcpy ADB文本输入成功")
                    return True
                else:
                    self.log(f"scrcpy ADB输入失败: {result.stderr}")
                    return False
            else:
                self.log("未找到scrcpy自带的adb")
                return False

        except Exception as e:
            self.log(f"scrcpy命令行输入失败: {str(e)}")
            return False

    def initialize_adb(self):
        """
        初始化ADB连接
        在程序启动时调用，避免后续操作时断开投屏

        Returns:
            bool: 初始化是否成功
        """
        try:
            import subprocess

            self.log("正在初始化ADB连接...")

            # 检查ADB是否可用
            adb_paths = [
                './adb.exe',  # 当前目录下的ADB（优先）
                'adb.exe',  # Windows下的ADB
                'adb',  # 系统PATH中的ADB
                './platform-tools/adb.exe',  # platform-tools文件夹中的ADB
            ]

            for adb_path in adb_paths:
                try:
                    result = subprocess.run([adb_path, 'version'], capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        self.adb_cmd = adb_path
                        self.log(f"找到ADB: {adb_path}")
                        break
                except:
                    continue

            if self.adb_cmd is None:
                self.log("❌ ADB不可用，请确保adb.exe在程序目录中")
                return False

            # 检查设备连接
            result = subprocess.run([self.adb_cmd, 'devices'], capture_output=True, text=True, timeout=5)
            self.log(f"ADB设备列表: {result.stdout.strip()}")

            if 'device' not in result.stdout:
                self.log("⚠️  未检测到ADB设备连接")
                self.log("请确保:")
                self.log("1. 手机已连接USB")
                self.log("2. 开启了开发者选项")
                self.log("3. 开启了USB调试")
                self.log("4. 允许了USB调试授权")
                return False

            self.adb_ready = True
            self.log("✅ ADB初始化成功，设备连接正常")
            self.log("现在可以安全地连接投屏软件了")
            return True

        except Exception as e:
            self.log(f"ADB初始化失败: {str(e)}")
            return False

    def input_text_via_adb_direct(self, text):
        """
        使用已初始化的ADB直接向手机输入文本

        Args:
            text (str): 要输入的文本

        Returns:
            bool: 输入是否成功
        """
        try:
            import subprocess

            if not self.adb_ready or self.adb_cmd is None:
                self.log("❌ ADB未初始化，请先初始化ADB连接")
                return False

            self.log("使用ADB直接输入文本...")

            # 清空输入框
            self.log("清空当前输入框...")

            # 全选
            result = subprocess.run([self.adb_cmd, 'shell', 'input', 'keyevent', 'KEYCODE_CTRL_A'],
                                  capture_output=True, text=True, timeout=5)
            time.sleep(0.3)

            # 删除
            result = subprocess.run([self.adb_cmd, 'shell', 'input', 'keyevent', 'KEYCODE_DEL'],
                                  capture_output=True, text=True, timeout=5)
            time.sleep(0.5)

            # 输入文本
            self.log(f"输入商品链接: {text[:50]}...")

            result = subprocess.run([self.adb_cmd, 'shell', 'input', 'text', text],
                                  capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                self.log("✅ ADB文本输入成功")
                time.sleep(1.0)
                return True
            else:
                self.log(f"❌ ADB输入失败: {result.stderr}")
                return False

        except Exception as e:
            self.log(f"ADB输入失败: {str(e)}")
            return False

    def escape_text_for_adb(self, text):
        """
        为ADB命令转义文本

        Args:
            text (str): 原始文本

        Returns:
            str: 转义后的文本
        """
        # ADB input text需要转义的字符
        # 空格需要用%s替代，特殊字符需要转义
        escaped = text.replace(' ', '%s')  # 空格转义
        escaped = escaped.replace('&', '\\&')  # &符号转义
        escaped = escaped.replace('(', '\\(')  # 括号转义
        escaped = escaped.replace(')', '\\)')
        escaped = escaped.replace(';', '\\;')  # 分号转义
        escaped = escaped.replace('|', '\\|')  # 管道符转义
        escaped = escaped.replace('<', '\\<')  # 小于号转义
        escaped = escaped.replace('>', '\\>')  # 大于号转义
        escaped = escaped.replace('`', '\\`')  # 反引号转义
        escaped = escaped.replace('$', '\\$')  # 美元符转义
        escaped = escaped.replace('"', '\\"')  # 双引号转义
        escaped = escaped.replace("'", "\\'")  # 单引号转义

        return escaped

    def check_adb_connection(self):
        """
        检查ADB连接状态

        Returns:
            bool: ADB是否可用且有设备连接
        """
        try:
            import subprocess

            # 检查ADB命令是否可用
            result = subprocess.run(['adb', 'version'], capture_output=True, text=True, timeout=5)
            if result.returncode != 0:
                return False

            # 检查设备连接
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=5)
            if result.returncode != 0:
                return False

            # 检查是否有设备在线
            lines = result.stdout.strip().split('\n')
            for line in lines[1:]:  # 跳过标题行
                if 'device' in line and 'offline' not in line:
                    return True

            return False

        except Exception:
            return False

    def find_phone_path_dynamically(self):
        """
        动态查找手机路径

        Returns:
            str or None: 找到的手机路径，如果没找到返回None
        """
        try:
            import os
            import subprocess

            # 方法1：检查所有可能的驱动器
            self.log("正在检查所有可能的驱动器...")
            for drive in "FGHIJKLMNOPQRSTUVWXYZ":
                possible_path = f"{drive}:\\DCIM\\Pindd\\goods"
                if os.path.exists(possible_path):
                    self.log(f"找到手机路径（驱动器）: {possible_path}")
                    return possible_path

            # 方法2：使用wmic命令查找可移动设备
            try:
                self.log("使用wmic命令查找可移动设备...")
                result = subprocess.run(['wmic', 'logicaldisk', 'where', 'drivetype=2', 'get', 'deviceid'],
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:  # 跳过标题行
                        drive = line.strip()
                        if drive and len(drive) == 2 and drive.endswith(':'):
                            possible_path = f"{drive}\\DCIM\\Pindd\\goods"
                            if os.path.exists(possible_path):
                                self.log(f"找到手机路径（wmic）: {possible_path}")
                                return possible_path
            except Exception as e:
                self.log(f"wmic命令执行失败: {str(e)}")

            return None

        except Exception as e:
            self.log(f"动态查找手机路径时出错: {str(e)}")
            return None

    def click_and_hold(self, x, y, hold_duration=0.5):
        """
        在指定位置点击并长按鼠标

        Args:
            x (int): X坐标
            y (int): Y坐标
            hold_duration (float): 长按持续时间（秒）
        """
        try:
            # 点击鼠标左键一次
            self.log("点击鼠标左键一次")
            pyautogui.click()
            time.sleep(0.5)  # 停顿0.5秒

            # 在指定位置长按鼠标左键
            self.log(f"在位置({x}, {y})长按鼠标左键{hold_duration}秒")
            pyautogui.moveTo(x, y)
            pyautogui.mouseDown()
            time.sleep(hold_duration)
            pyautogui.mouseUp()

            self.log("鼠标操作完成")

        except Exception as e:
            self.log(f"鼠标操作时出错: {str(e)}")

    def perform_right_to_left_swipe(self):
        """
        执行从右往左的滑动操作
        从位置(630, 185)按住左键滑动到(284, 185)
        """
        try:
            self.log("开始执行从右往左的滑动操作...")

            # 滑动参数（根据测试成功的参数）
            start_x, start_y = 630, 185
            end_x, end_y = 284, 185
            duration = 0.3  # 总时长0.3秒

            self.log(f"📍 起始位置: ({start_x}, {start_y})")
            self.log(f"📍 结束位置: ({end_x}, {end_y})")
            self.log(f"📏 滑动距离: {start_x - end_x} 像素")
            self.log(f"⏱️ 滑动时长: {duration} 秒")

            # 移动到起始位置
            self.log(f"移动到起始位置({start_x}, {start_y})")
            pyautogui.moveTo(start_x, start_y)
            time.sleep(0.2)  # 短暂停顿

            # 按住左键并拖拽到结束位置
            self.log(f"按住左键从({start_x}, {start_y})拖拽到({end_x}, {end_y})")
            pyautogui.mouseDown()  # 按下左键
            time.sleep(0.3)  # 短暂停顿确保按键生效

            # 平滑拖拽到目标位置
            self.log(f"开始滑动，时长{duration}秒...")
            pyautogui.dragTo(end_x, end_y, duration=duration)

            # 释放左键
            pyautogui.mouseUp()
            self.log("释放左键，滑动操作完成")

            # 等待一下让页面响应
            time.sleep(0.5)

            self.log("✅ 从右往左的滑动操作完成")

        except Exception as e:
            self.log(f"❌ 滑动操作失败: {str(e)}")

    def find_and_click_image_with_delay(self, image_name, delay=0.5):
        """
        查找图片并在移动到图片上等待指定时间后点击

        Args:
            image_name (str): 图片文件名
            delay (float): 移动到图片后等待的时间（秒）

        Returns:
            bool: 是否成功找到并点击图片
        """
        try:
            # 构建图片完整路径
            image_path = self.get_image_path(image_name)

            if not os.path.exists(image_path):
                self.log(f"错误：图片文件 {image_path} 不存在")
                return False

            self.log(f"正在查找图片: {image_name} (路径: {image_path})")

            # 查找图片
            location = pyautogui.locateOnScreen(image_path, confidence=0.8)
            if location is not None:
                # 计算图片中心点
                center_x = location.left + location.width // 2
                center_y = location.top + location.height // 2

                self.log(f"找到图片 {image_name}，位置: ({center_x}, {center_y})")

                # 移动到图片上
                pyautogui.moveTo(center_x, center_y)
                self.log(f"移动到图片上，等待{delay}秒...")
                time.sleep(delay)

                # 点击
                pyautogui.click()
                self.log(f"已点击图片: {image_name}")
                return True
            else:
                self.log(f"未找到图片: {image_name}")
                return False

        except Exception as e:
            self.log(f"查找或点击图片 {image_name} 时出错: {str(e)}")
            return False

    def find_and_click_image_with_retry(self, image_name, delay=0.5, max_retries=3, retry_delay=5):
        """
        查找图片并点击，支持重试机制

        Args:
            image_name (str): 图片文件名
            delay (float): 移动到图片后等待的时间（秒）
            max_retries (int): 最大重试次数
            retry_delay (float): 重试间隔时间（秒）

        Returns:
            bool: 是否成功找到并点击图片
        """
        for attempt in range(max_retries):
            self.log(f"尝试查找图片 {image_name} (第{attempt + 1}次/共{max_retries}次)")

            if self.find_and_click_image_with_delay(image_name, delay):
                return True

            if attempt < max_retries - 1:  # 不是最后一次尝试
                self.log(f"未找到图片 {image_name}，等待{retry_delay}秒后重试...")
                time.sleep(retry_delay)
            else:
                self.log(f"已达到最大重试次数，放弃查找图片 {image_name}")

        return False

    def wait_for_image(self, image_name, max_attempts=20, check_interval=0.2, confidence=0.8):
        """
        等待图片出现

        Args:
            image_name (str): 图片文件名
            max_attempts (int): 最大检测次数
            check_interval (float): 检测间隔（秒）
            confidence (float): 匹配置信度

        Returns:
            bool: 是否找到图片
        """
        try:
            image_path = self.get_image_path(image_name)

            if not os.path.exists(image_path):
                self.log(f"错误：图片文件 {image_path} 不存在")
                return False

            self.log(f"开始检测图片: {image_name} (路径: {image_path})")
            self.log(f"最多检测 {max_attempts} 次，每隔 {check_interval} 秒检测一次")

            for attempt in range(1, max_attempts + 1):
                self.log(f"第 {attempt}/{max_attempts} 次检测...")

                try:
                    location = pyautogui.locateOnScreen(image_path, confidence=confidence)

                    if location is not None:
                        self.log(f"✅ 找到图片 {image_name}！位置: ({location.left}, {location.top})")
                        return True

                except pyautogui.ImageNotFoundException:
                    # 图片未找到，继续下一次检测
                    pass
                except Exception as e:
                    self.log(f"检测过程中出现异常: {str(e)}")

                if attempt < max_attempts:
                    time.sleep(check_interval)

            self.log(f"❌ 检测完成，未找到图片: {image_name}")
            return False

        except Exception as e:
            self.log(f"检测图片 {image_name} 时出错: {str(e)}")
            return False

    def move_phone_images_to_product_folder(self):
        """
        从手机MTP设备复制图片到当前商品文件夹

        Returns:
            bool: 复制是否成功
        """
        try:
            self.log(f"开始从MTP设备复制图片到商品 {self.current_product_index} 文件夹...")

            # 确定目标文件夹
            target_folder = os.path.join(self.goods_folder, str(self.current_product_index))

            # 确保目标文件夹存在
            if not os.path.exists(target_folder):
                os.makedirs(target_folder)
                self.log(f"创建商品文件夹: {target_folder}")

            # 使用检测到的设备名称查找手机文件夹
            device_name_to_use = self.current_device_name if self.current_device_name else "未知设备"

            mtp_folder = self.mtp_manager.find_mtp_device_folder(
                device_name=device_name_to_use,
                target_path="内部存储设备\\DCIM\\Pindd\\goods"
            )

            if mtp_folder is None:
                self.log("❌ 无法找到手机MTP设备或目标文件夹")
                self.log("请检查以下事项：")
                self.log("1. 手机是否已连接到电脑")
                self.log("2. 手机是否开启了文件传输模式")
                self.log(f"3. 设备名称是否为 '{device_name_to_use}'")
                self.log("4. 图片是否已保存到 DCIM/Pindd/goods 文件夹")
                return False

            # 从MTP设备复制文件
            copied_files = self.mtp_manager.copy_files_from_mtp(mtp_folder, target_folder)

            if copied_files:
                self.log(f"✅ 成功复制 {len(copied_files)} 个图片到商品 {self.current_product_index} 文件夹")
                self.log("复制的文件:")
                for filename in copied_files:
                    self.log(f"  - {filename}")

                # 注意：由于MTP协议限制，我们只能复制文件，无法直接删除手机上的文件
                # 用户需要手动清理手机文件夹，或者我们可以提供提醒
                self.log("⚠️  注意：由于MTP协议限制，无法自动删除手机上的原文件")
                self.log("请手动删除手机 DCIM/Pindd/goods 文件夹中的图片以释放空间")

                return True
            else:
                self.log("❌ 未找到任何图片文件或复制失败")
                return False

        except Exception as e:
            self.log(f"从MTP设备复制图片时出错: {str(e)}")
            return False

    def execute_automation_sequence(self, links):
        """
        执行自动化操作序列（支持多商品循环处理）

        Args:
            links (list): 商品链接列表
        """
        try:
            if not links:
                self.log("错误：没有可用的商品链接")
                return False

            # 存储链接列表供其他方法使用
            self.links = links

            self.log("=== 开始自动化操作 ===")
            self.log(f"🚀 准备处理 {len(links)} 个商品链接")

            # 检测设备品牌并设置图片文件夹
            self.log("📱 检测连接设备品牌...")
            device_brand = self.detect_device_brand()
            if device_brand is None:
                self.log("⚠️ 警告：无法检测设备品牌，将使用默认图片文件夹")
                self.image_folder = "image"
                # 如果检测失败，尝试重新检测设备名称用于文件复制
                if not self.current_device_name:
                    self.current_device_name = self.mtp_manager.detect_connected_mtp_device()
                    if self.current_device_name:
                        self.log(f"📱 检测到设备名称: {self.current_device_name}")
            else:
                self.log(f"✅ 设备品牌检测完成，当前图片文件夹: {self.image_folder}")
                self.log(f"📱 当前设备: {self.current_device_name}")

            # 循环处理每个商品
            for product_index, link in enumerate(links, 1):
                # 检查是否需要插入暂停步骤
                if self.pause_controller and self.pause_controller.should_insert_pause_step():
                    self.log("🔴 检测到暂停请求，插入暂停等待步骤...")
                    self.pause_controller.mark_pause_step_inserted()
                    self.pause_controller.execute_pause_wait()

                self.log(f"\n{'='*60}")
                self.log(f"🎯 开始处理第 {product_index} 个商品")
                self.log(f"商品链接: {link}")
                self.log(f"{'='*60}")

                # 设置当前商品索引
                self.current_product_index = product_index

                # 处理单个商品
                success = self.process_single_product(link, product_index)

                if not success:
                    self.log(f"❌ 第 {product_index} 个商品处理失败")
                    return False

                self.log(f"✅ 第 {product_index} 个商品处理完成")

                # 如果不是最后一个商品，执行返回操作准备下一个商品
                if product_index < len(links):
                    # 在准备下一个商品前检查暂停
                    if self.pause_controller and self.pause_controller.should_insert_pause_step():
                        self.log("🔴 检测到暂停请求，插入暂停等待步骤...")
                        self.pause_controller.mark_pause_step_inserted()
                        self.pause_controller.execute_pause_wait()

                    self.log(f"🔄 准备处理下一个商品...")
                    if not self.return_to_search_page():
                        self.log("❌ 返回搜索页面失败")
                        return False

            self.log(f"\n🎉 所有 {len(links)} 个商品处理完成！")
            return True

        except Exception as e:
            self.log(f"❌ 执行自动化序列时出错: {str(e)}")
            import traceback
            self.log(f"错误详情: {traceback.format_exc()}")
            return False

    def process_single_product(self, link, product_index):
        """
        处理单个商品的完整流程

        Args:
            link: 商品链接
            product_index: 商品编号

        Returns:
            bool: 处理是否成功
        """
        try:
            # 步骤1: 确保剪贴板中有正确的商品链接
            self.log("步骤1: 设置剪贴板内容...")

            # 复制商品链接到剪贴板
            pyperclip.copy(link)
            self.log(f"已复制商品链接到剪贴板: {link}")

            # 验证剪贴板内容
            time.sleep(0.5)
            clipboard_content = pyperclip.paste()
            if clipboard_content == link:
                self.log("✅ 剪贴板内容验证成功")
            else:
                self.log(f"❌ 剪贴板内容验证失败")
                self.log(f"期望: {link}")
                self.log(f"实际: {clipboard_content}")
                return False

            # 步骤2: 点击搜索框并输入链接
            self.log("步骤2: 点击搜索框并输入链接...")

            # 移动到位置(480,96)并点击
            self.log("移动到位置(480,96)并点击...")
            pyautogui.click(480, 96)
            time.sleep(0.5)

            # 按下Ctrl+A全选
            self.log("按下Ctrl+A全选...")
            if not self.execute_autohotkey_script("ctrl_a"):
                self.log("Ctrl+A执行失败，使用备用方法...")
                pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.5)

            # 粘贴商品链接
            self.log("粘贴商品链接...")
            if self.simulate_real_ctrl_v():
                self.log("✅ Ctrl+V操作执行成功")
            else:
                self.log("❌ Ctrl+V操作失败，请手动粘贴链接")
                return False

            time.sleep(2.0)  # 等待粘贴完成

            # 短暂停顿
            time.sleep(1)

            # 步骤3: 查找并点击SOUSUO2.png
            self.log("步骤3: 点击搜索按钮...")
            if not self.find_and_click_image("SOUSUO2.png"):
                self.log("错误：未找到搜索按钮图片 SOUSUO2.png")
                return False

            # 步骤4: 等待3秒进入商品页面
            self.log("步骤4: 等待3秒进入商品页面...")
            time.sleep(3)

            # 步骤5: 执行从右往左的滑动操作
            self.log("步骤5: 执行从右往左的滑动操作...")
            self.perform_right_to_left_swipe()

            # 步骤6: 移动到指定位置
            self.log("步骤6: 移动到位置(470,185)...")
            pyautogui.moveTo(470, 185)
            self.log("已移动到位置(470,185)")

            # 步骤7: 执行鼠标点击和长按操作
            self.log("步骤7: 执行鼠标点击和长按操作...")
            self.click_and_hold(475, 323, hold_duration=0.5)

            # 停顿1秒后查找保存按钮
            time.sleep(1.0)

            # 步骤8: 查找并点击保存按钮（支持重试）
            self.log("步骤8: 查找并点击保存按钮...")
            if not self.find_and_click_image_with_retry("BAOCUN1.png", delay=0.5, max_retries=3, retry_delay=5):
                self.log("错误：未找到保存按钮图片 BAOCUN1.png（已重试3次）")
                return False

            # 步骤9: 检测保存中图片
            self.log("步骤9: 检测保存状态...")
            # 8秒检测时间：8秒 ÷ 0.2秒 = 80次检测
            if self.wait_for_image("BAOCUNZHONG.png", max_attempts=80, check_interval=0.2):
                self.log("✅ 检测到保存中状态，等待保存完成...")

                # 等待保存完成，给图片足够时间保存到手机
                self.log("等待5秒让图片完全保存到手机...")
                time.sleep(5.0)

                # 步骤10: 复制手机图片到当前商品文件夹
                self.log(f"步骤10: 复制手机图片到商品 {product_index} 文件夹...")

                # 尝试多次，因为文件系统可能有延迟
                max_attempts = 3
                for attempt in range(1, max_attempts + 1):
                    self.log(f"第 {attempt}/{max_attempts} 次尝试复制图片...")
                    if self.move_phone_images_to_product_folder():
                        self.log("✅ 图片复制完成")

                        # 步骤11: 删除手机上的原文件
                        self.log("步骤11: 删除手机上的原文件...")
                        if self.delete_phone_files():
                            self.log("✅ 手机文件删除完成")
                        else:
                            self.log("⚠️ 手机文件删除失败，请手动删除")

                        # 步骤12: 处理主图（在后台线程中执行）
                        self.log("步骤12: 开始处理主图...")
                        import threading
                        main_image_thread = threading.Thread(
                            target=self.process_main_images,
                            args=(product_index,),
                            daemon=True
                        )
                        main_image_thread.start()

                        # 步骤13: 开始第二轮操作（详情页）
                        self.log("步骤13: 开始第二轮操作（详情页）...")
                        if self.capture_detail_images():
                            self.log("✅ 详情页图片捕获完成")
                            self.log("✅ OCR分析和Excel保存已在详情页处理中完成")
                            return True
                        else:
                            self.log("❌ 详情页图片捕获失败")
                            return False
                    else:
                        if attempt < max_attempts:
                            self.log(f"第 {attempt} 次尝试失败，等待2秒后重试...")
                            time.sleep(2.0)
                        else:
                            self.log("❌ 所有尝试都失败，图片复制失败")
                            return False
            else:
                self.log("❌ 未检测到保存中状态，可能保存失败")
                return False

        except Exception as e:
            self.log(f"❌ 处理商品 {product_index} 时出错: {str(e)}")
            import traceback
            self.log(f"错误详情: {traceback.format_exc()}")
            return False



    def update_excel_with_analysis(self, analysis_result, target_link, product_index):
        """
        更新Excel文件，将分析结果保存到对应商品链接下方

        Args:
            analysis_result: 智能分析结果
            target_link: 目标商品链接
            product_index: 商品编号

        Returns:
            bool: 更新是否成功
        """
        try:
            self.log("💾 更新Excel文件...")

            excel_path = os.path.join("商品图片", "商品SKU信息.xlsx")

            # 读取现有Excel文件（无列名）
            if os.path.exists(excel_path):
                df = pd.read_excel(excel_path, header=None)  # 不使用第一行作为列名
            else:
                self.log("❌ Excel文件不存在，无法更新")
                return False

            # 找到目标商品链接的位置
            target_row = None
            for idx, row in df.iterrows():
                # 检查第二列（索引1）是否包含目标链接
                if len(row) > 1 and pd.notna(row.iloc[1]) and target_link in str(row.iloc[1]):
                    target_row = idx
                    self.log(f"找到目标商品链接在第 {idx+1} 行")
                    break

            if target_row is None:
                self.log(f"❌ 未找到目标商品链接: {target_link}")
                return False

            # 准备要插入的数据
            insert_data = []

            # 处理颜色分类和价格信息
            color_prices = analysis_result.get('color_prices', {})
            if color_prices:
                for color, price in color_prices.items():
                    if price and price != '未获取':
                        insert_data.append([color, price])

            # 处理尺码信息
            size_info = analysis_result.get('size_info', {})
            if size_info:
                sizes = size_info.get('sizes', [])
                size_range = size_info.get('range', '')
                if sizes:
                    size_text = f"{','.join(map(str, sizes))}"
                    if size_range:
                        size_text += f" ({size_range})"
                    insert_data.append(['尺码', size_text])

            # 如果有数据要插入
            if insert_data:
                # 在目标行下方插入数据
                for i, (left_value, right_value) in enumerate(insert_data):
                    insert_row = target_row + 1 + i

                    # 创建新行数据
                    new_row = pd.Series([None] * len(df.columns))
                    new_row.iloc[0] = left_value   # 左列：颜色或尺码
                    new_row.iloc[1] = right_value  # 右列：价格或尺码信息

                    # 插入新行
                    df = pd.concat([
                        df.iloc[:insert_row],
                        pd.DataFrame([new_row]),
                        df.iloc[insert_row:]
                    ], ignore_index=True)

                # 保存更新后的Excel文件
                df.to_excel(excel_path, index=False, header=False)
                self.log(f"✅ 已将 {len(insert_data)} 行数据插入Excel文件")
                return True
            else:
                self.log("⚠️ 没有有效数据需要插入Excel")
                return True

        except Exception as e:
            self.log(f"❌ 更新Excel时出错: {str(e)}")
            import traceback
            self.log(f"错误详情: {traceback.format_exc()}")
            return False

    def return_to_search_page(self):
        """
        返回到搜索页面，准备处理下一个商品

        Returns:
            bool: 返回是否成功
        """
        try:
            self.log("🔄 开始返回搜索页面...")

            # 移动到指定位置(470,590)
            self.log("移动到位置(470,590)...")
            pyautogui.moveTo(470, 590)
            time.sleep(0.5)

            # 点击鼠标右键
            self.log("点击鼠标右键...")
            pyautogui.rightClick()
            time.sleep(1.0)

            # 再次点击右键
            self.log("再次点击鼠标右键...")
            pyautogui.rightClick()
            time.sleep(0.5)

            self.log("✅ 返回搜索页面操作完成")
            return True

        except Exception as e:
            self.log(f"❌ 返回搜索页面时出错: {str(e)}")
            return False

    def delete_phone_files(self):
        """
        删除手机上的图片文件
        通过点击GOODS.png图片，然后使用AutoHotkey执行删除操作

        Returns:
            bool: 删除是否成功
        """
        try:
            self.log("开始删除手机文件操作...")

            # 查找并点击GOODS.png
            self.log("查找并点击GOODS.png...")
            if not self.find_and_click_image("GOODS.png"):
                self.log("❌ 未找到GOODS.png图片")
                return False

            # 等待文件夹打开
            time.sleep(1.0)

            # 使用AutoHotkey执行删除操作
            self.log("使用AutoHotkey执行删除操作...")
            if self.execute_autohotkey_script("delete_files"):
                self.log("✅ 删除操作执行成功")
                return True
            else:
                self.log("❌ AutoHotkey删除操作失败")

                # 备用方法：使用Python模拟按键
                self.log("使用备用方法执行删除操作...")
                try:
                    # F5刷新
                    pyautogui.press('f5')
                    time.sleep(1.0)

                    # Ctrl+A全选
                    pyautogui.hotkey('ctrl', 'a')
                    time.sleep(0.5)

                    # Delete删除
                    pyautogui.press('delete')
                    time.sleep(1.0)

                    self.log("✅ 备用删除方法执行完成")
                    return True

                except Exception as e:
                    self.log(f"备用删除方法失败: {str(e)}")
                    return False

        except Exception as e:
            self.log(f"删除手机文件失败: {str(e)}")
            return False

    def process_main_images(self, folder_index):
        """
        处理主图：保留前4张，重命名为主图1-4

        Args:
            folder_index (int): 商品文件夹编号

        Returns:
            bool: 处理是否成功
        """
        try:
            folder_path = os.path.join(self.goods_folder, str(folder_index))
            if not os.path.exists(folder_path):
                self.log(f"商品文件夹不存在: {folder_path}")
                return False

            self.log(f"处理主图文件夹: {folder_path}")

            # 获取所有图片文件
            image_files = []
            for file in os.listdir(folder_path):
                if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.gif')):
                    file_path = os.path.join(folder_path, file)
                    # 获取创建时间
                    create_time = os.path.getctime(file_path)
                    image_files.append((file_path, create_time))

            if not image_files:
                self.log("文件夹中没有图片文件")
                return True

            # 按创建时间排序
            image_files.sort(key=lambda x: x[1])
            self.log(f"找到 {len(image_files)} 张图片")

            # 删除第5张及以后的图片
            if len(image_files) > 4:
                for i in range(4, len(image_files)):
                    file_path = image_files[i][0]
                    try:
                        os.remove(file_path)
                        self.log(f"删除多余图片: {os.path.basename(file_path)}")
                    except Exception as e:
                        self.log(f"删除图片失败 {file_path}: {str(e)}")

                # 更新列表，只保留前4张
                image_files = image_files[:4]

            # 重命名为主图1-4
            for i, (file_path, _) in enumerate(image_files, 1):
                file_ext = os.path.splitext(file_path)[1]
                new_name = f"主图{i}{file_ext}"
                new_path = os.path.join(folder_path, new_name)

                try:
                    # 如果目标文件已存在，先删除
                    if os.path.exists(new_path):
                        os.remove(new_path)

                    os.rename(file_path, new_path)
                    self.log(f"重命名: {os.path.basename(file_path)} → {new_name}")
                except Exception as e:
                    self.log(f"重命名失败 {file_path}: {str(e)}")

            self.log("✅ 主图处理完成")

            return True

        except Exception as e:
            self.log(f"处理主图失败: {str(e)}")
            return False

    def perform_smart_ocr_analysis(self, product_index):
        """
        执行智能OCR分析并根据结果选择处理方案

        Args:
            product_index: 商品编号
        """
        try:
            self.log("=" * 50)
            self.log("🧠 智能OCR分析开始")
            self.log("=" * 50)

            # 定义OCR识别区域（根据您的测试结果）
            ocr_region = (270, 230, 691, 883)
            x1, y1, x2, y2 = ocr_region

            # 执行OCR识别
            ocr_results, screenshot_path = self.ocr_analyzer.capture_and_ocr_screen(
                x1, y1, x2, y2, product_index
            )

            if not ocr_results:
                self.log("❌ OCR识别失败，使用基础处理方案")
                return

            # 保存OCR结果到文件
            self.log("💾 保存OCR结果到文件...")
            self.save_ocr_results(ocr_results, product_index)

            # 智能分析商品信息
            analysis_result = self.ocr_analyzer.analyze_product_info(ocr_results)

            # 显示分析结果
            self.log("📊 智能分析结果:")
            size_info = analysis_result.get('size_info', {})
            if isinstance(size_info, dict):
                self.log(f"  优化尺码范围: {size_info.get('optimized_range', '')}")
                self.log(f"  原始尺码文本: {size_info.get('original_texts', [])}")
            else:
                self.log(f"  尺码信息: {size_info}")
            self.log(f"  颜色分类: {analysis_result['color_classifications']}")
            self.log(f"  颜色价格: {analysis_result['color_prices']}")
            self.log(f"  分析类型: {analysis_result['analysis_type']}")
            self.log(f"  处理方案: {analysis_result['processing_scheme']}")

            # 根据处理方案执行相应操作
            if analysis_result['processing_scheme'] == 'interactive':
                self.log("🔄 切换到交互式价格获取方案")
                self.execute_interactive_price_extraction(analysis_result, ocr_region, product_index)
            else:
                self.log("📝 使用基础处理方案，直接保存分析结果")
                self.save_analysis_result_to_excel(analysis_result, product_index)

            self.log("=" * 50)
            self.log("✅ 智能OCR分析完成")
            self.log("=" * 50)

        except Exception as e:
            self.log(f"智能OCR分析失败: {e}")
            self.log("使用基础处理方案")

    def save_analysis_result_to_excel(self, analysis_result, product_index):
        """
        保存分析结果到Excel文件（新的表格结构）

        Args:
            analysis_result: 分析结果
            product_index: 商品编号
        """
        try:
            self.log("💾 保存分析结果到Excel...")

            excel_path = os.path.join("商品图片", "商品SKU信息.xlsx")

            # 读取现有Excel文件（无列名）
            if os.path.exists(excel_path):
                df = pd.read_excel(excel_path, header=None)  # 不使用第一行作为列名
            else:
                self.log("❌ Excel文件不存在，无法保存分析结果")
                return

            # 找到当前商品链接的位置
            target_row = None
            current_link = self.links[product_index - 1] if product_index <= len(self.links) else None

            if current_link:
                for idx, row in df.iterrows():
                    # 检查第二列（索引1）是否包含当前商品链接
                    if len(row) > 1 and pd.notna(row.iloc[1]) and current_link in str(row.iloc[1]):
                        target_row = idx
                        self.log(f"找到目标商品链接在第 {idx+1} 行")
                        break

            if target_row is None:
                self.log(f"❌ 未找到商品{product_index}对应的链接")
                return

            # 准备要插入的数据（与test_ocr_quick.py一致的格式）
            insert_data = []

            # 添加颜色分类和价格信息（颜色和价格在同一行的两个单元格）
            if analysis_result['color_classifications']:
                for color in analysis_result['color_classifications']:
                    price = analysis_result['color_prices'].get(color, '未获取')
                    # 颜色在第二列，价格在第三列
                    insert_data.append([None, color, price])
                    self.log(f"✅ 保存有价格颜色: {color} -> {price}")

            # 添加原始尺码信息（数字列表）
            size_info = analysis_result.get('size_info', {})
            if isinstance(size_info, dict):
                size_numbers = size_info.get('size_numbers', [])
                if size_numbers:
                    # 保存原始尺码数字列表
                    size_list_text = " ， ".join(map(str, size_numbers))
                    insert_data.append([None, size_list_text, None])

                # 添加整理后的尺码范围
                optimized_range = size_info.get('optimized_range', '')
                if optimized_range:
                    insert_data.append([None, optimized_range, None])

            # 智能清空目标链接下方的旧数据，保留空行（方案3）
            if insert_data:
                # 找到下一个商品链接的位置（用于确定处理范围）
                next_link_row = None
                for idx in range(target_row + 1, len(df)):
                    row = df.iloc[idx]
                    # 检查是否是另一个商品链接（包含http或https）
                    if len(row) > 1 and pd.notna(row.iloc[1]):
                        cell_value = str(row.iloc[1])
                        if 'http' in cell_value and 'yangkeduo.com' in cell_value:
                            next_link_row = idx
                            self.log(f"找到下一个商品链接在第 {idx+1} 行")
                            break

                # 智能清空：只清空有内容的行，保留空行
                if next_link_row is not None:
                    # 处理目标链接到下一个链接之间的区域
                    before_df = df.iloc[:target_row + 1]
                    middle_df = df.iloc[target_row + 1:next_link_row]
                    after_df = df.iloc[next_link_row:]

                    # 清空有内容的行，保留空行
                    cleared_middle_rows = []
                    cleared_count = 0
                    for idx, row in middle_df.iterrows():
                        # 检查行是否有内容（任何一列有非空值）
                        has_content = any(pd.notna(cell) and str(cell).strip() != '' for cell in row)
                        if has_content:
                            # 有内容的行，清空为空行
                            cleared_middle_rows.append([None] * len(row))
                            cleared_count += 1
                        else:
                            # 空行，保持原样
                            cleared_middle_rows.append(row.tolist())

                    self.log(f"清空第 {target_row+2} 行到第 {next_link_row} 行中的 {cleared_count} 行有内容数据，保留空行")

                    # 创建清空后的中间区域DataFrame
                    cleared_middle_df = pd.DataFrame(cleared_middle_rows)

                    # 创建新的DataFrame包含插入的数据
                    insert_df = pd.DataFrame(insert_data)

                    # 合并DataFrame：before + insert + cleared_middle + after
                    new_df = pd.concat([before_df, insert_df, cleared_middle_df, after_df], ignore_index=True)
                else:
                    # 处理目标链接到文件末尾的区域
                    before_df = df.iloc[:target_row + 1]
                    middle_df = df.iloc[target_row + 1:]

                    # 清空有内容的行，保留空行
                    cleared_middle_rows = []
                    cleared_count = 0
                    for idx, row in middle_df.iterrows():
                        # 检查行是否有内容
                        has_content = any(pd.notna(cell) and str(cell).strip() != '' for cell in row)
                        if has_content:
                            # 有内容的行，清空为空行
                            cleared_middle_rows.append([None] * len(row))
                            cleared_count += 1
                        else:
                            # 空行，保持原样
                            cleared_middle_rows.append(row.tolist())

                    self.log(f"清空第 {target_row+2} 行到文件末尾中的 {cleared_count} 行有内容数据，保留空行")

                    # 创建清空后的中间区域DataFrame
                    cleared_middle_df = pd.DataFrame(cleared_middle_rows)

                    # 创建新的DataFrame包含插入的数据
                    insert_df = pd.DataFrame(insert_data)

                    # 合并DataFrame：before + insert + cleared_middle
                    new_df = pd.concat([before_df, insert_df, cleared_middle_df], ignore_index=True)

                # 保存Excel文件（保持原本格式，无列名）
                new_df.to_excel(excel_path, index=False, header=False)
                self.log(f"✅ 分析结果已保存到商品{product_index}下方: {excel_path}")
                self.log(f"   插入了 {len(insert_data)} 行新数据")

        except Exception as e:
            self.log(f"保存Excel失败: {e}")
            import traceback
            self.log(f"错误详情: {traceback.format_exc()}")

    def execute_interactive_price_extraction(self, analysis_result, ocr_region, product_index):
        """
        执行交互式价格获取

        Args:
            analysis_result: 分析结果
            ocr_region: OCR区域 (x1, y1, x2, y2)
            product_index: 商品编号
        """
        try:
            self.log("🚀 开始交互式价格获取...")

            # 获取需要交互获取价格的颜色
            colors_need_interaction = []
            for color in analysis_result['color_classifications']:
                # 现在color_classifications已经是纯字符串列表了
                if color not in analysis_result['color_prices']:
                    colors_need_interaction.append(color)

            if not colors_need_interaction:
                self.log("所有颜色都已有价格，无需交互")
                self.save_analysis_result_to_excel(analysis_result, product_index)
                return

            self.log(f"需要交互获取价格的颜色: {colors_need_interaction}")

            # 获取OCR区域坐标
            x1, y1, x2, y2 = ocr_region

            # 获取中间尺码
            middle_size = self.get_middle_size(analysis_result.get('size_info', {}))
            self.log(f"选择中间尺码: {middle_size}")

            # 步骤1：点击中间尺码
            if middle_size:
                # 优先使用记录的尺码坐标
                size_coords = None
                size_coordinates = analysis_result.get('size_coordinates', {})

                self.log(f"🔍 检查尺码坐标记录: {size_coordinates}")
                self.log(f"🔍 查找尺码: {middle_size}")

                if middle_size in size_coordinates:
                    # 获取相对坐标并转换为绝对坐标
                    relative_coords = size_coordinates[middle_size]
                    absolute_x = x1 + relative_coords[0]
                    absolute_y = y1 + relative_coords[1]
                    size_coords = (absolute_x, absolute_y)
                    self.log(f"📍 使用记录的尺码坐标: {middle_size}")
                    self.log(f"   相对坐标: {relative_coords}")
                    self.log(f"   绝对坐标: {size_coords}")
                else:
                    # 回退到OCR查找坐标
                    size_coords = self.find_size_coordinates(middle_size, ocr_region)
                    self.log(f"🔍 OCR查找尺码坐标: {middle_size} -> {size_coords}")

                if size_coords:
                    self.log(f"🎯 移动到尺码 {middle_size} 坐标: {size_coords}")
                    pyautogui.moveTo(size_coords[0], size_coords[1])
                    time.sleep(0.8)  # 等待0.8秒

                    self.log(f"🎯 点击尺码 {middle_size}")
                    pyautogui.click(size_coords[0], size_coords[1])
                    time.sleep(2.0)  # 等待页面更新
                else:
                    self.log(f"⚠️ 未找到尺码 {middle_size} 的坐标")

            # 复制原始价格字典
            updated_prices = analysis_result['color_prices'].copy()

            # 特殊处理：如果只有一个颜色，直接截图OCR，不点击颜色
            if len(colors_need_interaction) == 1:
                self.log("🎯 检测到单颜色商品，跳过颜色点击，直接截图OCR")
                color_name = colors_need_interaction[0]

                # 直接截图并OCR识别当前页面
                self.log(f"📸 截取单颜色商品 {color_name} 的页面...")
                price_ocr_results, screenshot_path = self.capture_and_ocr_price_screen(
                    x1, y1, x2, y2, product_index, 1
                )

                # 从OCR结果中提取价格
                if price_ocr_results:
                    price = self.extract_price_from_ocr_results(price_ocr_results)
                    if price:
                        if price == "售罄":
                            self.log(f"颜色 {color_name} 已售罄")
                            updated_prices[color_name] = "售罄"
                        else:
                            updated_prices[color_name] = price
                            self.log(f"✅ 获取到单颜色商品 {color_name} 的价格: {price}")
                    else:
                        self.log(f"⚠️ 未能从OCR结果中提取单颜色商品 {color_name} 的价格")
                        updated_prices[color_name] = "获取失败"
                else:
                    self.log(f"⚠️ 单颜色商品 {color_name} 的OCR识别失败")
                    updated_prices[color_name] = "OCR失败"
            else:
                # 步骤2：依次点击每个颜色并获取价格（多颜色商品）
                self.log(f"🎯 检测到多颜色商品，需要依次点击 {len(colors_need_interaction)} 个颜色")
                for i, color in enumerate(colors_need_interaction):
                    # 现在color已经是字符串了
                    color_name = color

                    self.log(f"🎨 处理颜色 {i+1}/{len(colors_need_interaction)}: {color_name}")

                    # 优先使用记录的坐标，如果没有则使用OCR查找
                    color_coords = None

                    # 尝试从分析结果中获取记录的坐标
                    color_coordinates = analysis_result.get('color_coordinates', {})
                    if color_name in color_coordinates:
                        # 获取相对坐标并转换为绝对坐标
                        relative_coords = color_coordinates[color_name]
                        absolute_x = x1 + relative_coords[0]
                        absolute_y = y1 + relative_coords[1]
                        color_coords = (absolute_x, absolute_y)
                        self.log(f"📍 使用记录的坐标: {color_name}")
                        self.log(f"   相对坐标: {relative_coords}")
                        self.log(f"   绝对坐标: {color_coords}")
                    else:
                        # 回退到OCR查找坐标
                        color_coords = self.find_color_coordinates_from_ocr(color_name, ocr_region)
                        self.log(f"🔍 OCR查找坐标: {color_name} -> {color_coords}")

                    if color_coords:
                        self.log(f"🎯 移动到颜色 {color_name} 坐标: {color_coords}")
                        pyautogui.moveTo(color_coords[0], color_coords[1])
                        time.sleep(0.8)  # 等待0.8秒

                        self.log(f"🎯 点击颜色 {color_name}")
                        pyautogui.click(color_coords[0], color_coords[1])
                        time.sleep(2.0)  # 等待页面更新

                        # 步骤3：截图并OCR识别更新后的页面
                        self.log(f"📸 截取颜色 {color_name} 更新后的页面...")
                        price_ocr_results, screenshot_path = self.capture_and_ocr_price_screen(
                            x1, y1, x2, y2, product_index, i+1
                        )

                        # 步骤4：从新的OCR结果中提取价格
                        if price_ocr_results:
                            price = self.extract_price_from_ocr_results(price_ocr_results)
                            if price:
                                if price == "售罄":
                                    self.log(f"颜色 {color_name} 已售罄，跳过")
                                else:
                                    updated_prices[color_name] = price
                                    self.log(f"✅ 获取到颜色 {color_name} 的价格: {price}")

                                    # 在点击第一个颜色后进行坐标校验
                                    if i == 0:  # 只在第一个颜色后执行坐标校验
                                        self.log("🔍 开始坐标校验，重新进行完整OCR识别...")
                                        updated_coordinates = self.verify_and_update_coordinates(
                                            x1, y1, x2, y2, analysis_result['color_coordinates']
                                        )
                                        if updated_coordinates:
                                            # 只更新坐标，不改变颜色列表
                                            for color_name, new_coord in updated_coordinates.items():
                                                if color_name in analysis_result['color_coordinates']:
                                                    analysis_result['color_coordinates'][color_name] = new_coord
                                            self.log("✅ 坐标校验完成，后续颜色将使用更新后的坐标")
                                        else:
                                            self.log("✅ 坐标无变化，继续使用原始坐标")
                            else:
                                self.log(f"⚠️ 未能从OCR结果中提取颜色 {color_name} 的价格")
                                updated_prices[color_name] = "获取失败"
                        else:
                            self.log(f"⚠️ 颜色 {color_name} 的OCR识别失败")
                            updated_prices[color_name] = "OCR失败"
                    else:
                        self.log(f"⚠️ 未找到颜色 {color_name} 的坐标")
                        updated_prices[color_name] = "坐标未找到"

            # 更新分析结果
            analysis_result['color_prices'] = updated_prices

            # 保存最终结果
            self.save_analysis_result_to_excel(analysis_result, product_index)

            self.log("🎉 交互式价格获取完成")

        except Exception as e:
            self.log(f"交互式价格获取失败: {e}")
            # 保存部分结果
            self.save_analysis_result_to_excel(analysis_result, product_index)

    def get_middle_size(self, size_info):
        """获取中间尺码（避免最小码和最大码）"""
        import re

        # 处理新的尺码信息格式
        if isinstance(size_info, dict):
            size_numbers = size_info.get('size_numbers', [])
            if size_numbers:
                # 排序尺码列表
                sorted_sizes = sorted(size_numbers)

                if len(sorted_sizes) >= 3:
                    # 如果有3个或以上尺码，选择中间的（排除首尾）
                    middle_index = len(sorted_sizes) // 2
                    selected_size = sorted_sizes[middle_index]
                    self.log(f"📏 尺码选择策略: 从{len(sorted_sizes)}个尺码中选择中间值 {selected_size}")
                    self.log(f"   完整尺码列表: {sorted_sizes}")
                    self.log(f"   避免最小码: {sorted_sizes[0]}，避免最大码: {sorted_sizes[-1]}")
                    return selected_size
                elif len(sorted_sizes) == 2:
                    # 如果只有2个尺码，选择较小的那个（避免最大码）
                    selected_size = sorted_sizes[0]
                    self.log(f"📏 尺码选择策略: 只有2个尺码，选择较小的 {selected_size} (避免最大码 {sorted_sizes[1]})")
                    return selected_size
                elif len(sorted_sizes) == 1:
                    # 如果只有1个尺码，就选择它
                    selected_size = sorted_sizes[0]
                    self.log(f"📏 尺码选择策略: 只有1个尺码，选择 {selected_size}")
                    return selected_size

        # 兼容旧格式
        if isinstance(size_info, str) and size_info:
            match = re.match(r'(\d+)-(\d+)', size_info)
            if match:
                min_size = int(match.group(1))
                max_size = int(match.group(2))

                # 计算中间区域的尺码（避免最小和最大）
                size_list = []
                for size in [73, 80, 90, 100, 110, 120, 130, 140, 150, 160, 170]:
                    if min_size <= size <= max_size:
                        size_list.append(size)

                if len(size_list) >= 3:
                    # 如果有3个或以上尺码，选择中间的
                    middle_index = len(size_list) // 2
                    selected_size = size_list[middle_index]
                    self.log(f"📏 尺码选择策略: 从范围{min_size}-{max_size}中选择中间值 {selected_size}")
                    return selected_size
                elif len(size_list) == 2:
                    # 如果只有2个尺码，选择较小的
                    selected_size = size_list[0]
                    self.log(f"📏 尺码选择策略: 范围{min_size}-{max_size}只有2个尺码，选择较小的 {selected_size}")
                    return selected_size
                elif len(size_list) == 1:
                    # 如果只有1个尺码，就选择它
                    selected_size = size_list[0]
                    self.log(f"📏 尺码选择策略: 范围{min_size}-{max_size}只有1个尺码，选择 {selected_size}")
                    return selected_size
                else:
                    # 如果没有标准尺码，计算数学中间值
                    selected_size = (min_size + max_size) // 2
                    self.log(f"📏 尺码选择策略: 范围{min_size}-{max_size}无标准尺码，使用数学中间值 {selected_size}")
                    return selected_size

        self.log("📏 尺码选择策略: 使用默认尺码 130")
        return 130  # 默认尺码

    def find_size_coordinates_from_ocr(self, middle_size, ocr_region):
        """通过OCR识别结果找到尺码的实际坐标（完整版本）"""
        try:
            self.log(f"🔍 通过OCR查找尺码 {middle_size} 的实际位置...")

            # 截取当前屏幕进行OCR
            x1, y1, x2, y2 = ocr_region
            screenshot = pyautogui.screenshot(region=(x1, y1, x2-x1, y2-y1))

            # 转换为numpy数组
            img_array = np.array(screenshot)
            img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

            # 获取OCR实例
            ocr = get_ocr_instance()
            results = ocr.ocr(img_bgr)

            if results and len(results) > 0:
                page_result = results[0]
                self.log(f"OCR结果类型: {type(page_result)}")

                # 方式1：尝试新的OCR结果格式
                try:
                    if hasattr(page_result, 'texts') and hasattr(page_result, 'scores') and hasattr(page_result, 'boxes'):
                        texts = page_result.texts
                        scores = page_result.scores
                        boxes = page_result.boxes

                        for i, (text, score, box) in enumerate(zip(texts, scores, boxes)):
                            self.log(f"文本 {i}: '{text}' (置信度: {score:.3f})")
                            # 修改匹配逻辑：支持包含尺码的文本（如"130￥29.9"）
                            if score > 0.8 and (text.strip() == str(middle_size) or text.startswith(str(middle_size))):
                                # 计算边界框中心点（相对于截图区域）
                                # box格式: [left, top, right, bottom]
                                center_x = int((box[0] + box[2]) / 2)
                                center_y = int((box[1] + box[3]) / 2)

                                # 转换为绝对坐标（相对于屏幕）
                                abs_center_x = center_x + x1
                                abs_center_y = center_y + y1

                                self.log(f"✅ 找到尺码 {middle_size} 在位置: ({abs_center_x}, {abs_center_y}) (来源文本: '{text}')")
                                return (abs_center_x, abs_center_y)
                except Exception as e:
                    self.log(f"方式1解析失败: {e}")

                # 方式2：尝试另一种新格式
                try:
                    if hasattr(page_result, '__iter__') and hasattr(page_result, '__getitem__'):
                        texts = [item.text for item in page_result]
                        scores = [item.score for item in page_result]
                        boxes = [item.box for item in page_result]

                        for i, (text, score, box) in enumerate(zip(texts, scores, boxes)):
                            self.log(f"文本 {i}: '{text}' (置信度: {score:.3f})")
                            # 修改匹配逻辑：支持包含尺码的文本（如"130￥29.9"）
                            if score > 0.8 and (text.strip() == str(middle_size) or text.startswith(str(middle_size))):
                                # 计算边界框中心点（相对于截图区域）
                                center_x = int((box[0] + box[2]) / 2)
                                center_y = int((box[1] + box[3]) / 2)

                                # 转换为绝对坐标（相对于屏幕）
                                abs_center_x = center_x + x1
                                abs_center_y = center_y + y1

                                self.log(f"✅ 找到尺码 {middle_size} 在位置: ({abs_center_x}, {abs_center_y}) (来源文本: '{text}')")
                                return (abs_center_x, abs_center_y)
                except Exception as e:
                    self.log(f"方式2解析失败: {e}")

                # 备用解析方式（传统格式）
                if hasattr(page_result, '__iter__'):
                    for line in page_result:
                        if line and len(line) >= 2:
                            text_info = line[1]
                            if len(text_info) >= 2:
                                text = text_info[0]
                                confidence = text_info[1]
                                bbox = line[0]

                                self.log(f"备用方式文本: '{text}' (置信度: {confidence:.3f})")
                                if confidence > 0.8 and (text.strip() == str(middle_size) or text.startswith(str(middle_size))):
                                    # 计算边界框中心点
                                    center_x = int((bbox[0][0] + bbox[2][0]) / 2)
                                    center_y = int((bbox[0][1] + bbox[2][1]) / 2)

                                    # 转换为绝对坐标
                                    abs_center_x = center_x + x1
                                    abs_center_y = center_y + y1

                                    self.log(f"✅ 备用方式找到尺码 {middle_size} 在位置: ({abs_center_x}, {abs_center_y})")
                                    return (abs_center_x, abs_center_y)

            self.log(f"⚠️ 未通过OCR找到尺码 {middle_size}")
            return None

        except Exception as e:
            self.log(f"通过OCR查找尺码坐标失败: {e}")
            return None

    def find_size_coordinates_by_known_positions(self, middle_size, ocr_region):
        """基于已知实际坐标查找尺码位置"""
        # 已知的实际坐标（基于您提供的测试数据）
        known_positions = {
            130: (422, 691)
        }

        if middle_size in known_positions:
            coords = known_positions[middle_size]
            self.log(f"✅ 使用已知坐标找到尺码 {middle_size}: {coords}")
            return coords

        # 如果没有已知坐标，尝试根据130的位置估算其他尺码
        if 130 in known_positions:
            base_x, base_y = known_positions[130]
            # 尺码按3列排列: 110 120 130 (第一行)
            #               140 150 160 (第二行)
            #               170         (第三行)

            size_layout = {
                110: (-2, 0),  # 130左边2个位置
                120: (-1, 0),  # 130左边1个位置
                130: (0, 0),   # 基准位置
                140: (-2, 1),  # 下一行左边2个位置
                150: (-1, 1),  # 下一行左边1个位置
                160: (0, 1),   # 下一行同列
                170: (-2, 2)   # 第三行左边2个位置
            }

            if middle_size in size_layout:
                col_offset, row_offset = size_layout[middle_size]
                # 估算每个按钮的间距
                col_spacing = 60  # 列间距
                row_spacing = 45  # 行间距

                estimated_x = base_x + col_offset * col_spacing
                estimated_y = base_y + row_offset * row_spacing

                self.log(f"✅ 基于130位置估算尺码 {middle_size}: ({estimated_x}, {estimated_y})")
                return (estimated_x, estimated_y)

        return None

    def find_size_coordinates(self, middle_size, ocr_region):
        """查找尺码坐标（优先使用OCR，备用已知坐标）"""
        # 首先尝试通过OCR查找
        coords = self.find_size_coordinates_from_ocr(middle_size, ocr_region)
        if coords:
            return coords

        # 备用：使用已知坐标
        coords = self.find_size_coordinates_by_known_positions(middle_size, ocr_region)
        if coords:
            return coords

        self.log(f"⚠️ 所有方法都无法找到尺码 {middle_size} 的坐标")
        return None

    def find_color_coordinates_from_ocr(self, color_name, ocr_region):
        """通过OCR查找颜色坐标"""
        try:
            x1, y1, x2, y2 = ocr_region
            screenshot = pyautogui.screenshot(region=(x1, y1, x2-x1, y2-y1))
            img_array = np.array(screenshot)
            img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

            ocr = get_ocr_instance()
            results = ocr.ocr(img_bgr)

            if results and len(results) > 0:
                for line in results[0]:
                    if line and len(line) >= 2:
                        text_info = line[1]
                        if len(text_info) >= 2:
                            text = text_info[0]
                            confidence = text_info[1]
                            bbox = line[0]

                            if confidence > 0.7 and (text.strip() == color_name or color_name in text):
                                # 计算边界框中心点
                                center_x = int((bbox[0][0] + bbox[2][0]) / 2)
                                center_y = int((bbox[0][1] + bbox[2][1]) / 2)

                                # 转换为绝对坐标
                                abs_center_x = center_x + x1
                                abs_center_y = center_y + y1

                                return (abs_center_x, abs_center_y)

            return None

        except Exception as e:
            self.log(f"查找颜色坐标失败: {e}")
            return None

    def capture_and_ocr_price_screen(self, x1, y1, x2, y2, product_index, color_index):
        """截取屏幕并进行价格OCR识别"""
        try:
            screenshot = pyautogui.screenshot(region=(x1, y1, x2-x1, y2-y1))

            # 保存截图
            goods_folder = "商品图片"
            screenshot_filename = f"{product_index}-{color_index}.jpg"
            screenshot_path = os.path.join(goods_folder, screenshot_filename)
            screenshot.save(screenshot_path)
            self.log(f"✅ 价格OCR截图已保存: {screenshot_path}")

            # OCR识别
            img_array = np.array(screenshot)
            img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

            ocr = get_ocr_instance()
            results = ocr.ocr(img_bgr)

            ocr_results = []
            if results and len(results) > 0:
                page_result = results[0]
                self.log(f"价格OCR结果类型: {type(page_result)}")

                # 方式1：检查是否是字典格式（与test_ocr_quick.py一致）
                if isinstance(page_result, dict) and 'rec_texts' in page_result and 'rec_scores' in page_result:
                    texts = page_result['rec_texts']
                    scores = page_result['rec_scores']

                    self.log(f"字典格式价格OCR识别到 {len(texts)} 个文本")

                    for text, score in zip(texts, scores):
                        ocr_results.append({
                            'text': text,
                            'confidence': score
                        })
                        self.log(f"价格OCR识别: {text} (置信度: {score:.3f})")

                # 方式2：使用属性格式（与test_ocr_quick.py一致）
                elif hasattr(page_result, 'rec_texts') and hasattr(page_result, 'rec_scores'):
                    texts = page_result.rec_texts
                    scores = page_result.rec_scores

                    self.log(f"属性格式价格OCR识别到 {len(texts)} 个文本")

                    for text, score in zip(texts, scores):
                        ocr_results.append({
                            'text': text,
                            'confidence': score
                        })
                        self.log(f"价格OCR识别: {text} (置信度: {score:.3f})")

                else:
                    self.log("价格OCR结果格式不匹配，尝试备用解析...")
                    # 打印可用的属性或键
                    if isinstance(page_result, dict):
                        self.log(f"字典可用键: {list(page_result.keys())}")
                    elif hasattr(page_result, '__dict__'):
                        self.log(f"对象可用属性: {list(page_result.__dict__.keys())}")

                    # 备用解析方式（传统格式）
                    if hasattr(page_result, '__iter__'):
                        for line in page_result:
                            if line and len(line) >= 2:
                                text_info = line[1]
                                if len(text_info) >= 2:
                                    text = text_info[0]
                                    confidence = text_info[1]

                                    ocr_results.append({
                                        'text': text,
                                        'confidence': confidence
                                    })
                                    self.log(f"备用方式价格OCR识别: {text} (置信度: {confidence:.3f})")

            self.log(f"✅ 价格OCR识别完成，共识别到 {len(ocr_results)} 个文本")

            return ocr_results, screenshot_path

        except Exception as e:
            self.log(f"价格OCR识别失败: {e}")
            return [], None

    def extract_price_from_ocr_results(self, ocr_results):
        """从OCR结果中提取价格（优化版）"""
        import re
        try:
            # 定义价格识别优先级（按您的要求）
            price_priority_groups = [
                # 第一优先级组：券前、底价、特卖价
                [
                    (r'券前￥(\d+\.?\d*)', '券前价格'),
                    (r'底价￥(\d+\.?\d*)', '底价'),
                    (r'特卖价￥(\d+\.?\d*)', '特卖价'),
                ],
                # 第二优先级组：券后、特价
                [
                    (r'券后￥(\d+\.?\d*)', '券后价格'),
                    (r'特价￥(\d+\.?\d*)', '特价'),
                ],
                # 第三优先级组：通用价格
                [
                    (r'￥(\d+\.?\d*)', '通用价格'),
                ]
            ]

            # 首先检查是否售罄
            for result in ocr_results:
                text = result['text']
                confidence = result['confidence']

                if confidence > 0.5 and ("该款式售罄" in text or "售罄" in text):
                    return "售罄"

            # 按优先级组查找价格
            for priority_group in price_priority_groups:
                for result in ocr_results:
                    text = result['text']
                    confidence = result['confidence']

                    if confidence > 0.5:
                        for pattern, pattern_name in priority_group:
                            match = re.search(pattern, text)
                            if match:
                                price = match.group(1)
                                self.log(f"✅ 提取到{pattern_name}: {price} (来源: {text})")
                                return price

            return None

        except Exception as e:
            self.log(f"提取价格失败: {e}")
            return None

    def process_detail_images(self, folder_index):
        """
        处理详情页图片：只处理新的详情页图片，不触碰已处理的主图
        保留前30张详情页图片，重命名为1-30（主图使用"主图X"格式）

        Args:
            folder_index (int): 商品文件夹编号

        Returns:
            bool: 处理是否成功
        """
        try:
            folder_path = os.path.join(self.goods_folder, str(folder_index))
            if not os.path.exists(folder_path):
                self.log(f"商品文件夹不存在: {folder_path}")
                return False

            self.log(f"处理详情页图片文件夹: {folder_path}")

            # 获取所有图片文件，但排除已处理的主图
            main_image_names = ['主图1.jpg', '主图2.jpg', '主图3.jpg', '主图4.jpg']
            detail_image_files = []

            for file in os.listdir(folder_path):
                if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.gif')):
                    # 跳过已处理的主图
                    if file in main_image_names:
                        self.log(f"跳过已处理的主图: {file}")
                        continue

                    file_path = os.path.join(folder_path, file)
                    # 获取创建时间
                    create_time = os.path.getctime(file_path)
                    detail_image_files.append((file_path, create_time))

            if not detail_image_files:
                self.log("文件夹中没有新的详情页图片文件")
                return True

            # 按创建时间排序
            detail_image_files.sort(key=lambda x: x[1])
            self.log(f"找到 {len(detail_image_files)} 张详情页图片")

            # 删除第31张及以后的详情页图片
            if len(detail_image_files) > 30:
                for i in range(30, len(detail_image_files)):
                    file_path = detail_image_files[i][0]
                    try:
                        os.remove(file_path)
                        self.log(f"删除多余详情页图片: {os.path.basename(file_path)}")
                    except Exception as e:
                        self.log(f"删除图片失败 {file_path}: {str(e)}")

                # 更新列表，只保留前30张
                detail_image_files = detail_image_files[:30]

            # 重命名详情页图片为1-30（主图使用"主图X"格式，不冲突）
            for i, (file_path, _) in enumerate(detail_image_files, 1):  # 从1开始
                file_ext = os.path.splitext(file_path)[1]
                new_name = f"{i}{file_ext}"
                new_path = os.path.join(folder_path, new_name)

                try:
                    # 如果目标文件已存在，先删除
                    if os.path.exists(new_path):
                        os.remove(new_path)

                    os.rename(file_path, new_path)
                    self.log(f"重命名详情页图片: {os.path.basename(file_path)} → {new_name}")
                except Exception as e:
                    self.log(f"重命名失败 {file_path}: {str(e)}")

            self.log("✅ 详情页图片处理完成")
            self.log("✅ 主图保持'主图X'格式，详情页图片重命名为1-30")
            return True

        except Exception as e:
            self.log(f"处理详情页图片失败: {str(e)}")
            return False

    def scroll_down_gradually(self, x, y, scroll_count=40, duration=3.0):
        """
        在指定位置逐渐向下滚动

        Args:
            x (int): X坐标
            y (int): Y坐标
            scroll_count (int): 滚动次数
            duration (float): 总持续时间（秒）
        """
        try:
            self.log(f"在位置({x}, {y})逐渐向下滚动{scroll_count}次，持续{duration}秒")

            # 移动到指定位置
            pyautogui.moveTo(x, y)

            # 计算每次滚动的间隔
            interval = duration / scroll_count

            for i in range(scroll_count):
                pyautogui.scroll(-1)  # 向下滚动
                if i < scroll_count - 1:  # 最后一次不需要等待
                    time.sleep(interval)

            self.log("✅ 滚动完成")

        except Exception as e:
            self.log(f"滚动失败: {str(e)}")

    def capture_detail_images(self):
        """
        捕获详情页图片的完整流程

        Returns:
            bool: 捕获是否成功
        """
        try:
            self.log("=== 开始详情页图片捕获流程 ===")

            # 步骤13.1: 移动鼠标到(475,230)并点击右键
            self.log("步骤13.1: 移动到(475,230)并点击右键...")
            pyautogui.moveTo(475, 230)
            pyautogui.rightClick()
            time.sleep(1.5)

            # 步骤13.2: 使用AutoHotkey向下滚动40次
            self.log("步骤13.2: 使用AutoHotkey向下滚动40次...")
            if self.execute_autohotkey_script("scroll_down"):
                self.log("✅ AutoHotkey滚动执行成功")
            else:
                self.log("❌ AutoHotkey滚动失败，使用备用方法...")
                # 备用方法：使用Python滚动
                self.scroll_down_gradually(475, 230, scroll_count=40, duration=3.0)

            # 步骤13.3: 移动到(477,300)并点击左键，等待0.5秒
            self.log("步骤13.3: 移动到(477,300)并点击左键...")
            pyautogui.moveTo(477, 300)
            pyautogui.click()
            time.sleep(0.5)
            self.log("✅ 点击操作完成")

            # 步骤13.4: 移动到(480,495)并长按0.5秒
            self.log("步骤13.4: 移动到(480,495)并长按0.5秒...")
            pyautogui.moveTo(480, 495)
            pyautogui.mouseDown()
            time.sleep(0.5)
            pyautogui.mouseUp()
            self.log("✅ 长按操作完成")

            # 步骤13.5: 停顿1秒后查找保存按钮
            time.sleep(1.0)
            self.log("步骤13.5: 查找并点击保存按钮...")
            if not self.find_and_click_image_with_retry("BAOCUN1.png", delay=0.5, max_retries=3, retry_delay=5):
                self.log("错误：未找到保存按钮图片 BAOCUN1.png（已重试3次）")
                return False

            # 步骤13.6: 检测保存中图片
            self.log("步骤13.6: 检测详情页保存状态...")
            if self.wait_for_image("BAOCUNZHONG.png", max_attempts=80, check_interval=0.2):
                self.log("✅ 检测到详情页保存中状态，等待保存完成...")

                # 等待保存完成
                time.sleep(5.0)

                # 步骤13.7: 复制详情页图片
                self.log("步骤13.7: 复制详情页图片...")
                max_attempts = 3
                for attempt in range(1, max_attempts + 1):
                    self.log(f"第 {attempt}/{max_attempts} 次尝试复制详情页图片...")
                    if self.move_phone_images_to_product_folder():
                        self.log("✅ 详情页图片复制完成")

                        # 步骤13.8: 删除手机上的详情页图片
                        self.log("步骤13.8: 删除手机上的详情页图片...")
                        if self.delete_phone_files():
                            self.log("✅ 手机详情页文件删除完成")
                        else:
                            self.log("⚠️ 手机详情页文件删除失败，请手动删除")

                        # 步骤13.8: 处理详情页图片
                        self.log("步骤13.8: 处理详情页图片...")
                        if self.process_detail_images(self.current_product_index):
                            self.log("✅ 详情页图片处理完成")
                        else:
                            self.log("⚠️ 详情页图片处理失败")

                        # 步骤13.9: 执行最后的鼠标操作
                        self.log("步骤13.9: 执行最后的鼠标操作...")
                        self.perform_final_mouse_operations()

                        # 步骤13.10: 执行智能OCR分析
                        self.log("步骤13.10: 执行智能OCR分析...")
                        self.perform_smart_ocr_analysis(self.current_product_index)

                        return True
                    else:
                        if attempt < max_attempts:
                            self.log(f"第 {attempt} 次尝试失败，等待2秒后重试...")
                            time.sleep(2.0)
                        else:
                            self.log("❌ 所有尝试都失败，详情页图片复制失败")
                            return False
            else:
                self.log("❌ 未检测到详情页保存中状态，可能保存失败")
                return False

        except Exception as e:
            self.log(f"详情页图片捕获失败: {str(e)}")
            return False

    def perform_final_mouse_operations(self):
        """
        执行最后的鼠标操作序列
        """
        try:
            self.log("开始执行最后的鼠标操作序列...")

            # 步骤1: 移动到(475,125)，等待0.5秒，点击右键
            self.log("移动到位置(475,125)...")
            pyautogui.moveTo(475, 125)
            time.sleep(0.5)

            self.log("点击右键...")
            pyautogui.rightClick()

            # 步骤2: 等待1秒
            self.log("等待1秒...")
            time.sleep(1.0)

            # 步骤3: 移动到(670,940)，等待0.2秒，点击左键
            self.log("移动到位置(670,940)...")
            pyautogui.moveTo(670, 940)
            time.sleep(0.2)

            self.log("点击左键...")
            pyautogui.click()

            # 等待页面加载完成
            self.log("等待2.5秒让页面加载完成...")
            time.sleep(2.5)

            self.log("✅ 最后的鼠标操作序列完成")

        except Exception as e:
            self.log(f"执行最后鼠标操作失败: {str(e)}")

    def capture_and_ocr_screen(self, x1, y1, x2, y2, product_index):
        """
        截取指定区域的屏幕并进行OCR识别，同时保存截图

        Args:
            x1, y1: 左上角坐标
            x2, y2: 右下角坐标
            product_index: 商品编号

        Returns:
            tuple: (OCR识别结果列表, 截图保存路径)
        """
        try:
            self.log(f"开始OCR识别屏幕区域: ({x1}, {y1}) 到 ({x2}, {y2})")

            # 截取屏幕指定区域
            screenshot = pyautogui.screenshot(region=(x1, y1, x2-x1, y2-y1))

            # 保存OCR截图到商品图片文件夹（直接保存在商品图片目录下）
            screenshot_filename = f"{product_index}.jpg"
            screenshot_path = os.path.join(self.goods_folder, screenshot_filename)

            # 确保目录存在
            os.makedirs(os.path.dirname(screenshot_path), exist_ok=True)

            # 保存截图
            screenshot.save(screenshot_path)
            self.log(f"✅ OCR截图已保存: {screenshot_path}")

            # 转换为numpy数组
            img_array = np.array(screenshot)

            # 转换颜色格式 (RGB -> BGR)
            img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

            # 获取OCR实例
            ocr = get_ocr_instance()

            # 执行OCR识别
            self.log("正在执行OCR识别...")
            results = ocr.ocr(img_bgr)

            # 调试：打印OCR原始结果
            self.log(f"OCR原始结果类型: {type(results)}")
            self.log(f"OCR原始结果长度: {len(results) if results else 'None'}")

            # 处理OCR结果
            ocr_texts = []
            if results and len(results) > 0:
                # 获取第一页结果
                page_result = results[0]
                self.log(f"第一页结果类型: {type(page_result)}")

                if page_result:
                    self.log(f"识别到 {len(page_result)} 个文本行")

                    for i, line in enumerate(page_result):
                        if line and len(line) >= 2:
                            box_points = line[0]  # 边界框坐标点
                            text_info = line[1]   # 文本信息

                            if len(text_info) >= 2:
                                text = text_info[0]      # 提取文本内容
                                confidence = text_info[1] # 提取置信度

                                # 只保留置信度较高的结果
                                if confidence > 0.3:  # 降低阈值以获取更多结果
                                    # 安全地处理边界框
                                    bbox = None
                                    try:
                                        bbox = box_points
                                    except Exception as e:
                                        self.log(f"处理边界框失败: {e}")

                                    ocr_texts.append({
                                        'text': text,
                                        'confidence': confidence,
                                        'bbox': bbox  # 边界框坐标
                                    })
                                    self.log(f"OCR识别: {text} (置信度: {confidence:.4f})")
                                else:
                                    self.log(f"跳过低置信度文本: '{text}' (置信度: {confidence:.4f})")
                else:
                    self.log("第一页结果为空")

            if ocr_texts:
                self.log(f"✅ OCR识别完成，共识别到 {len(ocr_texts)} 个文本")
            else:
                self.log("⚠️ OCR未识别到任何文本")

            return ocr_texts, screenshot_path

        except Exception as e:
            self.log(f"OCR识别失败: {str(e)}")
            return [], None

    def save_ocr_results(self, ocr_results, product_index):
        """
        保存OCR识别结果到OCR文件夹

        Args:
            ocr_results: OCR识别结果列表
            product_index: 商品编号
        """
        try:
            # 创建OCR文件夹
            ocr_folder = "OCR"
            if not os.path.exists(ocr_folder):
                os.makedirs(ocr_folder)
                self.log(f"创建OCR文件夹: {ocr_folder}")

            # 创建OCR结果文件名
            ocr_filename = f"商品{product_index}_OCR结果.txt"
            ocr_filepath = os.path.join(ocr_folder, ocr_filename)

            # 保存OCR结果
            with open(ocr_filepath, 'w', encoding='utf-8') as f:
                f.write(f"商品 {product_index} OCR识别结果\n")
                f.write(f"识别时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"识别区域: (265, 282) 到 (691, 883)\n")
                f.write("=" * 50 + "\n\n")

                if ocr_results:
                    f.write(f"共识别到 {len(ocr_results)} 个文本:\n\n")
                    for i, result in enumerate(ocr_results, 1):
                        f.write(f"{i}. {result['text']}\n")
                        f.write(f"   置信度: {result['confidence']:.4f}\n")
                        f.write(f"   边界框: {result['bbox']}\n\n")
                else:
                    f.write("未识别到任何文本\n")

            self.log(f"✅ OCR结果已保存: {ocr_filepath}")
            return ocr_filepath

        except Exception as e:
            self.log(f"保存OCR结果失败: {str(e)}")
            return None

    def create_or_update_sku_excel(self, ocr_results, product_index):
        """
        创建或更新商品SKU信息Excel文件

        Args:
            ocr_results: OCR识别结果列表
            product_index: 商品编号
        """
        try:
            # Excel文件路径
            excel_filename = "商品SKU信息.xlsx"
            excel_filepath = os.path.join(self.goods_folder, excel_filename)

            # 准备数据
            if ocr_results:
                # 将OCR结果合并为一个字符串
                ocr_text = "\n".join([result['text'] for result in ocr_results])
                confidence_avg = sum([result['confidence'] for result in ocr_results]) / len(ocr_results)
            else:
                ocr_text = "未识别到文本"
                confidence_avg = 0.0

            # 创建新的数据行
            new_data = {
                '商品编号': product_index,
                'OCR识别文本': ocr_text,
                '平均置信度': f"{confidence_avg:.4f}",
                '识别时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                '识别区域': "(265, 282) 到 (691, 883)"
            }

            # 检查Excel文件是否存在
            if os.path.exists(excel_filepath):
                # 读取现有数据
                df = pd.read_excel(excel_filepath)
                self.log(f"读取现有Excel文件，当前有 {len(df)} 行数据")
            else:
                # 创建新的DataFrame
                df = pd.DataFrame()
                self.log("创建新的Excel文件")

            # 添加新数据（在适当的位置插入空行）
            if not df.empty:
                # 在现有数据后添加几个空行，然后添加新数据
                empty_rows = pd.DataFrame([{col: '' for col in df.columns} for _ in range(3)])
                df = pd.concat([df, empty_rows], ignore_index=True)

            # 添加新的商品数据
            new_row = pd.DataFrame([new_data])
            df = pd.concat([df, new_row], ignore_index=True)

            # 保存Excel文件
            df.to_excel(excel_filepath, index=False)
            self.log(f"✅ 商品SKU信息已保存到Excel: {excel_filepath}")

            return excel_filepath

        except Exception as e:
            self.log(f"保存商品SKU信息失败: {str(e)}")
            return None

    def perform_ocr_analysis(self):
        """
        执行OCR分析的主要方法
        在指定区域(270,460)到(693,883)进行OCR识别
        """
        try:
            self.log("=== 开始OCR分析 ===")

            # 定义OCR识别区域
            x1, y1 = 270, 230
            x2, y2 = 691, 883

            # 执行OCR识别并保存截图
            ocr_results, screenshot_path = self.capture_and_ocr_screen(x1, y1, x2, y2, self.current_product_index)

            if ocr_results:
                self.log("OCR识别结果:")
                for i, result in enumerate(ocr_results, 1):
                    self.log(f"  {i}. {result['text']} (置信度: {result['confidence']:.2f})")

                # 保存OCR文字结果到OCR文件夹
                ocr_file_path = self.save_ocr_results(ocr_results, self.current_product_index)

                # 创建或更新商品SKU信息Excel文件（为将来功能做准备）
                excel_path = self.create_or_update_sku_excel(ocr_results, self.current_product_index)

                self.log("✅ OCR分析完成，所有文件已保存")
                return ocr_results
            else:
                self.log("❌ OCR识别失败或未找到文本")

                # 即使没有识别到文本，也保存空结果
                self.save_ocr_results([], self.current_product_index)
                self.create_or_update_sku_excel([], self.current_product_index)

                return []

        except Exception as e:
            self.log(f"OCR分析失败: {str(e)}")
            return []

    def verify_and_update_coordinates(self, x1, y1, x2, y2, original_coordinates):
        """
        校验并更新颜色坐标（点击第一个颜色后执行）
        只对比已知颜色的坐标变化，不添加新颜色

        Args:
            x1, y1, x2, y2: OCR区域坐标
            original_coordinates: 第一次OCR的原始坐标字典

        Returns:
            dict or None: 更新后的坐标字典（只包含原有颜色），如果无变化则返回None
        """
        try:
            self.log("🔍 执行坐标校验OCR识别...")

            # 执行与第一次完全相同的OCR识别流程
            ocr_results, _ = self.ocr_analyzer.capture_and_ocr_screen(
                x1, y1, x2, y2, "coordinate_verify"
            )

            if not ocr_results:
                self.log("⚠️ 坐标校验OCR识别失败")
                return None

            # 使用与第一次完全相同的智能分析流程
            analysis_result = self.ocr_analyzer.analyze_product_info(ocr_results)
            new_all_coordinates = analysis_result.get('color_coordinates', {})

            if not new_all_coordinates:
                self.log("⚠️ 坐标校验未识别到颜色坐标")
                return None

            # 只处理原有的颜色，不添加新颜色
            updated_coordinates = {}
            coordinate_changed = False
            coordinate_threshold = 15  # 坐标变化阈值（像素）

            self.log("📊 坐标对比结果（仅对比原有颜色）:")
            for color_name in original_coordinates:
                orig_coord = original_coordinates[color_name]

                if color_name in new_all_coordinates:
                    new_coord = new_all_coordinates[color_name]

                    # 计算坐标差异
                    x_diff = abs(orig_coord[0] - new_coord[0])
                    y_diff = abs(orig_coord[1] - new_coord[1])

                    self.log(f"   {color_name}: {orig_coord} → {new_coord} (差异: X±{x_diff}, Y±{y_diff})")

                    # 判断是否超过阈值
                    if x_diff > coordinate_threshold or y_diff > coordinate_threshold:
                        coordinate_changed = True
                        updated_coordinates[color_name] = new_coord
                        self.log(f"   🔄 {color_name} 坐标变化超过阈值({coordinate_threshold}px)，将更新")
                    else:
                        updated_coordinates[color_name] = orig_coord
                        self.log(f"   ✅ {color_name} 坐标无显著变化")
                else:
                    # 如果新OCR中找不到原有颜色，保持原坐标
                    updated_coordinates[color_name] = orig_coord
                    self.log(f"   ⚠️ {color_name}: 新OCR中未找到，保持原坐标")

            # 检查是否有新识别的颜色（仅用于日志提示，不添加到结果中）
            new_colors = set(new_all_coordinates.keys()) - set(original_coordinates.keys())
            if new_colors:
                self.log(f"🚫 发现新颜色但不添加: {list(new_colors)}")

            if coordinate_changed:
                self.log("🔄 检测到坐标变化，将使用更新后的坐标")
                return updated_coordinates
            else:
                self.log("✅ 所有颜色坐标无显著变化，继续使用原始坐标")
                return None

        except Exception as e:
            self.log(f"坐标校验失败: {e}")
            return None

class AutoCaptureApp:
    """自动化数据捕获应用主界面"""

    def __init__(self, root):
        """
        初始化应用界面

        Args:
            root: tkinter根窗口
        """
        self.root = root
        self.root.title("自动化数据捕获软件 v2.0")
        self.root.geometry("545x420+1320+480")  # 设置窗口大小和位置：宽x高+左上角x+左上角y
        self.root.resizable(True, True)

        # 设置现代化配色
        self.root.configure(bg='#f0f0f0')

        # 数据存储
        self.excel_reader = None
        self.links = []
        self.product_vars = []  # 商品选择变量列表

        # 自动化控制器
        self.automation_controller = AutomationController(log_callback=self.log_message)

        # 暂停控制器
        self.pause_controller = PauseController(log_callback=self.log_message)

        # 设置自动化控制器的暂停控制器引用
        self.automation_controller.pause_controller = self.pause_controller

        # 创建界面
        self.create_widgets()

        # 设置F8全局热键（在界面创建后）
        self.setup_global_hotkey()

        # 检测MTP设备（在界面创建后）
        self.detect_and_update_device_status()

    def create_widgets(self):
        """创建紧凑型界面组件"""
        # 主框架（减少内边距）
        main_frame = tk.Frame(self.root, bg='#f0f0f0', padx=10, pady=8)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 顶部框架（标题和导入按钮在同一行）
        top_frame = tk.Frame(main_frame, bg='#f0f0f0')
        top_frame.pack(fill=tk.X, pady=(0, 8))

        # 标题（更小字体，左侧）
        title_label = tk.Label(top_frame, text="数据捕获 v2.0",
                              font=("Microsoft YaHei", 10, "bold"),
                              bg='#f0f0f0', fg='#2c3e50')
        title_label.pack(side=tk.LEFT)

        # 导入链接按钮（右侧，更小）
        self.import_btn = tk.Button(top_frame, text="导入链接",
                                   command=self.import_links,
                                   font=("Microsoft YaHei", 9),
                                   bg='#3498db', fg='white',
                                   relief='flat', padx=12, pady=3,
                                   cursor='hand2')
        self.import_btn.pack(side=tk.RIGHT)

        # 设备状态标签（导入按钮左边）
        self.device_status_label = tk.Label(top_frame, text="检测设备中...",
                                           font=("Microsoft YaHei", 8),
                                           bg='#f0f0f0', fg='#666666')
        self.device_status_label.pack(side=tk.RIGHT, padx=(0, 10))

        # 状态标签（更小字体，居中）
        self.status_label = tk.Label(main_frame, text="请选择Excel文件",
                                    font=("Microsoft YaHei", 8),
                                    bg='#f0f0f0', fg='#7f8c8d')
        self.status_label.pack(pady=(2, 0))

        # 商品选择框架（缩小高度）
        self.product_frame = tk.Frame(main_frame, bg='#ffffff', relief='solid', bd=1, height=120)
        self.product_frame.pack(fill=tk.X, pady=(8, 0))
        self.product_frame.pack_propagate(False)  # 保持固定高度

        # 商品选择标题（缩小字体）
        self.product_title = tk.Label(self.product_frame, text="选择商品",
                                     font=("Microsoft YaHei", 9, "bold"),
                                     bg='#ffffff', fg='#2c3e50')

        # 商品列表容器（使用滚动框）
        self.product_canvas = tk.Canvas(self.product_frame, bg='#ffffff', highlightthickness=0)
        self.product_scrollbar = tk.Scrollbar(self.product_frame, orient="vertical", command=self.product_canvas.yview)
        self.product_list_frame = tk.Frame(self.product_canvas, bg='#ffffff')

        self.product_list_frame.bind(
            "<Configure>",
            lambda e: self.product_canvas.configure(scrollregion=self.product_canvas.bbox("all"))
        )

        self.product_canvas.create_window((0, 0), window=self.product_list_frame, anchor="nw")
        self.product_canvas.configure(yscrollcommand=self.product_scrollbar.set)

        # 执行选中商品按钮（统一蓝色）
        self.execute_selected_btn = tk.Button(self.product_frame, text="执行选中",
                                            command=self.execute_selected_products,
                                            font=("Microsoft YaHei", 8),
                                            bg='#3498db', fg='white',
                                            relief='flat', padx=10, pady=2,
                                            cursor='hand2', state=tk.DISABLED)

        # 空白提示标签（缩小）
        self.empty_label = tk.Label(self.product_frame, text="导入Excel后显示商品选择",
                                   font=("Microsoft YaHei", 8),
                                   bg='#ffffff', fg='#bdc3c7')
        self.empty_label.pack(expand=True)

        # 开始操作按钮（更小）
        operation_frame = tk.Frame(main_frame, bg='#f0f0f0')
        operation_frame.pack(pady=(8, 5))

        self.start_btn = tk.Button(operation_frame, text="开始全部操作",
                                  command=self.start_automation,
                                  font=("Microsoft YaHei", 9),
                                  bg='#3498db', fg='white',
                                  relief='flat', padx=15, pady=4,
                                  cursor='hand2', state=tk.DISABLED)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 暂停/继续按钮
        self.pause_btn = tk.Button(operation_frame, text="暂停运行",
                                  command=self.toggle_pause,
                                  font=("Microsoft YaHei", 9),
                                  bg='#dc3545', fg='white',
                                  relief='flat', padx=15, pady=4,
                                  cursor='hand2', state=tk.DISABLED)
        self.pause_btn.pack(side=tk.LEFT)

        # 日志标题行（日志标题和状态标签在同一行）
        log_title_frame = tk.Frame(main_frame, bg='#f0f0f0')
        log_title_frame.pack(fill=tk.X, pady=(8, 3))

        # 日志标题（左侧）
        log_label = tk.Label(log_title_frame, text="日志",
                            font=("Microsoft YaHei", 9, "bold"),
                            bg='#f0f0f0', fg='#2c3e50')
        log_label.pack(side=tk.LEFT)

        # 操作状态标签（右侧，与日志标题同一水平线）
        self.operation_status_label = tk.Label(log_title_frame, text="请先导入",
                                              font=("Microsoft YaHei", 8),
                                              bg='#f0f0f0', fg='#7f8c8d')
        self.operation_status_label.pack(side=tk.RIGHT)

        # 创建日志文本框（进一步缩小高度：从13改为8）
        log_frame = tk.Frame(main_frame, bg='#ffffff', relief='solid', bd=1)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 8))

        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=50,
                                                 font=("Microsoft YaHei", 8),
                                                 bg='#ffffff', fg='#2c3e50',
                                                 relief='flat', padx=5, pady=4)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=1, pady=1)

        # 底部按钮框架（更小）
        button_frame = tk.Frame(main_frame, bg='#f0f0f0')
        button_frame.pack(fill=tk.X, pady=(5, 0))

        # 清空日志按钮（更小）
        clear_btn = tk.Button(button_frame, text="清空",
                             command=self.clear_log,
                             font=("Microsoft YaHei", 8),
                             bg='#e74c3c', fg='white',
                             relief='flat', padx=8, pady=2,
                             cursor='hand2')
        clear_btn.pack(side=tk.LEFT)

        # 退出按钮（更小）
        exit_btn = tk.Button(button_frame, text="退出",
                            command=self.root.quit,
                            font=("Microsoft YaHei", 8),
                            bg='#95a5a6', fg='white',
                            relief='flat', padx=8, pady=2,
                            cursor='hand2')
        exit_btn.pack(side=tk.RIGHT)

        # 初始日志
        self.log_message("🎉 应用启动成功，等待导入Excel文件...")

    def create_product_selection(self):
        """创建商品选择列表"""
        # 清空现有的商品选择
        for widget in self.product_list_frame.winfo_children():
            widget.destroy()
        self.product_vars.clear()

        if not self.links:
            return

        # 隐藏空白提示标签
        self.empty_label.pack_forget()

        # 显示商品选择标题（减少间距）
        self.product_title.pack(pady=(5, 3))

        # 显示滚动框和商品列表（减少右边距以缩短空白）
        self.product_canvas.pack(side="left", fill="both", expand=True, padx=(5, 0), pady=(0, 5))
        self.product_scrollbar.pack(side="right", fill="y", pady=(0, 5), padx=(0, 0))

        # 创建商品选择项（横向排列，每行4个）
        current_row_frame = None
        for i, link in enumerate(self.links, 1):
            # 每4个商品创建一个新行（减少右边距）
            if (i - 1) % 4 == 0:
                current_row_frame = tk.Frame(self.product_list_frame, bg='#ffffff')
                current_row_frame.pack(fill=tk.X, pady=2, padx=(5, 0))

            # 创建选择变量
            var = tk.BooleanVar(value=True)  # 默认全选
            self.product_vars.append(var)

            # 创建每个商品的框架（精确间距）
            product_item_frame = tk.Frame(current_row_frame, bg='#ffffff')
            product_item_frame.pack(side=tk.LEFT, padx=3, pady=1)

            # 创建自定义样式的选择框（去除阴影）
            checkbox = tk.Checkbutton(product_item_frame,
                                    text=f"商品{i}",
                                    variable=var,
                                    font=("Microsoft YaHei", 9, "bold"),
                                    bg='#ffffff', fg='#2c3e50',
                                    selectcolor='#3498db',
                                    indicatoron=0,  # 关闭默认指示器，使用按钮样式
                                    relief='flat',  # 去除边框阴影
                                    borderwidth=0,  # 去除边框
                                    padx=4, pady=2,
                                    highlightthickness=0,  # 去除焦点边框
                                    command=lambda v=var, cb=None: self.update_checkbox_style(v, cb))

            # 设置初始样式（选中状态为蓝色）
            self.update_checkbox_style(var, checkbox)
            checkbox.pack()

            # 绑定点击后的样式更新
            def on_click(cb=checkbox, v=var):
                # 延迟更新样式，确保变量值已更新
                checkbox.after(10, lambda: self.update_checkbox_style(v, cb))
                self.update_execute_button_state()

            checkbox.config(command=on_click)

        # 显示执行按钮（绿底白字，精确定位）
        self.execute_selected_btn.config(bg='#27ae60', fg='white',
                                        font=("Microsoft YaHei", 8),
                                        padx=8, pady=2)  # 缩小按钮尺寸
        self.execute_selected_btn.pack(side=tk.RIGHT, padx=(0, 18), pady=(0, 5))  # 精确右边距
        self.execute_selected_btn.config(state=tk.NORMAL)

        # 更新滚动区域
        self.product_list_frame.update_idletasks()
        self.product_canvas.configure(scrollregion=self.product_canvas.bbox("all"))

        self.log_message(f"📦 已加载 {len(self.links)} 个商品，可选择性执行")

    def update_checkbox_style(self, var, checkbox):
        """更新复选框样式：选中=蓝底白字，未选中=白底黑字（无阴影）"""
        if checkbox is None:
            return

        try:
            if var.get():
                # 选中状态：蓝色背景，白色文字，无边框
                checkbox.config(
                    bg='#3498db',
                    fg='white',
                    activebackground='#2980b9',
                    activeforeground='white',
                    relief='flat',
                    borderwidth=0,
                    highlightthickness=0
                )
            else:
                # 未选中状态：白色背景，黑色文字，无边框
                checkbox.config(
                    bg='white',
                    fg='#2c3e50',
                    activebackground='#ecf0f1',
                    activeforeground='#2c3e50',
                    relief='flat',
                    borderwidth=0,
                    highlightthickness=0
                )
        except Exception as e:
            # 忽略样式更新错误
            pass

    def update_execute_button_state(self):
        """更新执行按钮状态"""
        # 检查是否有选中的商品
        selected_count = sum(1 for var in self.product_vars if var.get())

        if selected_count > 0:
            self.execute_selected_btn.config(state=tk.NORMAL)
            self.execute_selected_btn.config(text=f"执行选中 ({selected_count})")
        else:
            self.execute_selected_btn.config(state=tk.DISABLED)
            self.execute_selected_btn.config(text="执行选中")

    def execute_selected_products(self):
        """执行选中的商品"""
        # 获取选中的商品索引
        selected_indices = []
        for i, var in enumerate(self.product_vars):
            if var.get():
                selected_indices.append(i + 1)  # 商品编号从1开始

        if not selected_indices:
            self.log_message("⚠️ 请至少选择一个商品")
            return

        self.log_message(f"🚀 开始执行选中的商品: {selected_indices}")
        self.log_message("请确保投屏软件已打开并处于可操作状态")
        self.log_message("3秒后开始执行操作...")

        # 禁用按钮防止重复点击
        self.execute_selected_btn.config(state=tk.DISABLED)
        self.start_btn.config(state=tk.DISABLED)

        # 给用户3秒准备时间
        for i in range(3, 0, -1):
            self.log_message(f"倒计时: {i} 秒")
            time.sleep(1)

        # 在后台线程中执行选中的商品
        threading.Thread(target=self.execute_selected_thread, args=(selected_indices,), daemon=True).start()

    def execute_selected_thread(self, selected_indices):
        """在后台线程中执行选中的商品"""
        try:
            # 设置链接列表供AutomationController使用
            self.automation_controller.links = self.links

            # 执行设备品牌检测（与全部操作保持一致）
            self.log_message("📱 检测连接设备品牌...")
            device_brand = self.automation_controller.detect_device_brand()
            if device_brand is None:
                self.log_message("⚠️ 警告：无法检测设备品牌，将使用默认图片文件夹")
                self.automation_controller.image_folder = "image"
                # 如果检测失败，尝试重新检测设备名称用于文件复制
                if not self.automation_controller.current_device_name:
                    self.automation_controller.current_device_name = self.automation_controller.mtp_manager.detect_connected_mtp_device()
                    if self.automation_controller.current_device_name:
                        self.log_message(f"📱 检测到设备名称: {self.automation_controller.current_device_name}")
            else:
                self.log_message(f"✅ 设备品牌检测完成，当前图片文件夹: {self.automation_controller.image_folder}")
                self.log_message(f"📱 当前设备: {self.automation_controller.current_device_name}")

            success_count = 0
            for i, product_index in enumerate(selected_indices):
                self.log_message(f"\n{'='*50}")
                self.log_message(f"📍 开始处理商品 {product_index} ({i+1}/{len(selected_indices)})")
                self.log_message(f"商品链接: {self.links[product_index-1]}")
                self.log_message(f"{'='*50}")

                # 设置当前商品索引
                self.automation_controller.current_product_index = product_index

                # 处理单个商品
                link = self.links[product_index - 1]
                if self.automation_controller.process_single_product(link, product_index):
                    success_count += 1
                    self.log_message(f"✅ 商品 {product_index} 处理完成")
                else:
                    self.log_message(f"❌ 商品 {product_index} 处理失败")

                # 如果不是最后一个商品，执行返回操作准备下一个商品
                if i < len(selected_indices) - 1:
                    self.log_message(f"🔄 准备处理下一个商品...")
                    if not self.automation_controller.return_to_search_page():
                        self.log_message("❌ 返回搜索页面失败")
                        break

            self.log_message(f"\n🎉 选中商品处理完成！成功: {success_count}/{len(selected_indices)}")

        except Exception as e:
            self.log_message(f"❌ 执行过程中发生错误: {str(e)}")
            import traceback
            self.log_message(f"错误详情: {traceback.format_exc()}")

        finally:
            # 重新启用按钮
            self.execute_selected_btn.config(state=tk.NORMAL)
            self.start_btn.config(state=tk.NORMAL)
            self.update_execute_button_state()

    def log_message(self, message):
        """
        在日志框中添加消息

        Args:
            message (str): 要添加的消息
        """
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)  # 自动滚动到底部
        self.root.update()  # 立即更新界面

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("日志已清空")

    def import_links(self):
        """导入链接功能"""
        try:
            # 打开文件选择对话框
            file_path = filedialog.askopenfilename(
                title="选择Excel文件",
                filetypes=[
                    ("Excel文件", "*.xlsx *.xls"),
                    ("所有文件", "*.*")
                ],
                initialdir=os.getcwd()
            )

            if not file_path:
                self.log_message("用户取消了文件选择")
                return

            self.log_message(f"正在读取文件: {os.path.basename(file_path)}")

            # 在后台线程中处理文件读取
            threading.Thread(target=self.process_excel_file, args=(file_path,), daemon=True).start()

        except Exception as e:
            error_msg = f"导入链接时出错: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("错误", error_msg)

    def process_excel_file(self, file_path):
        """
        在后台线程中处理Excel文件

        Args:
            file_path (str): Excel文件路径
        """
        try:
            # 创建Excel读取器
            self.excel_reader = ExcelReader(file_path)

            # 读取Excel文件
            if self.excel_reader.read_excel():
                self.log_message("Excel文件读取成功")

                # 获取链接
                self.links = self.excel_reader.get_links()

                if self.links:
                    self.log_message(f"📊 已读取{len(self.links)}个商品链接")
                    self.status_label.config(text=f"✅ 已导入 {len(self.links)} 个商品链接")

                    # 复制Excel文件到商品图片文件夹
                    self.copy_excel_to_product_folder(file_path)

                    # 创建商品文件夹
                    self.log_message(f"📁 正在创建 {len(self.links)} 个商品文件夹...")
                    created_folders = self.excel_reader.create_product_folders(len(self.links))
                    if created_folders:
                        self.log_message(f"✅ 成功创建商品文件夹: 1-{len(self.links)}")
                    else:
                        self.log_message("❌ 创建商品文件夹失败")

                    # 创建商品选择列表
                    self.root.after(0, self.create_product_selection)

                    # 启用开始操作按钮
                    self.start_btn.config(state=tk.NORMAL)
                    self.operation_status_label.config(text="✅ 准备就绪，可以开始操作")

                    # 显示前几个链接作为预览
                    self.log_message("🔗 链接预览:")
                    for i, link in enumerate(self.links[:3], 1):
                        self.log_message(f"  {i}. {link}")

                    if len(self.links) > 3:
                        self.log_message(f"  ... 还有 {len(self.links) - 3} 个链接")
                else:
                    self.log_message("未在Excel文件中找到任何链接")
                    self.status_label.config(text="未找到商品链接")
                    self.start_btn.config(state=tk.DISABLED)
                    self.operation_status_label.config(text="未找到商品链接")
            else:
                self.log_message("Excel文件读取失败")
                self.status_label.config(text="文件读取失败")

        except Exception as e:
            error_msg = f"处理Excel文件时出错: {str(e)}"
            self.log_message(error_msg)
            self.status_label.config(text="处理失败")

    def copy_excel_to_product_folder(self, source_file_path):
        """
        复制Excel文件到商品图片文件夹

        Args:
            source_file_path (str): 源Excel文件路径
        """
        try:
            # 确保商品图片文件夹存在
            goods_folder = "商品图片"
            if not os.path.exists(goods_folder):
                os.makedirs(goods_folder)

            # 目标文件路径
            target_file_path = os.path.join(goods_folder, "商品SKU信息.xlsx")

            # 复制文件
            shutil.copy2(source_file_path, target_file_path)

            self.log_message(f"✅ Excel文件已复制到: {target_file_path}")

        except Exception as e:
            self.log_message(f"❌ 复制Excel文件失败: {e}")

    def toggle_pause(self):
        """切换暂停/继续状态"""
        try:
            if self.pause_controller.is_paused:
                # 当前是暂停状态，点击继续
                if self.pause_controller.resume():
                    self.update_pause_button()
                    self.log_message("🟢 继续执行请求已发出")
            else:
                # 当前是运行状态，点击暂停
                if self.pause_controller.pause():
                    self.update_pause_button()
                    self.log_message("🔴 暂停请求已发出")
        except Exception as e:
            self.log_message(f"❌ 切换暂停状态失败: {e}")

    def update_pause_button(self):
        """更新暂停按钮的显示状态"""
        try:
            text = self.pause_controller.get_status_text()
            color = self.pause_controller.get_button_color()
            self.pause_btn.config(text=text, bg=color)
        except Exception as e:
            self.log_message(f"❌ 更新暂停按钮失败: {e}")

    def enable_pause_button(self):
        """启用暂停按钮"""
        self.pause_btn.config(state=tk.NORMAL)

    def disable_pause_button(self):
        """禁用暂停按钮"""
        self.pause_btn.config(state=tk.DISABLED)

    def setup_global_hotkey(self):
        """设置F8全局热键"""
        try:
            # 注册F8热键
            keyboard.add_hotkey('f8', self.on_f8_pressed)
            self.log_message("✅ F8全局热键已注册")
        except Exception as e:
            self.log_message(f"❌ 注册F8热键失败: {e}")

    def on_f8_pressed(self):
        """F8热键按下事件处理"""
        try:
            # 只有在自动化运行时才响应F8
            if self.pause_btn['state'] == 'normal':
                self.toggle_pause()
                self.log_message("🎹 F8热键触发暂停/继续切换")
        except Exception as e:
            self.log_message(f"❌ F8热键处理失败: {e}")

    def detect_and_update_device_status(self):
        """检测MTP设备并更新状态显示"""
        def detect_device():
            try:
                # 使用MTP管理器检测设备
                device_name = self.automation_controller.mtp_manager.detect_connected_mtp_device()

                # 更新UI显示（需要在主线程中执行）
                self.root.after(0, self.update_device_status_display, device_name)

            except Exception as e:
                self.log_message(f"❌ 检测MTP设备失败: {e}")
                self.root.after(0, self.update_device_status_display, None)

        # 在后台线程中执行设备检测
        threading.Thread(target=detect_device, daemon=True).start()

    def update_device_status_display(self, device_name):
        """更新设备状态显示标签"""
        try:
            if device_name:
                status_text = f"已连接设备MTP: {device_name}"
                text_color = "#28a745"  # 绿色
                self.log_message(f"✅ 检测到MTP设备: {device_name}")
            else:
                status_text = "未连接设备MTP"
                text_color = "#dc3545"  # 红色
                self.log_message("⚠️ 未检测到MTP设备")

            self.device_status_label.config(text=status_text, fg=text_color)

        except Exception as e:
            self.log_message(f"❌ 更新设备状态显示失败: {e}")

    def start_automation(self):
        """开始自动化操作"""
        try:
            if not self.links:
                self.log_message("错误：没有可用的商品链接，请先导入Excel文件")
                return

            # 禁用开始按钮，启用暂停按钮
            self.start_btn.config(state=tk.DISABLED)
            self.enable_pause_button()
            self.operation_status_label.config(text="正在执行自动化操作...")

            self.log_message("=== 准备开始自动化操作 ===")
            self.log_message("请确保投屏软件已打开并处于可操作状态")
            self.log_message("5秒后开始执行操作...")

            # 给用户5秒准备时间
            for i in range(5, 0, -1):
                self.log_message(f"倒计时: {i} 秒")
                time.sleep(1)

            # 在后台线程中执行自动化操作
            threading.Thread(target=self.execute_automation_thread, daemon=True).start()

        except Exception as e:
            error_msg = f"启动自动化操作时出错: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("错误", error_msg)
            self.start_btn.config(state=tk.NORMAL)
            self.operation_status_label.config(text="操作失败")

    def execute_automation_thread(self):
        """在后台线程中执行自动化操作"""
        try:
            # 执行自动化序列
            success = self.automation_controller.execute_automation_sequence(self.links)

            if success:
                self.operation_status_label.config(text="自动化操作完成")
                self.log_message("✅ 自动化操作成功完成！")
            else:
                self.operation_status_label.config(text="自动化操作失败")
                self.log_message("❌ 自动化操作失败，请检查日志")

            # 重新启用开始按钮，禁用暂停按钮
            self.start_btn.config(state=tk.NORMAL)
            self.disable_pause_button()

        except Exception as e:
            error_msg = f"执行自动化操作时出错: {str(e)}"
            self.log_message(error_msg)
            self.operation_status_label.config(text="操作异常")
            self.start_btn.config(state=tk.NORMAL)
            self.disable_pause_button()

def main():
    """主函数"""
    # 创建主窗口
    root = tk.Tk()

    # 创建应用
    app = AutoCaptureApp(root)

    # 运行应用
    root.mainloop()

if __name__ == "__main__":
    main()
